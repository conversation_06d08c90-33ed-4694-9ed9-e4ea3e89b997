.device-management {
  padding: 20px;
}

.device-sub-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
}

:deep(.el-tabs__header) {
  margin: 0;
  background-color: #f8f9fa;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-weight: 500;
  color: #666;
}

:deep(.el-tabs__item.is-active) {
  color: #667eea;
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  background-color: #667eea;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

.device-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 10px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #6fcf97 0%, #6fcf97 100%);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #f25b5b 0%, #f25b5b 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f2c94c 0%, #f2c94c 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #999;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
}

.device-toolbar {
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.modern-search {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
  max-width: 400px;
}

.modern-search .fas.fa-search {
  position: absolute;
  left: 12px;
  color: #999;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.toolbar-buttons {
  display: flex;
  gap: 10px;
}

.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
}

.modern-btn.small {
  padding: 6px 12px;
  font-size: 12px;
}

.modern-btn.sm {
  padding: 4px 8px;
  font-size: 12px;
}

.modern-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modern-btn.success {
  background: #6fcf97;
  color: white;
}

.modern-btn.warning {
  background: #f2c94c;
  color: #333;
}

.modern-btn.secondary {
  background: #909399;
  color: white;
}

.modern-btn.danger {
  background: #f25b5b;
  color: white;
}

.modern-btn.info {
  background: #409eff;
  color: white;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.batch-operations-bar {
  background: #f0f9ff;
  border: 1px solid #b3e0ff;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #409eff;
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modern-table-container {
  overflow-x: auto;
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.modern-table th {
  background-color: #f8f9fa;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #333;
  border-bottom: 2px solid #eef2f7;
}

.modern-table td {
  padding: 12px;
  border-bottom: 1px solid #eef2f7;
  vertical-align: middle;
}

.modern-table tbody tr:hover {
  background-color: #f8f9fa;
}

.modern-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #667eea;
}

.device-id {
  font-family: monospace;
  font-size: 12px;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
}

.device-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.auto-created-badge {
  background: #fff3cd;
  color: #856404;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
}

.device-type-icon-only {
  font-size: 20px;
}

.auth-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.auth-badge.auth-none {
  background: #f8f9fa;
  color: #999;
}

.auth-badge.auth-unlimited {
  background: #d4edda;
  color: #155724;
}

.auth-badge.auth-normal {
  background: #cce5ff;
  color: #004085;
}

.auth-badge.auth-exhausted {
  background: #f8d7da;
  color: #721c24;
}

.usage-stats {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.usage-bar {
  width: 100%;
  height: 6px;
  background: #eef2f7;
  border-radius: 3px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: #6fcf97;
  border-radius: 3px;
  transition: width 0.3s;
}

.usage-fill.usage-warning {
  background: #f2c94c;
}

.usage-text {
  font-size: 12px;
  color: #666;
  text-align: right;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.status-active {
  background: #d4edda;
  color: #155724;
}

.status-badge.status-inactive {
  background: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eef2f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.card-content {
  padding: 20px;
}

.loading-state {
  text-align: center;
  padding: 30px;
  color: #999;
}

.device-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.device-type-card {
  border: 1px solid #eef2f7;
  border-radius: 10px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
}

.device-type-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.device-type-card.system-type {
  background: #f8f9fa;
}

.device-type-card.inactive {
  opacity: 0.7;
}

.type-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.type-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.type-info {
  flex: 1;
}

.type-name {
  margin: 0 0 5px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.type-code {
  font-size: 12px;
  color: #999;
  background: #eef2f7;
  padding: 2px 6px;
  border-radius: 4px;
}

.type-status {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.system-badge {
  background: #667eea;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.inactive-badge {
  background: #f25b5b;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.type-description {
  color: #666;
  margin-bottom: 20px;
  min-height: 40px;
}

.type-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.system-type-hint {
  color: #999;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-management {
    padding: 15px;
  }
  
  .device-stats-cards {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  }
  
  .stat-card {
    padding: 12px;
  }
  
  .stat-value {
    font-size: 18px;
  }
  
  .device-toolbar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .modern-search {
    max-width: none;
  }
  
  .batch-operations-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .card-actions {
    justify-content: center;
  }
  
  .device-types-grid {
    grid-template-columns: 1fr;
  }
}