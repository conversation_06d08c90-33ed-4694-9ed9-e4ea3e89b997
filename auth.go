package main

import (
	"crypto/rand"
	"database/sql"
	"encoding/hex"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

var jwtSecret []byte

// initJWTSecret 初始化JWT密钥
func initJWTSecret() {
	// 从环境变量获取JWT密钥
	if secret := os.Getenv("JWT_SECRET"); secret != "" {
		jwtSecret = []byte(secret)
		log.Println("JWT secret loaded from environment variable")
		return
	}

	// 尝试从数据库加载JWT密钥
	secret := loadJWTSecretFromDB()
	if secret != "" {
		jwtSecret = []byte(secret)
		log.Println("JWT secret loaded from database")
		return
	}

	// 如果数据库中也没有，生成新密钥并保存
	bytes := make([]byte, 32) // 256位密钥
	if _, err := rand.Read(bytes); err != nil {
		log.Fatal("Failed to generate JWT secret:", err)
	}
	secret = hex.EncodeToString(bytes)
	jwtSecret = []byte(secret)

	// 保存到数据库
	if err := saveJWTSecretToDB(secret); err != nil {
		log.Printf("Warning: Failed to save JWT secret to database: %v", err)
		log.Println("JWT secret generated but not persisted (consider setting JWT_SECRET environment variable)")
	} else {
		log.Println("JWT secret generated and saved to database")
	}
}

// loadJWTSecretFromDB 从数据库加载JWT密钥
func loadJWTSecretFromDB() string {
	var secret string
	err := db.QueryRow("SELECT value FROM system_configs WHERE category = 'security' AND key_name = 'jwt_secret'").Scan(&secret)
	if err != nil {
		if err != sql.ErrNoRows {
			log.Printf("Error loading JWT secret from database: %v", err)
		}
		return ""
	}
	return secret
}

// saveJWTSecretToDB 保存JWT密钥到数据库
func saveJWTSecretToDB(secret string) error {
	_, err := db.Exec(`
		INSERT OR REPLACE INTO system_configs (category, key_name, value, value_type, description, updated_at)
		VALUES ('security', 'jwt_secret', ?, 'string', 'JWT密钥', CURRENT_TIMESTAMP)
	`, secret)
	return err
}

// rotateJWTSecret 轮换JWT密钥（管理员功能）
func rotateJWTSecret() error {
	bytes := make([]byte, 32) // 256位密钥
	if _, err := rand.Read(bytes); err != nil {
		return fmt.Errorf("failed to generate new JWT secret: %v", err)
	}

	newSecret := hex.EncodeToString(bytes)
	if err := saveJWTSecretToDB(newSecret); err != nil {
		return fmt.Errorf("failed to save new JWT secret: %v", err)
	}

	jwtSecret = []byte(newSecret)
	log.Println("JWT secret rotated successfully")
	return nil
}

// Claims JWT声明结构
type Claims struct {
	UserID   int    `json:"user_id"`
	Username string `json:"username"`
	jwt.RegisteredClaims
}

// HashPassword 使用bcrypt哈希密码
func HashPassword(password string) (string, error) {
	saltRounds := 12 // 可以通过配置调整
	if rounds := os.Getenv("PASSWORD_SALT_ROUNDS"); rounds != "" {
		// 可以从环境变量读取，但这里保持简单
	}

	bytes, err := bcrypt.GenerateFromPassword([]byte(password), saltRounds)
	return string(bytes), err
}

// CheckPasswordHash 验证密码哈希
func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// GenerateToken 生成JWT令牌
func GenerateToken(userID int, username string) (string, error) {
	// 确保JWT密钥已初始化
	if len(jwtSecret) == 0 {
		initJWTSecret()
	}

	// 从环境变量获取过期时间，默认24小时
	expirationHours := 24
	if hours := os.Getenv("JWT_EXPIRATION_HOURS"); hours != "" {
		// 可以解析，但这里保持简单
	}

	claims := &Claims{
		UserID:   userID,
		Username: username,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expirationHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Issuer:    "ipc-management",
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// ValidateToken 验证JWT令牌
func ValidateToken(tokenString string) (*Claims, error) {
	// 确保JWT密钥已初始化
	if len(jwtSecret) == 0 {
		initJWTSecret()
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	return claims, nil
}

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		tokenString := c.GetHeader("Authorization")
		if tokenString == "" {
			c.JSON(401, gin.H{"error": "Authorization header required"})
			c.Abort()
			return
		}

		// 移除 "Bearer " 前缀
		if len(tokenString) > 7 && tokenString[:7] == "Bearer " {
			tokenString = tokenString[7:]
		}

		claims, err := ValidateToken(tokenString)
		if err != nil {
			c.JSON(401, gin.H{"error": "Invalid token"})
			c.Abort()
			return
		}

		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Next()
	}
}
