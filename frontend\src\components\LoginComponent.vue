<template>
  <div class="login-section">
    <div class="login-card">
      <div class="login-header">
        <div class="login-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <h2>用户登录</h2>
        <p>请输入您的账号信息进行登录</p>
      </div>
      <el-form :model="loginForm" class="login-form" size="large">
        <el-form-item>
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            clearable
            size="large"
          >
            <template #prefix>
              <i class="fas fa-user"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            clearable
            show-password
            size="large"
          >
            <template #prefix>
              <i class="fas fa-lock"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleLogin"
            :loading="props.loginLoading"
            class="login-button"
            :disabled="!loginForm.username.trim() || !loginForm.password.trim()"
          >
            {{ props.loginLoading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>

  <!-- 验证码弹出窗口 -->
  <div v-if="showCaptchaModal" class="captcha-overlay">
    <div class="captcha-modal">
      <div class="captcha-header">
        <h3>安全验证</h3>
        <div class="captcha-subtitle">请按数字顺序点击红点（共{{ count }}个）</div>
      </div>
      <div class="captcha-content">
        <div v-if="loading" class="captcha-loading">验证码加载中...</div>
        <div v-else class="captcha-image-container">
          <img :src="img" @click="onImgClick" ref="imgRef" class="captcha-image"/>
          <template v-for="(p, i) in clicks" :key="i">
            <span :style="pointStyle(p)" class="click-marker">{{ i + 1 }}</span>
          </template>
        </div>
        <div class="captcha-actions">
          <el-button type="primary" @click="submit" :disabled="clicks.length !== count">
            提交验证 ({{ clicks.length }}/{{ count }})
          </el-button>
          <el-button @click="refreshCaptcha">刷新</el-button>
          <el-button @click="cancelLogin">取消</el-button>
        </div>
        <div v-if="msg" class="captcha-message" :style="{color: msgColor}">{{ msg }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 定义 props
const props = defineProps({
  loginLoading: {
    type: Boolean,
    default: false
  }
})

// 定义 emits
const emit = defineEmits(['login'])

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 验证码相关状态
const showCaptchaModal = ref(false)
const img = ref('')
const id = ref('')
const count = ref(0)
const clicks = reactive([])
const loading = ref(false)
const msg = ref('')
const msgColor = ref('red')
const imgRef = ref(null)
const tempLoginData = ref(null)

// 处理登录
const handleLogin = async () => {
  if (!loginForm.username.trim() || !loginForm.password.trim()) {
    ElMessage.error('请输入用户名和密码')
    return
  }

  // 直接加载验证码，不先验证账号密码
  try {
    const res = await axios.post('/api/captcha/prepare', {
      username: loginForm.username,
      password: loginForm.password
    })

    // 显示验证码
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count
    clicks.splice(0)
    msg.value = ''

    // 临时存储登录信息，等验证码通过后再验证
    tempLoginData.value = {
      username: loginForm.username,
      password: loginForm.password
    }
    showCaptchaModal.value = true

  } catch (e) {
    const errorMsg = e.response?.data?.error || '获取验证码失败'
    ElMessage.error(errorMsg)
  }
}

// 验证码图片点击处理
const onImgClick = (e) => {
  const rect = imgRef.value.getBoundingClientRect()

  // 坐标转换
  const scaleX = imgRef.value.naturalWidth / rect.width
  const scaleY = imgRef.value.naturalHeight / rect.height
  const x = Math.round((e.clientX - rect.left) * scaleX)
  const y = Math.round((e.clientY - rect.top) * scaleY)

  if (clicks.length >= count.value) return
  clicks.push({ x, y })
}

// 提交验证码
const submit = async () => {
  try {
    // 如果有临时登录数据，说明是登录流程
    if (tempLoginData.value) {
      const res = await axios.post('/api/captcha/verify-login', {
        id: id.value,
        clicks: clicks,
        username: tempLoginData.value.username,
        password: tempLoginData.value.password
      })

      // 验证成功，触发登录成功事件，传递包含 token 的响应数据
      emit('login', {
        username: tempLoginData.value.username,
        token: res.data.token
      })
      cancelLogin()
      ElMessage.success('登录成功')
    }
  } catch (e) {
    const errorMsg = e.response?.data?.error || '验证失败'
    msg.value = errorMsg
    msgColor.value = 'red'
    ElMessage.error(errorMsg)
  }
}

// 刷新验证码
const refreshCaptcha = async () => {
  if (!tempLoginData.value) return

  loading.value = true
  msg.value = ''
  clicks.splice(0)

  try {
    const res = await axios.post('/api/captcha/prepare', {
      username: tempLoginData.value.username,
      password: tempLoginData.value.password
    })

    // 更新验证码
    img.value = res.data.img
    id.value = res.data.id
    count.value = res.data.count

  } catch (e) {
    const errorMsg = e.response?.data?.error || '获取验证码失败'
    ElMessage.error(errorMsg)
  }
  loading.value = false
}

// 取消登录
const cancelLogin = () => {
  tempLoginData.value = null
  showCaptchaModal.value = false
  img.value = ''
  clicks.splice(0)
  msg.value = ''
}

// 计算点击标记的样式
const pointStyle = (p) => {
  return {
    left: `${(p.x / 300) * 100}%`,
    top: `${(p.y / 150) * 100}%`,
    transform: 'translate(-50%, -50%)'
  }
}
</script>

<style scoped>
.login-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
  padding: 40px;
  text-align: center;
  animation: slideUp 0.6s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.login-header {
  margin-bottom: 35px;
}

.login-icon {
  width: 70px;
  height: 70px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28px;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.login-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px;
}

.login-header p {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0;
  font-weight: 400;
}

.login-form {
  text-align: left;
}

.login-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.login-form :deep(.el-input__wrapper) {
  height: 52px;
  border-radius: 12px;
  font-size: 16px;
  padding-left: 45px;
  padding-right: 15px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.login-form :deep(.el-input__wrapper:hover) {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.login-form :deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
}

.login-form :deep(.el-input__inner) {
  height: 100%;
  border: none;
  background: transparent;
  color: #2c3e50;
  font-weight: 400;
}

.login-form :deep(.el-input__prefix) {
  left: 15px;
  color: #7f8c8d;
  font-size: 16px;
}

.login-button {
  width: 100%;
  height: 52px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  margin-top: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.login-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.1);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 验证码弹窗样式 */
.captcha-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.captcha-modal {
  background: white;
  border-radius: 16px;
  padding: 28px;
  max-width: 420px;
  width: 90%;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: captchaSlideIn 0.3s ease-out;
}

@keyframes captchaSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.captcha-header {
  text-align: center;
  margin-bottom: 24px;
}

.captcha-header h3 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.captcha-subtitle {
  color: #7f8c8d;
  font-size: 14px;
}

.captcha-content {
  text-align: center;
}

.captcha-loading {
  padding: 40px;
  color: #7f8c8d;
  font-size: 14px;
}

.captcha-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 24px;
  border: 2px solid #e8ecf0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.captcha-image {
  display: block;
  width: 300px;
  height: 150px;
  cursor: crosshair;
}

.click-marker {
  position: absolute;
  width: 20px;
  height: 20px;
  background: rgba(102, 126, 234, 0.9);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.captcha-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.captcha-message {
  font-size: 14px;
  margin-top: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  border-left: 4px solid #dc3545;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card {
    padding: 32px 24px;
    margin: 0 16px;
    border-radius: 20px;
  }

  .login-header h2 {
    font-size: 22px;
  }

  .login-icon {
    width: 64px;
    height: 64px;
    font-size: 24px;
  }

  .login-form :deep(.el-input__wrapper) {
    height: 48px;
    padding-left: 40px;
  }

  .login-button {
    height: 48px;
    font-size: 15px;
  }

  /* 移动端验证码弹窗适配 */
  .captcha-modal {
    width: 95%;
    max-width: none;
    margin: 0 10px;
    padding: 24px;
  }

  .captcha-image {
    width: 100%;
    max-width: 280px;
    height: auto;
  }

  .captcha-actions {
    flex-direction: column;
    gap: 10px;
  }

  .captcha-actions .el-button {
    width: 100%;
  }
}
</style>