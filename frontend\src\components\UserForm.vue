<template>
  <div class="user-form">
    <el-form :model="formData" label-width="120px" label-position="left">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="用户名" required>
            <el-input 
              v-model="formData.username" 
              placeholder="请输入用户名"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="isEdit ? '新密码' : '密码'" :required="!isEdit">
            <el-input 
              v-model="formData.password" 
              type="password" 
              :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'"
              show-password
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="邮箱">
            <el-input 
              v-model="formData.email" 
              placeholder="请输入邮箱"
              type="email"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话">
            <el-input 
              v-model="formData.phone" 
              placeholder="请输入电话号码"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注">
        <el-input 
          v-model="formData.remark" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="最大使用次数">
            <el-input-number 
              v-model="formData.maxUsage" 
              :min="1" 
              :max="10000"
              :disabled="!formData.usageControlEnabled"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="过期时间">
            <el-date-picker
              v-model="formData.expireTime"
              type="date"
              placeholder="选择过期时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">权限设置</el-divider>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="启用使用限制">
            <el-switch v-model="formData.usageControlEnabled" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="允许后台登录">
            <el-switch v-model="formData.canLoginBackend" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="允许MQTT登录">
            <el-switch v-model="formData.canLoginMqtt" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="账户状态">
            <el-switch 
              v-model="formData.isActive"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="信息加密">
            <el-switch v-model="formData.accountInfoEncryption" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="账户信息内容">
        <el-checkbox-group v-model="formData.accountInfoContent">
          <el-checkbox label="expire_time">过期时间</el-checkbox>
          <el-checkbox label="max_usage">最大使用次数</el-checkbox>
          <el-checkbox label="current_usage">当前使用次数</el-checkbox>
          <el-checkbox label="device_count">设备数量</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSave"
        :loading="loading"
      >
        {{ loading ? '保存中...' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save', 'cancel'])

const handleSave = () => {
  // 基本验证
  if (!props.formData.username) {
    ElMessage.error('请输入用户名')
    return
  }
  
  if (!props.isEdit && !props.formData.password) {
    ElMessage.error('请输入密码')
    return
  }
  
  if (props.formData.email && !isValidEmail(props.formData.email)) {
    ElMessage.error('请输入有效的邮箱地址')
    return
  }
  
  if (props.formData.usageControlEnabled && (!props.formData.maxUsage || props.formData.maxUsage < 1)) {
    ElMessage.error('启用使用限制时，最大使用次数必须大于0')
    return
  }
  
  emit('save')
}

const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
</script>

<style scoped>
.user-form {
  padding: 0;
}

.form-actions {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.el-form-item {
  margin-bottom: 1.5rem;
}

.el-divider {
  margin: 1.5rem 0;
}

.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.el-checkbox {
  margin-right: 0;
}

@media (max-width: 768px) {
  .el-col {
    margin-bottom: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .el-checkbox-group {
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>
