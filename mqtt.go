package main

import (
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net"
	"strings"
	"sync"
	"time"
)

// EventBroadcaster 事件广播接口
type EventBroadcaster interface {
	BroadcastEvent(eventType, data string)
}

// 全局事件广播器
var GlobalEventBroadcaster EventBroadcaster

// MQTTServer MQTT服务器结构
type MQTTServer struct {
	Port     int
	listener net.Listener
	clients  map[string]*MQTTConnection
	topics   map[string][]string // topic -> client_ids
	mu       sync.RWMutex
	running  bool
	stopChan chan struct{}

	// 安全相关
	config     *MQTTConfig
	blacklist  map[string]*BlacklistEntry  // IP -> BlacklistEntry
	failedAuth map[string]*FailedAuthEntry // IP -> FailedAuthEntry
	securityMu sync.RWMutex

	// 保持消息
	retainedMessages map[string]*RetainedMessage // topic -> message
	retainedMu       sync.RWMutex

	// 遗嘱消息（内存存储）
	willMessages map[string]*WillMessage // client_id -> will_message
	willMu       sync.RWMutex
}

// MQTTConnection MQTT连接结构
type MQTTConnection struct {
	ID       string
	conn     net.Conn
	server   *MQTTServer
	topics   []string
	lastPing time.Time
	mu       sync.RWMutex

	// 认证相关
	username      string
	deviceID      string // 设备ID（从用户名@设备ID中解析）
	clientIP      string
	authenticated bool

	// 遗嘱相关
	willTopic        string
	willMessage      string
	willQoS          int
	willRetain       bool
	hasWill          bool
	normalDisconnect bool // 标识是否为正常断开

	// 心跳包精度优化
	heartbeatStats    HeartbeatStats
	networkQuality    NetworkQuality
	adaptiveInterval  time.Duration
	keepAliveInterval time.Duration
	lastPingRequest   time.Time // PINGREQ发送时间
	lastPingResponse  time.Time // PINGRESP接收时间
	connectedAt       time.Time // 连接建立时间
	missedPings       int       // 连续丢失的心跳包数量
}

// HeartbeatStats 心跳包统计信息
type HeartbeatStats struct {
	TotalHeartbeats  int64
	MissedHeartbeats int64
	AverageLatency   time.Duration
	MaxLatency       time.Duration
	MinLatency       time.Duration
	LastLatency      time.Duration
	LatencyHistory   []time.Duration // 保留最近10次的延迟记录
}

// NetworkQuality 网络质量评估
type NetworkQuality struct {
	Score          float64 // 0-1之间，1表示最好
	PacketLossRate float64
	AverageRTT     time.Duration
	Jitter         time.Duration
	LastUpdated    time.Time
}

// HeartbeatDiagnostics 心跳包诊断信息
type HeartbeatDiagnostics struct {
	ExpectedInterval time.Duration `json:"expected_interval"`
	ActualInterval   time.Duration `json:"actual_interval"`
	DeviationPercent float64       `json:"deviation_percent"`
	IsAbnormal       bool          `json:"is_abnormal"`
	StabilityLevel   string        `json:"stability_level"`
	Trend            string        `json:"trend"`
	Jitter           time.Duration `json:"jitter"`
	Analysis         string        `json:"analysis"`
	LikelyCause      string        `json:"likely_cause"`
	Recommendation   string        `json:"recommendation"`
	ClientKeepAlive  time.Duration `json:"client_keep_alive"`
	ServerTimeout    time.Duration `json:"server_timeout"`
	WriteLatency     time.Duration `json:"write_latency"`
}

// MQTTPacket MQTT消息包结构
type MQTTPacket struct {
	Type    byte
	Length  int
	Payload []byte
}

// MQTTConfig MQTT服务器配置
type MQTTConfig struct {
	RequireAuth             bool `json:"require_auth"`
	MaxFailedAttempts       int  `json:"max_failed_attempts"`
	BlacklistDuration       int  `json:"blacklist_duration"` // 小时
	CleanupInterval         int  `json:"cleanup_interval"`   // 分钟
	AllowAnonymous          bool `json:"allow_anonymous"`
	HeartbeatInterval       int  `json:"heartbeat_interval"`        // 心跳检查间隔（秒）
	HeartbeatTimeout        int  `json:"heartbeat_timeout"`         // 心跳超时时间（秒）
	HeartbeatPacketInterval int  `json:"heartbeat_packet_interval"` // 心跳包间隔（毫秒）
}

// BlacklistEntry 黑名单条目
type BlacklistEntry struct {
	IP        string    `json:"ip"`
	Reason    string    `json:"reason"`
	AddedAt   time.Time `json:"added_at"`
	ExpiresAt time.Time `json:"expires_at"`
	Username  string    `json:"username"`
}

// FailedAuthEntry 失败认证记录
type FailedAuthEntry struct {
	IP          string    `json:"ip"`
	Count       int       `json:"count"`
	LastAttempt time.Time `json:"last_attempt"`
	Username    string    `json:"username"`
}

// RetainedMessage 保持消息结构
type RetainedMessage struct {
	Topic     string    `json:"topic"`
	Message   string    `json:"message"`
	QoS       int       `json:"qos"`
	Timestamp time.Time `json:"timestamp"`
}

// WillMessage 遗嘱消息结构
type WillMessage struct {
	ClientID    string    `json:"client_id"`
	Topic       string    `json:"topic"`
	Message     string    `json:"message"`
	QoS         int       `json:"qos"`
	Retain      bool      `json:"retain"`
	CreatedAt   time.Time `json:"created_at"`
	IsConnected bool      `json:"is_connected"` // 客户端连接状态
}

// MQTT消息类型常量
const (
	CONNECT     = 0x10
	CONNACK     = 0x20
	PUBLISH     = 0x30
	PUBACK      = 0x40
	SUBSCRIBE   = 0x80
	SUBACK      = 0x90
	UNSUBSCRIBE = 0xA0
	UNSUBACK    = 0xB0
	PINGREQ     = 0xC0
	PINGRESP    = 0xD0
	DISCONNECT  = 0xE0
)

var GlobalMQTTServer *MQTTServer

// InitMQTTServer 初始化MQTT服务器
func InitMQTTServer(port int) error {
	// 从数据库读取配置
	config := &MQTTConfig{
		RequireAuth:             GetConfigBool("mqtt", "require_auth", true),
		MaxFailedAttempts:       GetConfigInt("mqtt", "max_failed_attempts", 3),
		BlacklistDuration:       GetConfigInt("mqtt", "blacklist_duration", 24),
		CleanupInterval:         GetConfigInt("mqtt", "cleanup_interval", 30),
		AllowAnonymous:          GetConfigBool("mqtt", "allow_anonymous", false),
		HeartbeatInterval:       GetConfigInt("mqtt", "heartbeat_interval", 30),          // 默认30秒
		HeartbeatTimeout:        GetConfigInt("mqtt", "heartbeat_timeout", 120),          // 默认120秒，更宽容
		HeartbeatPacketInterval: GetConfigInt("mqtt", "heartbeat_packet_interval", 1000), // 默认1000毫秒
	}

	// 如果传入的端口为0，从配置中读取
	if port == 0 {
		port = GetConfigInt("mqtt", "port", 1883)
	}

	server := &MQTTServer{
		Port:             port,
		clients:          make(map[string]*MQTTConnection),
		topics:           make(map[string][]string),
		stopChan:         make(chan struct{}),
		config:           config,
		blacklist:        make(map[string]*BlacklistEntry),
		failedAuth:       make(map[string]*FailedAuthEntry),
		retainedMessages: make(map[string]*RetainedMessage),
		willMessages:     make(map[string]*WillMessage),
	}

	listener, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", port))
	if err != nil {
		return fmt.Errorf("failed to start MQTT server: %v", err)
	}

	server.listener = listener
	server.running = true

	go server.acceptConnections()
	go server.heartbeatChecker()
	go server.securityCleaner()
	go server.statsUpdater()

	// 从数据库加载保持消息
	server.loadRetainedMessagesFromDB()

	GlobalMQTTServer = server
	log.Printf("MQTT server started on port %d with authentication enabled", port)

	return nil
}

// acceptConnections 接受客户端连接
func (s *MQTTServer) acceptConnections() {
	for {
		select {
		case <-s.stopChan:
			return
		default:
			conn, err := s.listener.Accept()
			if err != nil {
				if s.running {
					log.Printf("Failed to accept connection: %v", err)
				}
				continue
			}

			go s.handleConnection(conn)
		}
	}
}

// handleConnection 处理单个客户端连接
func (s *MQTTServer) handleConnection(conn net.Conn) {
	defer conn.Close()

	// 获取客户端IP
	clientIP := conn.RemoteAddr().String()
	if idx := strings.LastIndex(clientIP, ":"); idx != -1 {
		clientIP = clientIP[:idx] // 去掉端口号
	}

	// 将IPv6 localhost转换为IPv4格式
	if clientIP == "[::1]" || clientIP == "::1" {
		clientIP = "127.0.0.1"
	}

	// 检查是否在黑名单中
	if s.isBlacklisted(clientIP) {
		log.Printf("Connection from blacklisted IP %s rejected", clientIP)
		return
	}

	clientID := fmt.Sprintf("client_%d", time.Now().UnixNano())
	client := &MQTTConnection{
		ID:            clientID,
		conn:          conn,
		server:        s,
		topics:        make([]string, 0),
		lastPing:      time.Now(),
		clientIP:      clientIP,
		authenticated: false,
	}

	// 初始化心跳统计
	client.initializeHeartbeatStats()

	s.mu.Lock()
	s.clients[clientID] = client
	s.mu.Unlock()

	defer func() {
		// 获取当前客户端的实际ID（可能在CONNECT处理中被更新了）
		client.mu.RLock()
		actualClientID := client.ID
		shouldTriggerWill := client.hasWill && !client.normalDisconnect && client.authenticated
		willTopic := client.willTopic
		willMessage := client.willMessage
		willQoS := client.willQoS
		willRetain := client.willRetain
		isNormalDisconnect := client.normalDisconnect
		hasWill := client.hasWill
		client.mu.RUnlock()

		log.Printf("Client %s (actual ID: %s): Starting cleanup - normalDisconnect=%t, hasWill=%t, shouldTriggerWill=%t",
			clientID, actualClientID, isNormalDisconnect, hasWill, shouldTriggerWill)

		// 如果是异常断开且有遗嘱，触发遗嘱消息
		if shouldTriggerWill {
			log.Printf("Client %s: Triggering will message due to abnormal disconnect", actualClientID)
			s.triggerWillMessage(actualClientID, willTopic, willMessage, willQoS, willRetain)
		}

		// 清理遗嘱内存记录（只在没有触发遗嘱时清理，因为触发时已经在triggerWillMessage中清理了）
		if hasWill && !shouldTriggerWill {
			s.willMu.Lock()
			delete(s.willMessages, actualClientID)
			s.willMu.Unlock()
			log.Printf("Client %s: Will message automatically deleted from memory (normal disconnect)", actualClientID)
		}

		// 安全的延迟清理机制，使用连接对象指针比较避免竞态条件
		go func(clientToClean *MQTTConnection, clientIDToClean string) {
			time.Sleep(5 * time.Second) // 等待5秒，允许客户端重连

			s.mu.Lock()
			defer s.mu.Unlock()

			// 检查当前映射中的客户端是否是同一个连接对象
			if currentClient, exists := s.clients[clientIDToClean]; exists {
				// 通过指针比较确定是否是同一个连接
				if currentClient == clientToClean {
					// 是同一个连接对象，需要清理
					delete(s.clients, clientIDToClean)
					log.Printf("Client %s: Removed from clients map (delayed cleanup)", clientIDToClean)

					// 清理订阅
					s.cleanupClientSubscriptions(clientIDToClean)
				} else {
					// 不是同一个连接对象，说明客户端已重连，跳过清理
					log.Printf("Client %s: Skipping cleanup - client has reconnected with new connection", clientIDToClean)
				}
			} else {
				// 客户端已经被其他地方清理了
				log.Printf("Client %s: Already cleaned up by other process", clientIDToClean)
			}
		}(client, actualClientID)

		// 立即将连接设置为nil，确保统计数据中不包含已断开的客户端
		if client, exists := s.clients[actualClientID]; exists {
			client.mu.Lock()
			client.conn = nil
			client.mu.Unlock()
		}

		log.Printf("Client %s: Cleanup completed and disconnected", actualClientID)

		// 广播客户端断开连接事件
		s.broadcastMQTTEvent("client_disconnected", map[string]interface{}{
			"client_id": actualClientID,
			"timestamp": time.Now().Unix(),
		})
	}()

	log.Printf("New client connected: %s", clientID)

	for {
		packet, err := client.readPacket()
		if err != nil {
			log.Printf("Error reading packet from %s: %v", clientID, err)
			break
		}

		err = client.handlePacket(packet)
		if err != nil {
			log.Printf("Error handling packet from %s: %v", clientID, err)
			// 检查是否是正常断开连接的错误消息
			if err.Error() == "client disconnected" {
				log.Printf("Client %s: Normal disconnect detected", clientID)
			}
			break
		}
	}
}

// readPacket 读取MQTT数据包
func (c *MQTTConnection) readPacket() (*MQTTPacket, error) {
	// 读取消息类型
	msgType := make([]byte, 1)
	_, err := c.conn.Read(msgType)
	if err != nil {
		return nil, err
	}

	packet := &MQTTPacket{
		Type: msgType[0],
	}

	// 解析剩余长度（可变长度编码）
	remainingLength := 0
	multiplier := 1
	for {
		b := make([]byte, 1)
		_, err := c.conn.Read(b)
		if err != nil {
			return nil, err
		}
		remainingLength += int(b[0]&0x7F) * multiplier
		if b[0]&0x80 == 0 {
			break
		}
		multiplier *= 128
		if multiplier > 128*128*128 {
			return nil, fmt.Errorf("invalid remaining length")
		}
	}

	packet.Length = remainingLength
	// log.Printf("MQTT Read: Type=0x%02x, Remaining Length=%d", packet.Type, remainingLength)

	// 读取有效载荷
	if remainingLength > 0 {
		payload := make([]byte, remainingLength)
		_, err = c.conn.Read(payload)
		if err != nil {
			return nil, err
		}
		packet.Payload = payload
	}

	return packet, nil
}

// handlePacket 处理MQTT数据包
func (c *MQTTConnection) handlePacket(packet *MQTTPacket) error {
	switch packet.Type & 0xF0 {
	case CONNECT:
		return c.handleConnect(packet)
	case PUBLISH:
		return c.handlePublish(packet)
	case SUBSCRIBE:
		return c.handleSubscribe(packet)
	case UNSUBSCRIBE:
		return c.handleUnsubscribe(packet)
	case PINGREQ:
		return c.handlePingReq(packet)
	case DISCONNECT:
		return c.handleDisconnect(packet)
	default:
		log.Printf("Unknown packet type: 0x%02X", packet.Type)
		return nil
	}
}

// handleConnect 处理连接请求
func (c *MQTTConnection) handleConnect(packet *MQTTPacket) error {
	log.Printf("Client %s: CONNECT from %s", c.ID, c.clientIP)

	// 解析CONNECT包
	clientID, username, password, err := c.parseConnectPacket(packet)
	if err != nil {
		log.Printf("Client %s: failed to parse CONNECT packet: %v", c.ID, err)
		return c.sendConnack(0x04) // 拒绝连接
	}

	// 处理客户端ID
	if clientID == "" {
		// 如果客户端没有提供ID，生成一个临时ID
		// 注意：在完整的MQTT实现中，这需要检查CleanSession标志
		clientID = fmt.Sprintf("auto_%d", time.Now().UnixNano())
		log.Printf("Client provided empty ID, generated: %s", clientID)
	}

	// 检查是否有同ID的客户端已存在（重连情况）
	var existingTopics []string
	c.server.mu.Lock()
	if existingClient, exists := c.server.clients[clientID]; exists {
		// 保存现有客户端的订阅信息
		existingClient.mu.RLock()
		existingTopics = make([]string, len(existingClient.topics))
		copy(existingTopics, existingClient.topics)
		existingClient.mu.RUnlock()

		// 关闭旧连接（如果存在）
		if existingClient.conn != nil {
			existingClient.conn.Close()
			log.Printf("Client %s: Found existing connection, preserving %d subscriptions", clientID, len(existingTopics))
		} else {
			log.Printf("Client %s: Found existing client record (conn=nil), preserving %d subscriptions", clientID, len(existingTopics))
		}
	}

	// 先从旧ID的映射中删除
	delete(c.server.clients, c.ID)

	// 使用新的客户端ID
	c.ID = clientID

	// 恢复订阅信息到新的连接
	if len(existingTopics) > 0 {
		c.mu.Lock()
		c.topics = existingTopics
		c.mu.Unlock()
		log.Printf("Client %s: Restored %d subscriptions: %v", clientID, len(existingTopics), existingTopics)
	}

	c.server.clients[clientID] = c
	c.server.mu.Unlock()

	log.Printf("Client ID set to: %s", clientID)

	// 验证认证
	if c.server.config.RequireAuth && !c.server.config.AllowAnonymous {
		if !c.authenticate(username, password) {
			c.server.recordFailedAuth(c.clientIP, username)
			log.Printf("Client %s: authentication failed for user %s", c.ID, username)
			return c.sendConnack(0x04) // 拒绝连接
		}
	}

	c.username = username
	c.authenticated = true

	// MQTT客户端认证成功后，检查设备状态
	// 注意：设备ID与MQTT客户端ID相同
	if err := AutoCreateDeviceOnMQTTLogin(c.ID, username); err != nil {
		log.Printf("Client %s: 自动创建设备失败: %v", c.ID, err)
		// 不阻止MQTT连接，只记录错误
	}

	// 无论是否自动创建，都要检查设备状态
	device, err := GetDeviceByDeviceID(c.ID)
	if err != nil {
		log.Printf("Client %s: 无法获取设备信息: %v", c.ID, err)
		return c.sendConnack(0x04) // 拒绝连接
	}

	if !device.IsActive {
		log.Printf("Client %s: 设备已被禁用", c.ID)
		return c.sendConnack(0x04) // 拒绝连接
	}

	// 如果客户端设置了遗嘱，保存到内存
	if c.hasWill {
		c.server.willMu.Lock()
		c.server.willMessages[c.ID] = &WillMessage{
			ClientID:    c.ID,
			Topic:       c.willTopic,
			Message:     c.willMessage,
			QoS:         c.willQoS,
			Retain:      c.willRetain,
			CreatedAt:   time.Now(),
			IsConnected: true,
		}
		c.server.willMu.Unlock()
		log.Printf("Client %s: Will message saved to memory - topic='%s', message='%s'", c.ID, c.willTopic, c.willMessage)
	}

	// 发送CONNACK成功
	err = c.sendConnack(0x00)
	if err != nil {
		return err
	}

	log.Printf("Client %s: authenticated successfully as %s", c.ID, username)

	// 更新心跳时间，避免刚连接就被认为超时
	c.mu.Lock()
	c.lastPing = time.Now()
	c.mu.Unlock()

	// 广播客户端连接事件 - 在认证成功后发送
	c.server.broadcastMQTTEvent("client_connected", map[string]interface{}{
		"client": map[string]interface{}{
			"id":                c.ID,
			"username":          c.username,
			"client_ip":         c.clientIP,
			"authenticated":     c.authenticated,
			"topics":            len(c.topics),
			"subscribed_topics": c.topics,
			"last_ping":         c.lastPing.UnixMilli(),
		},
		"timestamp": time.Now().Unix(),
	})

	// 如果恢复了订阅，发送相关的保持消息
	if len(existingTopics) > 0 {
		go func() {
			for _, topic := range existingTopics {
				c.sendRetainedMessages(topic)
			}
		}()
	}

	// 发送授权信息给客户端
	go func() {
		if err := SendAccountInfoOnLogin(c.ID, c.username, c.deviceID); err != nil {
			log.Printf("Failed to send authorization info to client %s: %v", c.ID, err)
		}
	}()

	return nil
}

// handlePublish 处理发布消息
func (c *MQTTConnection) handlePublish(packet *MQTTPacket) error {
	if len(packet.Payload) < 2 {
		return fmt.Errorf("invalid publish payload")
	}

	// 更新心跳时间
	c.mu.Lock()
	oldPing := c.lastPing
	c.lastPing = time.Now()
	c.mu.Unlock()

	// 记录PUBLISH消息也会更新心跳时间
	interval := c.lastPing.Sub(oldPing)
	log.Printf("📤 Client %s: PUBLISH (also updates heartbeat) - Interval: %v", c.ID, interval)

	// 解析主题长度
	topicLen := int(packet.Payload[0])<<8 | int(packet.Payload[1])
	if len(packet.Payload) < 2+topicLen {
		return fmt.Errorf("invalid topic length")
	}

	topic := string(packet.Payload[2 : 2+topicLen])
	message := packet.Payload[2+topicLen:]

	log.Printf("Client %s: PUBLISH to %s: %s", c.ID, topic, string(message))

	// 广播消息给订阅者
	c.server.broadcastMessage(topic, message, c.ID)

	// 广播消息发布事件
	c.server.broadcastMQTTEvent("message_received", map[string]interface{}{
		"topic":     topic,
		"message":   string(message),
		"timestamp": time.Now().Unix(),
	})

	// 处理特定主题的业务逻辑
	c.handleBusinessLogic(topic, message)

	return nil
}

// handleSubscribe 处理订阅请求
func (c *MQTTConnection) handleSubscribe(packet *MQTTPacket) error {
	if len(packet.Payload) < 4 {
		return fmt.Errorf("invalid subscribe payload")
	}

	// 更新心跳时间
	c.mu.Lock()
	oldPing := c.lastPing
	c.lastPing = time.Now()
	c.mu.Unlock()

	// 记录SUBSCRIBE消息也会更新心跳时间
	interval := c.lastPing.Sub(oldPing)
	log.Printf("📥 Client %s: SUBSCRIBE (also updates heartbeat) - Interval: %v", c.ID, interval)

	// 解析主题长度
	topicLen := int(packet.Payload[2])<<8 | int(packet.Payload[3])
	if len(packet.Payload) < 4+topicLen {
		return fmt.Errorf("invalid topic length")
	}

	topic := string(packet.Payload[4 : 4+topicLen])

	log.Printf("Client %s: SUBSCRIBE to %s", c.ID, topic)

	// 添加到订阅列表
	c.server.mu.Lock()
	if _, exists := c.server.topics[topic]; !exists {
		c.server.topics[topic] = make([]string, 0)
	}

	// 检查是否已经订阅，避免重复
	alreadySubscribed := false
	for _, clientID := range c.server.topics[topic] {
		if clientID == c.ID {
			alreadySubscribed = true
			break
		}
	}
	if !alreadySubscribed {
		c.server.topics[topic] = append(c.server.topics[topic], c.ID)
	}
	c.server.mu.Unlock()

	c.mu.Lock()
	// 检查客户端是否已经订阅了这个主题
	clientAlreadySubscribed := false
	for _, existingTopic := range c.topics {
		if existingTopic == topic {
			clientAlreadySubscribed = true
			break
		}
	}
	if !clientAlreadySubscribed {
		c.topics = append(c.topics, topic)
		log.Printf("Client %s: Added topic %s to subscription list. Total topics: %d", c.ID, topic, len(c.topics))
		log.Printf("Client %s: Current subscribed topics: %v", c.ID, c.topics)
	} else {
		log.Printf("Client %s: Already subscribed to topic %s", c.ID, topic)
	}
	c.mu.Unlock()

	// 发送SUBACK
	suback := []byte{SUBACK, 0x03, packet.Payload[0], packet.Payload[1], 0x00}
	_, err := c.conn.Write(suback)
	if err != nil {
		return err
	}

	log.Printf("Client %s: sent SUBACK for %s", c.ID, topic)

	// 广播客户端状态更新事件 - 订阅完成后
	c.server.broadcastMQTTEvent("client_updated", map[string]interface{}{
		"client": map[string]interface{}{
			"id":                c.ID,
			"username":          c.username,
			"client_ip":         c.clientIP,
			"authenticated":     c.authenticated,
			"topics":            len(c.topics),
			"subscribed_topics": c.topics,
			"last_ping":         c.lastPing.UnixMilli(),
		},
		"timestamp": time.Now().Unix(),
	})

	// 检查并发送匹配的保持消息
	log.Printf("Client %s: Starting to send retained messages for topic %s", c.ID, topic)
	c.sendRetainedMessages(topic)

	return nil
}

// handleUnsubscribe 处理取消订阅
func (c *MQTTConnection) handleUnsubscribe(packet *MQTTPacket) error {
	if len(packet.Payload) < 4 {
		return fmt.Errorf("invalid unsubscribe payload")
	}

	// 解析主题长度
	topicLen := int(packet.Payload[2])<<8 | int(packet.Payload[3])
	if len(packet.Payload) < 4+topicLen {
		return fmt.Errorf("invalid topic length")
	}

	topic := string(packet.Payload[4 : 4+topicLen])

	log.Printf("Client %s: UNSUBSCRIBE from %s", c.ID, topic)

	// 从订阅列表移除
	c.server.mu.Lock()
	if clients, exists := c.server.topics[topic]; exists {
		newClients := make([]string, 0)
		for _, id := range clients {
			if id != c.ID {
				newClients = append(newClients, id)
			}
		}
		if len(newClients) > 0 {
			c.server.topics[topic] = newClients
		} else {
			delete(c.server.topics, topic)
		}
	}
	c.server.mu.Unlock()

	c.mu.Lock()
	newTopics := make([]string, 0)
	for _, t := range c.topics {
		if t != topic {
			newTopics = append(newTopics, t)
		}
	}
	c.topics = newTopics
	c.mu.Unlock()

	// 发送UNSUBACK
	unsuback := []byte{UNSUBACK, 0x02, packet.Payload[0], packet.Payload[1]}
	_, err := c.conn.Write(unsuback)
	if err != nil {
		return err
	}

	log.Printf("Client %s: sent UNSUBACK for %s", c.ID, topic)
	return nil
}

// handlePingReq 处理心跳请求 - 优化版本
func (c *MQTTConnection) handlePingReq(packet *MQTTPacket) error {
	now := time.Now()

	c.mu.Lock()
	lastPingTime := c.lastPing
	c.lastPing = now
	c.lastPingRequest = now

	// 重置连续丢失计数
	c.missedPings = 0

	// 计算心跳间隔
	interval := now.Sub(lastPingTime)

	// 更新心跳统计
	c.updateHeartbeatStats(interval)

	// 评估网络质量
	c.assessNetworkQuality(interval)

	// 自适应调整心跳间隔
	c.adjustAdaptiveInterval()

	// 获取诊断信息
	diagnostics := c.generateHeartbeatDiagnostics(interval)

	c.mu.Unlock()

	// 详细的心跳诊断日志
	log.Printf("🔥 [HEARTBEAT DIAGNOSIS] Client %s:", c.ID)
	log.Printf("   📊 Interval: %v (%.6fs) | Expected: %.2fs | Deviation: %.2f%%",
		interval, interval.Seconds(), diagnostics.ExpectedInterval.Seconds(), diagnostics.DeviationPercent)
	log.Printf("   🌐 Network Quality: %.4f | Latency: %v | Total Beats: %d",
		c.networkQuality.Score, c.heartbeatStats.LastLatency, c.heartbeatStats.TotalHeartbeats)
	log.Printf("   ⏱️  Client KeepAlive: %v | Adaptive: %v | Server Timeout: %ds",
		c.keepAliveInterval, c.adaptiveInterval, c.server.config.HeartbeatTimeout)
	log.Printf("   📈 Stability: %s | Trend: %s | Jitter: %v",
		diagnostics.StabilityLevel, diagnostics.Trend, diagnostics.Jitter)

	// 如果间隔异常，详细分析和处理
	if diagnostics.IsAbnormal {
		log.Printf("⚠️  [ABNORMAL HEARTBEAT] Client %s:", c.ID)
		log.Printf("   🔍 Analysis: %s", diagnostics.Analysis)
		log.Printf("   💡 Likely Cause: %s", diagnostics.LikelyCause)
		log.Printf("   🎯 Recommendation: %s", diagnostics.Recommendation)

		// 使用配置的最小心跳包间隔进行检查
		minInterval := time.Duration(c.server.config.HeartbeatPacketInterval) * time.Millisecond
		if interval < minInterval {
			c.addToBlacklistForFrequentHeartbeat(interval)
			return fmt.Errorf("client blacklisted for frequent heartbeat (interval %v < minimum %v)", interval, minInterval)
		}
	}

	// 发送PINGRESP
	pingresp := []byte{PINGRESP, 0x00}
	writeStart := time.Now()
	_, err := c.conn.Write(pingresp)
	writeEnd := time.Now()

	if err != nil {
		return err
	}

	// 记录PINGRESP发送时间和写入延迟
	c.mu.Lock()
	c.lastPingResponse = writeEnd
	writeLatency := writeEnd.Sub(writeStart)
	c.mu.Unlock()

	log.Printf("   📤 PINGRESP sent in %v", writeLatency)

	// 广播心跳事件（包含诊断信息）
	c.server.broadcastMQTTEvent("client_heartbeat", map[string]interface{}{
		"client_id":         c.ID,
		"timestamp":         now.Unix(),
		"type":              "ping",
		"interval":          interval.Seconds(),
		"network_quality":   c.networkQuality.Score,
		"latency":           c.heartbeatStats.LastLatency.Milliseconds(),
		"adaptive_interval": c.adaptiveInterval.Seconds(),
		"diagnostics":       diagnostics,
	})

	return nil
}

// addToBlacklistForFrequentHeartbeat 因频繁心跳包将客户端加入黑名单
func (c *MQTTConnection) addToBlacklistForFrequentHeartbeat(actualInterval time.Duration) {
	// 获取客户端IP
	clientIP := c.clientIP
	username := c.username
	if username == "" {
		username = "unknown"
	}

	// 构建黑名单原因
	reason := fmt.Sprintf("心跳间隔过短 (%v < 1秒)，影响服务器性能", actualInterval)

	// 加入黑名单，默认24小时
	c.server.securityMu.Lock()
	expiresAt := time.Now().Add(24 * time.Hour)
	entry := &BlacklistEntry{
		IP:        clientIP,
		Reason:    reason,
		AddedAt:   time.Now(),
		ExpiresAt: expiresAt,
		Username:  username,
	}
	c.server.blacklist[clientIP] = entry
	c.server.securityMu.Unlock()

	log.Printf("🚫 [BLACKLIST] Client %s (IP: %s, User: %s) 已加入黑名单",
		c.ID, clientIP, username)
	log.Printf("   📋 原因: %s", reason)
	log.Printf("   ⏰ 过期时间: %s", expiresAt.Format("2006-01-02 15:04:05"))

	// 发送最终通知给客户端（如果已认证）
	if c.authenticated {
		noticeMsg := fmt.Sprintf(`{
			"type": "blacklist_notice",
			"message": "由于心跳间隔过短，您的IP已被加入黑名单",
			"reason": "%s",
			"expires_at": "%s",
			"timestamp": %d
		}`, reason, expiresAt.Format("2006-01-02 15:04:05"), time.Now().Unix())

		topic := fmt.Sprintf("system/blacklist/%s", c.ID)
		c.publishToTopic(topic, []byte(noticeMsg))
	}

	// 广播黑名单事件
	c.server.broadcastMQTTEvent("client_blacklisted", map[string]interface{}{
		"timestamp":  time.Now().Unix(),
		"client_id":  c.ID,
		"ip":         clientIP,
		"username":   username,
		"reason":     reason,
		"expires_at": expiresAt.Unix(),
		"interval":   actualInterval.Seconds(),
	})
}

// handleDisconnect 处理断开连接
func (c *MQTTConnection) handleDisconnect(packet *MQTTPacket) error {
	log.Printf("Client %s: DISCONNECT", c.ID)

	// 标记为正常断开，遗嘱清理将在defer中处理
	c.mu.Lock()
	c.normalDisconnect = true
	c.mu.Unlock()

	// 返回错误以退出处理循环，触发defer清理逻辑
	return fmt.Errorf("client disconnected")
}

// broadcastMessage 广播消息给订阅者（优化版本）
func (s *MQTTServer) broadcastMessage(topic string, message []byte, senderID string) {
	s.mu.RLock()
	subscribers, exists := s.topics[topic]
	if !exists {
		s.mu.RUnlock()
		return
	}

	// 预分配切片容量，减少内存分配
	clients := make([]*MQTTConnection, 0, len(subscribers))
	for _, clientID := range subscribers {
		if clientID != senderID {
			if client, exists := s.clients[clientID]; exists {
				// 检查连接是否有效
				client.mu.RLock()
				if client.conn != nil {
					clients = append(clients, client)
				}
				client.mu.RUnlock()
			}
		}
	}
	s.mu.RUnlock()

	if len(clients) == 0 {
		return // 没有有效的订阅者
	}

	// 预构建PUBLISH消息，避免重复构建
	packet := s.buildPublishPacket(topic, message)

	// 使用工作池模式发送消息，避免创建过多goroutine
	s.broadcastToClients(clients, packet, topic)
}

// buildPublishPacket 构建PUBLISH消息包
func (s *MQTTServer) buildPublishPacket(topic string, message []byte) []byte {
	topicBytes := []byte(topic)
	topicLen := len(topicBytes)

	payload := make([]byte, 2+topicLen+len(message))
	payload[0] = byte(topicLen >> 8)
	payload[1] = byte(topicLen & 0xFF)
	copy(payload[2:2+topicLen], topicBytes)
	copy(payload[2+topicLen:], message)

	packet := []byte{PUBLISH, byte(len(payload))}
	packet = append(packet, payload...)
	return packet
}

// broadcastToClients 使用工作池模式广播消息
func (s *MQTTServer) broadcastToClients(clients []*MQTTConnection, packet []byte, topic string) {
	const maxWorkers = 10 // 限制并发goroutine数量

	clientChan := make(chan *MQTTConnection, len(clients))

	// 启动工作协程
	workers := len(clients)
	if workers > maxWorkers {
		workers = maxWorkers
	}

	var wg sync.WaitGroup
	for i := 0; i < workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for client := range clientChan {
				s.sendToClient(client, packet)
			}
		}()
	}

	// 发送客户端到通道
	for _, client := range clients {
		clientChan <- client
	}
	close(clientChan)

	// 等待所有工作完成
	wg.Wait()

	log.Printf("Broadcasted message to %d subscribers of topic %s", len(clients), topic)
}

// sendToClient 发送消息到单个客户端
func (s *MQTTServer) sendToClient(client *MQTTConnection, packet []byte) {
	client.mu.RLock()
	conn := client.conn
	clientID := client.ID
	client.mu.RUnlock()

	if conn == nil {
		return // 连接已关闭
	}

	_, err := conn.Write(packet)
	if err != nil {
		log.Printf("Failed to send message to client %s: %v", clientID, err)
		// 可以考虑在这里标记客户端为断开状态
	}
}

// handleBusinessLogic 处理特定主题的业务逻辑
func (c *MQTTConnection) handleBusinessLogic(topic string, message []byte) {
	switch topic {
	case "ipc/auth/request":
		c.handleAuthRequest(message)
	case "ipc/captcha/request":
		c.handleCaptchaRequest(message)
	case "ipc/system/command":
		c.handleSystemCommand(message)
	}
}

// handleAuthRequest 处理认证请求
func (c *MQTTConnection) handleAuthRequest(message []byte) {
	log.Printf("Client %s: Auth request - %s", c.ID, string(message))

	// 这里可以添加认证逻辑
	// 例如解析用户名密码，验证并返回结果

	response := map[string]interface{}{
		"type":      "auth_response",
		"success":   true,
		"message":   "Authentication successful",
		"timestamp": time.Now().Unix(),
	}

	responseBytes, _ := json.Marshal(response)
	c.publishToTopic("ipc/auth/response", responseBytes)
}

// handleCaptchaRequest 处理验证码请求
func (c *MQTTConnection) handleCaptchaRequest(message []byte) {
	log.Printf("Client %s: Captcha request - %s", c.ID, string(message))

	// 这里可以添加验证码生成逻辑

	response := map[string]interface{}{
		"type":      "captcha_response",
		"success":   true,
		"message":   "Captcha generated",
		"timestamp": time.Now().Unix(),
	}

	responseBytes, _ := json.Marshal(response)
	c.publishToTopic("ipc/captcha/response", responseBytes)
}

// handleSystemCommand 处理系统命令
func (c *MQTTConnection) handleSystemCommand(message []byte) {
	log.Printf("Client %s: System command - %s", c.ID, string(message))

	var cmd map[string]interface{}
	if err := json.Unmarshal(message, &cmd); err != nil {
		log.Printf("Failed to parse system command: %v", err)
		return
	}

	response := map[string]interface{}{
		"type":      "system_response",
		"command":   cmd,
		"success":   true,
		"timestamp": time.Now().Unix(),
	}

	responseBytes, _ := json.Marshal(response)
	c.publishToTopic("ipc/system/response", responseBytes)
}

// publishToTopic 发布消息到指定主题
func (c *MQTTConnection) publishToTopic(topic string, message []byte) {
	c.server.broadcastMessage(topic, message, c.ID)
}

// heartbeatChecker 心跳检查器
func (s *MQTTServer) heartbeatChecker() {
	interval := time.Duration(s.config.HeartbeatInterval) * time.Second
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	log.Printf("MQTT heartbeat checker started: interval=%ds, timeout=%ds",
		s.config.HeartbeatInterval, s.config.HeartbeatTimeout)

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.checkClientHeartbeats()
		}
	}
}

// checkClientHeartbeats 检查客户端心跳
func (s *MQTTServer) checkClientHeartbeats() {
	now := time.Now()
	timeout := time.Duration(s.config.HeartbeatTimeout) * time.Second

	s.mu.RLock()
	expiredClients := make([]string, 0)
	for clientID, client := range s.clients {
		client.mu.RLock()
		if now.Sub(client.lastPing) > timeout {
			expiredClients = append(expiredClients, clientID)
		}
		client.mu.RUnlock()
	}
	s.mu.RUnlock()

	// 断开过期客户端
	for _, clientID := range expiredClients {
		s.mu.RLock()
		if client, exists := s.clients[clientID]; exists {
			// 标记为异常断开（心跳超时），确保触发遗嘱消息
			client.mu.Lock()
			client.normalDisconnect = false // 确保这是异常断开
			client.mu.Unlock()

			// 关闭连接，这将触发handleConnection的defer清理逻辑
			client.conn.Close()
			log.Printf("Client %s disconnected due to heartbeat timeout", clientID)
		}
		s.mu.RUnlock()
	}
}

// PublishMessage 服务器主动发布消息
func (s *MQTTServer) PublishMessage(topic string, message interface{}) error {
	messageBytes, err := json.Marshal(message)
	if err != nil {
		return err
	}

	s.broadcastMessage(topic, messageBytes, "server")

	// 广播消息发布事件
	s.broadcastMQTTEvent("message_published", map[string]interface{}{
		"topic":     topic,
		"message":   message,
		"timestamp": time.Now().Unix(),
	})

	return nil
}

// broadcastMQTTEvent 广播MQTT事件到SSE客户端
func (s *MQTTServer) broadcastMQTTEvent(eventType string, data map[string]interface{}) {
	log.Printf("Broadcasting MQTT event: %s with data: %+v", eventType, data)
	if GlobalEventBroadcaster != nil {
		dataBytes, err := json.Marshal(data)
		if err != nil {
			log.Printf("Failed to marshal MQTT event data: %v", err)
			return
		}
		log.Printf("Sending event to SSE clients: %s", eventType)
		GlobalEventBroadcaster.BroadcastEvent(eventType, string(dataBytes))
	} else {
		log.Printf("GlobalEventBroadcaster is nil, cannot broadcast event")
	}
}

// SendMessageToClient 向指定客户端发送消息
func (s *MQTTServer) SendMessageToClient(clientID string, topic string, message interface{}) error {
	var messageBytes []byte

	// 如果是字符串，直接转换为字节数组
	if str, ok := message.(string); ok {
		messageBytes = []byte(str)
	} else {
		// 否则进行JSON序列化
		var err error
		messageBytes, err = json.Marshal(message)
		if err != nil {
			return err
		}
	}

	s.mu.RLock()
	client, exists := s.clients[clientID]
	s.mu.RUnlock()

	if !exists {
		return fmt.Errorf("client %s not found", clientID)
	}

	// 构建PUBLISH消息
	topicBytes := []byte(topic)
	topicLen := len(topicBytes)

	payload := make([]byte, 2+topicLen+len(messageBytes))
	payload[0] = byte(topicLen >> 8)
	payload[1] = byte(topicLen & 0xFF)
	copy(payload[2:2+topicLen], topicBytes)
	copy(payload[2+topicLen:], messageBytes)

	// 使用可变长度编码来支持更长的消息
	packet := []byte{PUBLISH}
	remainingLength := encodeRemainingLength(len(payload))
	packet = append(packet, remainingLength...)
	packet = append(packet, payload...)

	// 发送给指定客户端
	_, err := client.conn.Write(packet)
	if err != nil {
		return fmt.Errorf("failed to send message to client %s: %v", clientID, err)
	}

	log.Printf("Sent message to client %s on topic %s", clientID, topic)
	return nil
}

// encodeRemainingLength 编码MQTT剩余长度字段
func encodeRemainingLength(length int) []byte {
	var encoded []byte
	for {
		encodedByte := byte(length % 128)
		length = length / 128
		if length > 0 {
			encodedByte |= 128
		}
		encoded = append(encoded, encodedByte)
		if length == 0 {
			break
		}
	}
	return encoded
}

// GetStats 获取服务器统计信息
func (s *MQTTServer) GetStats() map[string]interface{} {
	s.mu.RLock()
	s.securityMu.RLock()
	defer s.mu.RUnlock()
	defer s.securityMu.RUnlock()

	clientList := make([]map[string]interface{}, 0)
	for clientID, client := range s.clients {
		client.mu.RLock()
		clientInfo := map[string]interface{}{
			"id":                clientID,
			"username":          client.username,
			"client_ip":         client.clientIP,
			"authenticated":     client.authenticated,
			"connected_at":      client.lastPing.Format("2006-01-02 15:04:05"),
			"last_ping":         client.lastPing.UnixMilli(), // 使用毫秒时间戳提高精度
			"subscribed_topics": client.topics,               // 添加客户端订阅的主题列表
		}
		client.mu.RUnlock()
		clientList = append(clientList, clientInfo)
	}

	topicList := make([]string, 0)
	for topic := range s.topics {
		topicList = append(topicList, topic)
	}

	blacklistList := make([]BlacklistEntry, 0)
	for _, entry := range s.blacklist {
		blacklistList = append(blacklistList, *entry)
	}

	failedAuthList := make([]FailedAuthEntry, 0)
	for _, entry := range s.failedAuth {
		failedAuthList = append(failedAuthList, *entry)
	}

	stats := map[string]interface{}{
		"port":              s.Port,
		"running":           s.running,
		"clients":           len(s.clients),
		"topics":            len(s.topics),
		"client_list":       clientList,
		"topic_list":        topicList,
		"blacklist":         blacklistList,
		"failed_auth":       failedAuthList,
		"config":            s.config,
		"blacklist_count":   len(s.blacklist),
		"failed_auth_count": len(s.failedAuth),
	}

	return stats
}

// Stop 停止MQTT服务器
func (s *MQTTServer) Stop() {
	if !s.running {
		return
	}

	log.Println("Stopping MQTT server...")
	s.running = false

	// 关闭停止通道，通知所有goroutine停止
	close(s.stopChan)

	// 关闭监听器
	if s.listener != nil {
		s.listener.Close()
	}

	// 关闭所有客户端连接
	s.mu.Lock()
	clientCount := len(s.clients)
	for clientID, client := range s.clients {
		if client.conn != nil {
			client.conn.Close()
		}
		log.Printf("Closed connection for client: %s", clientID)
	}
	// 清空客户端映射
	s.clients = make(map[string]*MQTTConnection)
	s.mu.Unlock()

	// 清理其他资源
	s.securityMu.Lock()
	s.blacklist = make(map[string]*BlacklistEntry)
	s.failedAuth = make(map[string]*FailedAuthEntry)
	s.securityMu.Unlock()

	s.retainedMu.Lock()
	s.retainedMessages = make(map[string]*RetainedMessage)
	s.retainedMu.Unlock()

	s.willMu.Lock()
	s.willMessages = make(map[string]*WillMessage)
	s.willMu.Unlock()

	log.Printf("MQTT server stopped successfully. Closed %d client connections.", clientCount)
}

// parseConnectPacket 解析CONNECT包中的客户端ID、用户名、密码和遗嘱信息
func (c *MQTTConnection) parseConnectPacket(packet *MQTTPacket) (string, string, string, error) {
	log.Printf("MQTT Parse: Packet length=%d", len(packet.Payload))

	if len(packet.Payload) < 10 {
		return "", "", "", fmt.Errorf("invalid CONNECT packet")
	}

	// 解析协议名长度
	if len(packet.Payload) < 2 {
		return "", "", "", fmt.Errorf("invalid CONNECT packet")
	}
	protocolNameLen := int(packet.Payload[0])<<8 | int(packet.Payload[1])
	log.Printf("MQTT Parse: Protocol name length=%d", protocolNameLen)

	// 跳过协议名长度字段 + 协议名 + 协议版本
	offset := 2 + protocolNameLen + 1
	if offset >= len(packet.Payload) {
		return "", "", "", fmt.Errorf("invalid CONNECT packet")
	}

	// 读取连接标志
	connectFlags := packet.Payload[offset]
	willFlag := connectFlags&0x04 != 0
	willQoS := int((connectFlags & 0x18) >> 3)
	willRetain := connectFlags&0x20 != 0
	usernameFlag := connectFlags&0x80 != 0
	passwordFlag := connectFlags&0x40 != 0

	log.Printf("MQTT Parse: Connect flags=0x%02x, will_flag=%t, will_qos=%d, will_retain=%t, username_flag=%t, password_flag=%t",
		connectFlags, willFlag, willQoS, willRetain, usernameFlag, passwordFlag)
	offset++

	// 读取保持连接时间
	if offset+2 > len(packet.Payload) {
		return "", "", "", fmt.Errorf("invalid keep alive in CONNECT packet")
	}
	keepAlive := int(packet.Payload[offset])<<8 | int(packet.Payload[offset+1])
	log.Printf("MQTT Parse: Keep alive=%d seconds", keepAlive)

	// 设置客户端的keepAlive间隔
	c.mu.Lock()
	if keepAlive > 0 {
		c.keepAliveInterval = time.Duration(keepAlive) * time.Second
		c.adaptiveInterval = c.keepAliveInterval
	} else {
		c.keepAliveInterval = 30 * time.Second // 默认30秒
		c.adaptiveInterval = c.keepAliveInterval
	}
	c.mu.Unlock()

	offset += 2

	// 解析客户端标识符
	if offset+2 > len(packet.Payload) {
		return "", "", "", fmt.Errorf("invalid CONNECT packet")
	}
	clientIDLen := int(packet.Payload[offset])<<8 | int(packet.Payload[offset+1])
	log.Printf("MQTT Parse: Client ID length=%d", clientIDLen)
	offset += 2
	if offset+clientIDLen > len(packet.Payload) {
		return "", "", "", fmt.Errorf("invalid client ID length in CONNECT packet")
	}
	clientID := string(packet.Payload[offset : offset+clientIDLen])
	log.Printf("MQTT Parse: Client ID='%s'", clientID)
	offset += clientIDLen

	// 解析遗嘱主题和消息（如果设置了will flag）
	if willFlag {
		// 解析遗嘱主题
		if offset+2 > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid will topic in CONNECT packet")
		}
		willTopicLen := int(packet.Payload[offset])<<8 | int(packet.Payload[offset+1])
		offset += 2
		if offset+willTopicLen > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid will topic length in CONNECT packet")
		}
		c.willTopic = string(packet.Payload[offset : offset+willTopicLen])
		offset += willTopicLen

		// 解析遗嘱消息
		if offset+2 > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid will message in CONNECT packet")
		}
		willMessageLen := int(packet.Payload[offset])<<8 | int(packet.Payload[offset+1])
		offset += 2
		if offset+willMessageLen > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid will message length in CONNECT packet")
		}
		c.willMessage = string(packet.Payload[offset : offset+willMessageLen])
		offset += willMessageLen

		// 设置遗嘱参数
		c.willQoS = willQoS
		c.willRetain = willRetain
		c.hasWill = true

		log.Printf("MQTT Parse: Will - topic='%s', message='%s', qos=%d, retain=%t",
			c.willTopic, c.willMessage, c.willQoS, c.willRetain)
	} else {
		c.hasWill = false
	}

	username := ""
	password := ""

	// 检查用户名标志
	if usernameFlag {
		log.Printf("MQTT Parse: Parsing username, offset=%d", offset)
		if offset+2 > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid username in CONNECT packet")
		}
		usernameLen := int(packet.Payload[offset])<<8 | int(packet.Payload[offset+1])
		log.Printf("MQTT Parse: Username length=%d", usernameLen)
		offset += 2
		if offset+usernameLen > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid username length in CONNECT packet")
		}
		username = string(packet.Payload[offset : offset+usernameLen])
		log.Printf("MQTT Parse: Username='%s'", username)
		offset += usernameLen
	}

	// 检查密码标志
	if passwordFlag {
		log.Printf("MQTT Parse: Parsing password, offset=%d", offset)
		if offset+2 > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid password in CONNECT packet")
		}
		passwordLen := int(packet.Payload[offset])<<8 | int(packet.Payload[offset+1])
		log.Printf("MQTT Parse: Password length=%d", passwordLen)
		offset += 2
		if offset+passwordLen > len(packet.Payload) {
			return "", "", "", fmt.Errorf("invalid password length in CONNECT packet")
		}
		password = string(packet.Payload[offset : offset+passwordLen])
		log.Printf("MQTT Parse: Password='%s'", password)
	}

	return clientID, username, password, nil
}

// sendConnack 发送CONNACK响应
func (c *MQTTConnection) sendConnack(returnCode byte) error {
	connack := []byte{CONNACK, 0x02, 0x00, returnCode}
	_, err := c.conn.Write(connack)
	return err
}

// authenticate 验证用户身份
func (c *MQTTConnection) authenticate(username, password string) bool {
	if username == "" && password == "" {
		return c.server.config.AllowAnonymous
	}

	// 解析用户名，支持两种格式：
	// 1. 普通用户名：直接使用用户账户认证
	// 2. 用户名@设备ID：使用设备认证
	var actualUsername, deviceID string
	if strings.Contains(username, "@") {
		parts := strings.SplitN(username, "@", 2)
		if len(parts) == 2 {
			actualUsername = parts[0]
			deviceID = parts[1]
			log.Printf("MQTT Auth: Device authentication mode - user: %s, device: %s", actualUsername, deviceID)
		} else {
			log.Printf("MQTT Auth: Invalid username format: %s", username)
			return false
		}
	} else {
		actualUsername = username
		log.Printf("MQTT Auth: User authentication mode - user: %s", actualUsername)
	}

	// 获取用户信息
	user, err := GetUserByUsername(actualUsername)
	if err != nil {
		log.Printf("MQTT Auth: User '%s' not found", actualUsername)
		return false
	}

	// 基础认证检查
	if !ValidateUserPassword(user, password) || !user.IsActive {
		log.Printf("MQTT Auth: Authentication failed for user '%s' - invalid password or inactive", actualUsername)
		return false
	}

	// 检查MQTT登录权限
	if !user.CanLoginMqtt {
		log.Printf("MQTT Auth: User '%s' does not have MQTT login permission", actualUsername)
		return false
	}

	// 如果是设备认证模式，需要验证设备
	if deviceID != "" {
		// 验证设备是否存在且属于该用户
		device, err := GetDeviceByDeviceID(deviceID)
		if err != nil {
			// 设备不存在，尝试自动创建
			log.Printf("MQTT Auth: Device '%s' not found, attempting to auto-create", deviceID)
			if autoErr := AutoCreateDeviceOnMQTTLogin(deviceID, actualUsername); autoErr != nil {
				log.Printf("MQTT Auth: Failed to auto-create device '%s': %v", deviceID, autoErr)
				return false
			}

			// 重新获取设备信息
			device, err = GetDeviceByDeviceID(deviceID)
			if err != nil {
				log.Printf("MQTT Auth: Device '%s' still not found after auto-creation", deviceID)
				return false
			}
		}

		if device.UserID != user.ID {
			log.Printf("MQTT Auth: Device '%s' does not belong to user '%s'", deviceID, actualUsername)
			return false
		}

		if !device.IsActive {
			log.Printf("MQTT Auth: Device '%s' is inactive", deviceID)
			return false
		}

		// 检查设备授权
		deviceAuth, err := GetDeviceAuthorization(deviceID)
		if err != nil {
			log.Printf("MQTT Auth: No authorization found for device '%s'", deviceID)
			return false
		}

		if !deviceAuth.IsActive {
			log.Printf("MQTT Auth: Device authorization for '%s' is inactive", deviceID)
			return false
		}

		// 检查设备级别的使用限制
		if deviceAuth.UsageControlEnabled {
			// 检查设备过期时间
			if deviceAuth.ExpireTime != nil && time.Now().After(*deviceAuth.ExpireTime) {
				log.Printf("MQTT Auth: Device '%s' has expired", deviceID)
				return false
			}

			// 检查设备使用次数限制
			if deviceAuth.MaxUsage > 0 && deviceAuth.CurrentUsage >= deviceAuth.MaxUsage {
				log.Printf("MQTT Auth: Device '%s' has exceeded usage limit (%d/%d)", deviceID, deviceAuth.CurrentUsage, deviceAuth.MaxUsage)
				return false
			}
		}

		// 更新设备使用次数（如果启用了使用控制）
		if deviceAuth.UsageControlEnabled {
			if err := UpdateDeviceUsage(deviceID); err != nil {
				log.Printf("MQTT Auth: Failed to update device usage for '%s': %v", deviceID, err)
			} else {
				log.Printf("MQTT Auth: Updated usage count for device '%s'", deviceID)
			}
		}

		// 保存设备ID到连接信息
		c.deviceID = deviceID
		log.Printf("MQTT Auth: Device '%s' authenticated successfully for user '%s'", deviceID, actualUsername)
	} else {
		// 普通用户认证模式，检查用户级别的账户限制
		if user.UsageControlEnabled {
			// 注意：账户过期时间不阻止MQTT登录，过期信息会通过MQTT发送给客户端处理
			// 只检查使用次数限制
			if user.CurrentUsage >= user.MaxUsage {
				log.Printf("MQTT Auth: User '%s' has exceeded usage limit (%d/%d)", actualUsername, user.CurrentUsage, user.MaxUsage)
				return false
			}
		}
		log.Printf("MQTT Auth: User '%s' authenticated successfully", actualUsername)
	}

	// 保存认证信息
	c.username = actualUsername
	return true
}

// isBlacklisted 检查IP是否在黑名单中
func (s *MQTTServer) isBlacklisted(ip string) bool {
	s.securityMu.RLock()
	defer s.securityMu.RUnlock()

	entry, exists := s.blacklist[ip]
	if !exists {
		return false
	}

	// 检查是否已过期
	if time.Now().After(entry.ExpiresAt) {
		// 异步删除过期条目
		go func() {
			s.securityMu.Lock()
			delete(s.blacklist, ip)
			s.securityMu.Unlock()
		}()
		return false
	}

	return true
}

// recordFailedAuth 记录认证失败
func (s *MQTTServer) recordFailedAuth(ip, username string) {
	s.securityMu.Lock()
	defer s.securityMu.Unlock()

	entry, exists := s.failedAuth[ip]
	if !exists {
		entry = &FailedAuthEntry{
			IP:          ip,
			Count:       0,
			LastAttempt: time.Now(),
			Username:    username,
		}
		s.failedAuth[ip] = entry
	}

	entry.Count++
	entry.LastAttempt = time.Now()
	entry.Username = username

	log.Printf("Failed authentication from %s (user: %s), count: %d", ip, username, entry.Count)

	// 检查是否达到阈值
	if entry.Count >= s.config.MaxFailedAttempts {
		s.addToBlacklist(ip, username, "Too many failed authentication attempts")
		delete(s.failedAuth, ip) // 清除失败记录
	}
}

// addToBlacklist 添加到黑名单
func (s *MQTTServer) addToBlacklist(ip, username, reason string) {
	expiresAt := time.Now().Add(time.Duration(s.config.BlacklistDuration) * time.Hour)

	entry := &BlacklistEntry{
		IP:        ip,
		Reason:    reason,
		AddedAt:   time.Now(),
		ExpiresAt: expiresAt,
		Username:  username,
	}

	s.blacklist[ip] = entry
	log.Printf("Added %s to blacklist (user: %s, reason: %s, expires: %s)", ip, username, reason, expiresAt.Format("2006-01-02 15:04:05"))
}

// securityCleaner 定期清理过期的黑名单和失败记录
func (s *MQTTServer) securityCleaner() {
	ticker := time.NewTicker(time.Duration(s.config.CleanupInterval) * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.cleanupExpiredEntries()
		}
	}
}

// cleanupExpiredEntries 清理过期条目
func (s *MQTTServer) cleanupExpiredEntries() {
	s.securityMu.Lock()
	defer s.securityMu.Unlock()

	now := time.Now()

	// 清理过期黑名单条目
	for ip, entry := range s.blacklist {
		if now.After(entry.ExpiresAt) {
			delete(s.blacklist, ip)
			log.Printf("Removed expired blacklist entry for %s", ip)
		}
	}

	// 清理过期失败记录（超过1小时）
	for ip, entry := range s.failedAuth {
		if now.Sub(entry.LastAttempt) > time.Hour {
			delete(s.failedAuth, ip)
			log.Printf("Removed expired failed auth entry for %s", ip)
		}
	}
}

// UpdateConfig 更新MQTT配置
func (s *MQTTServer) UpdateConfig(config *MQTTConfig) {
	s.securityMu.Lock()
	defer s.securityMu.Unlock()

	s.config = config

	// 保存配置到数据库
	configs := []struct {
		key, value, valueType, description string
	}{
		{"require_auth", fmt.Sprintf("%t", config.RequireAuth), "bool", "是否需要认证"},
		{"allow_anonymous", fmt.Sprintf("%t", config.AllowAnonymous), "bool", "是否允许匿名连接"},
		{"max_failed_attempts", fmt.Sprintf("%d", config.MaxFailedAttempts), "int", "最大失败认证次数"},
		{"blacklist_duration", fmt.Sprintf("%d", config.BlacklistDuration), "int", "黑名单时长（小时）"},
		{"cleanup_interval", fmt.Sprintf("%d", config.CleanupInterval), "int", "清理间隔（分钟）"},
		{"heartbeat_interval", fmt.Sprintf("%d", config.HeartbeatInterval), "int", "心跳检查间隔（秒）"},
		{"heartbeat_timeout", fmt.Sprintf("%d", config.HeartbeatTimeout), "int", "心跳超时时间（秒）"},
		{"heartbeat_packet_interval", fmt.Sprintf("%d", config.HeartbeatPacketInterval), "int", "心跳包间隔（毫秒）"},
	}

	for _, cfg := range configs {
		if err := SetConfig("mqtt", cfg.key, cfg.value, cfg.valueType, cfg.description); err != nil {
			log.Printf("Failed to save MQTT config %s to database: %v", cfg.key, err)
		}
	}

	log.Printf("MQTT configuration updated: %+v", config)
}

// RemoveFromBlacklist 从黑名单移除IP
func (s *MQTTServer) RemoveFromBlacklist(ip string) bool {
	s.securityMu.Lock()
	defer s.securityMu.Unlock()

	_, exists := s.blacklist[ip]
	if exists {
		delete(s.blacklist, ip)
		log.Printf("Removed %s from blacklist", ip)
		return true
	}
	return false
}

// AddToBlacklistManually 手动添加到黑名单
func (s *MQTTServer) AddToBlacklistManually(ip, username, reason string, hours int) {
	s.securityMu.Lock()
	defer s.securityMu.Unlock()

	expiresAt := time.Now().Add(time.Duration(hours) * time.Hour)
	entry := &BlacklistEntry{
		IP:        ip,
		Reason:    reason,
		AddedAt:   time.Now(),
		ExpiresAt: expiresAt,
		Username:  username,
	}

	s.blacklist[ip] = entry
	log.Printf("Manually added %s to blacklist (user: %s, reason: %s, expires: %s)",
		ip, username, reason, expiresAt.Format("2006-01-02 15:04:05"))
}

// ClearFailedAuth 清除失败认证记录
func (s *MQTTServer) ClearFailedAuth(ip string) bool {
	s.securityMu.Lock()
	defer s.securityMu.Unlock()

	_, exists := s.failedAuth[ip]
	if exists {
		delete(s.failedAuth, ip)
		log.Printf("Cleared failed auth record for %s", ip)
		return true
	}
	return false
}

// AddRetainedMessage 添加保持消息
func (s *MQTTServer) AddRetainedMessage(topic, message string, qos int) {
	s.retainedMu.Lock()
	defer s.retainedMu.Unlock()

	if message == "" {
		// 空消息表示删除保持消息
		delete(s.retainedMessages, topic)
		// 从数据库中删除
		if err := DeleteRetainedMessageFromDB(topic); err != nil {
			log.Printf("Failed to delete retained message from database: %v", err)
		}
		log.Printf("Removed retained message for topic %s", topic)
		return
	}

	// 保存到内存
	s.retainedMessages[topic] = &RetainedMessage{
		Topic:     topic,
		Message:   message,
		QoS:       qos,
		Timestamp: time.Now(),
	}

	// 保存到数据库
	if err := SaveRetainedMessage(topic, message, qos); err != nil {
		log.Printf("Failed to save retained message to database: %v", err)
	}

	log.Printf("Added retained message for topic %s", topic)
}

// GetRetainedMessages 获取所有保持消息
func (s *MQTTServer) GetRetainedMessages() []*RetainedMessage {
	s.retainedMu.RLock()
	defer s.retainedMu.RUnlock()

	messages := make([]*RetainedMessage, 0, len(s.retainedMessages))
	for _, msg := range s.retainedMessages {
		messages = append(messages, msg)
	}
	return messages
}

// DeleteRetainedMessage 删除指定主题的保持消息
func (s *MQTTServer) DeleteRetainedMessage(topic string) bool {
	s.retainedMu.Lock()
	defer s.retainedMu.Unlock()

	_, exists := s.retainedMessages[topic]
	if exists {
		delete(s.retainedMessages, topic)
		// 从数据库中删除
		if err := DeleteRetainedMessageFromDB(topic); err != nil {
			log.Printf("Failed to delete retained message from database: %v", err)
		}
		log.Printf("Deleted retained message for topic %s", topic)
		return true
	}
	return false
}

// ClearAllRetainedMessages 清空所有保持消息
func (s *MQTTServer) ClearAllRetainedMessages() int {
	s.retainedMu.Lock()
	defer s.retainedMu.Unlock()

	count := len(s.retainedMessages)
	s.retainedMessages = make(map[string]*RetainedMessage)

	// 从数据库中清空
	if dbCount, err := ClearAllRetainedMessagesFromDB(); err != nil {
		log.Printf("Failed to clear retained messages from database: %v", err)
	} else {
		log.Printf("Cleared %d retained messages from database", dbCount)
	}

	log.Printf("Cleared all %d retained messages", count)
	return count
}

// sendRetainedMessages 向客户端发送匹配的保持消息
func (c *MQTTConnection) sendRetainedMessages(subscribedTopic string) {
	// 检查客户端状态
	if !c.authenticated || c.conn == nil {
		log.Printf("Client %s: Not sending retained messages - client not authenticated or connection closed", c.ID)
		return
	}

	c.server.retainedMu.RLock()
	defer c.server.retainedMu.RUnlock()

	log.Printf("Client %s: Checking retained messages for subscription to %s", c.ID, subscribedTopic)
	log.Printf("Total retained messages: %d", len(c.server.retainedMessages))

	for topic, retainedMsg := range c.server.retainedMessages {
		log.Printf("Checking retained message: topic=%s, message=%s", topic, retainedMsg.Message)
		// 检查主题是否匹配（支持简单通配符匹配）
		if c.topicMatches(subscribedTopic, topic) {
			log.Printf("Topic match found: %s matches %s", subscribedTopic, topic)
			// 构建PUBLISH消息
			messageBytes := []byte(retainedMsg.Message)
			topicBytes := []byte(topic)
			topicLen := len(topicBytes)

			payload := make([]byte, 2+topicLen+len(messageBytes))
			payload[0] = byte(topicLen >> 8)
			payload[1] = byte(topicLen & 0xFF)
			copy(payload[2:2+topicLen], topicBytes)
			copy(payload[2+topicLen:], messageBytes)

			// 设置RETAIN标志位 (MQTT协议中RETAIN标志位在第0位)
			publishType := PUBLISH | 0x01 // 设置RETAIN标志

			// 使用简单长度编码（与broadcastMessage保持一致）
			packet := []byte{byte(publishType), byte(len(payload))}
			packet = append(packet, payload...)

			// 同步发送给客户端（避免在连接关闭后发送）
			if c.authenticated && c.conn != nil {
				_, err := c.conn.Write(packet)
				if err != nil {
					log.Printf("Failed to send retained message to client %s: %v", c.ID, err)
				} else {
					log.Printf("Sent retained message to client %s for topic %s", c.ID, topic)
				}
			}
		}
	}
}

// topicMatches 检查主题是否匹配（支持MQTT通配符）
func (c *MQTTConnection) topicMatches(subscribedTopic, messageTopic string) bool {
	// 完全匹配
	if subscribedTopic == messageTopic {
		return true
	}

	// 支持 '+' 单级通配符和 '#' 多级通配符
	return c.matchTopic(subscribedTopic, messageTopic)
}

// matchTopic MQTT主题匹配算法
func (c *MQTTConnection) matchTopic(pattern, topic string) bool {
	// 支持基本的MQTT通配符匹配
	// + 匹配单级
	// # 匹配多级（必须在末尾）

	// 处理多级通配符 #
	if strings.Contains(pattern, "#") {
		hashIndex := strings.Index(pattern, "#")
		// # 必须在末尾并且前面必须是 / 或者是开头
		if hashIndex == len(pattern)-1 {
			prefix := pattern[:hashIndex]
			if prefix == "" {
				return true // 单独的 # 匹配所有主题
			}
			if strings.HasSuffix(prefix, "/") {
				return strings.HasPrefix(topic, prefix)
			}
		}
		return false
	}

	// 精确匹配或 + 通配符匹配
	patternParts := strings.Split(pattern, "/")
	topicParts := strings.Split(topic, "/")

	// 如果层级数不同且没有多级通配符，则不匹配
	if len(patternParts) != len(topicParts) {
		return false
	}

	// 逐级比较
	for i, patternPart := range patternParts {
		if patternPart != "+" && patternPart != topicParts[i] {
			return false
		}
	}

	return true
}

// loadRetainedMessagesFromDB 从数据库加载保持消息到内存
func (s *MQTTServer) loadRetainedMessagesFromDB() {
	s.retainedMu.Lock()
	defer s.retainedMu.Unlock()

	messages, err := LoadRetainedMessagesFromDB()
	if err != nil {
		log.Printf("Failed to load retained messages from database: %v", err)
		return
	}

	s.retainedMessages = messages
	log.Printf("Loaded %d retained messages from database", len(messages))
}

// GetAllWillMessages 获取所有遗嘱消息（内存版本）
func (s *MQTTServer) GetAllWillMessages() []*WillMessage {
	s.willMu.RLock()
	defer s.willMu.RUnlock()

	// 更新连接状态
	s.mu.RLock()
	onlineClientIds := make(map[string]bool)
	for clientID := range s.clients {
		onlineClientIds[clientID] = true
	}
	s.mu.RUnlock()

	wills := make([]*WillMessage, 0, len(s.willMessages))
	for _, will := range s.willMessages {
		// 创建副本并更新连接状态
		willCopy := *will
		willCopy.IsConnected = onlineClientIds[will.ClientID]
		wills = append(wills, &willCopy)
	}

	return wills
}

// DeleteWillMessage 删除遗嘱消息（内存版本）
func (s *MQTTServer) DeleteWillMessage(clientID string) bool {
	s.willMu.Lock()
	defer s.willMu.Unlock()

	if _, exists := s.willMessages[clientID]; exists {
		delete(s.willMessages, clientID)
		log.Printf("Will message deleted from memory for client %s", clientID)
		return true
	}
	return false
}

// GetWillMessage 获取指定客户端遗嘱消息（内存版本）
func (s *MQTTServer) GetWillMessage(clientID string) (*WillMessage, bool) {
	s.willMu.RLock()
	defer s.willMu.RUnlock()

	if will, exists := s.willMessages[clientID]; exists {
		// 更新连接状态
		s.mu.RLock()
		_, isConnected := s.clients[clientID]
		s.mu.RUnlock()

		willCopy := *will
		willCopy.IsConnected = isConnected
		return &willCopy, true
	}
	return nil, false
}

// triggerWillMessage 触发遗嘱消息发布
func (s *MQTTServer) triggerWillMessage(clientID, topic, message string, qos int, retain bool) {
	if topic == "" || message == "" {
		log.Printf("Invalid will message: empty topic or message for client %s", clientID)
		return
	}

	log.Printf("Triggering will message for client %s: topic='%s', message='%s', qos=%d, retain=%t",
		clientID, topic, message, qos, retain)

	// 如果设置了retain标志，保存为保持消息
	if retain {
		s.AddRetainedMessage(topic, message, qos)
	}

	// 构建PUBLISH消息包
	messageBytes := []byte(message)
	topicBytes := []byte(topic)
	topicLen := len(topicBytes)

	// 构建负载：主题长度(2字节) + 主题 + 消息内容
	payload := make([]byte, 2+topicLen+len(messageBytes))
	payload[0] = byte(topicLen >> 8)
	payload[1] = byte(topicLen & 0xFF)
	copy(payload[2:2+topicLen], topicBytes)
	copy(payload[2+topicLen:], messageBytes)

	// 设置PUBLISH包类型，根据QoS和Retain标志设置
	publishType := PUBLISH
	if retain {
		publishType |= 0x01 // 设置RETAIN标志
	}
	// QoS标志设置在bit 1-2
	publishType |= (qos & 0x03) << 1

	// 构建完整的MQTT包：消息类型 + 剩余长度 + 负载
	packet := []byte{byte(publishType), byte(len(payload))}
	packet = append(packet, payload...)

	// 获取订阅了该主题的客户端列表
	s.mu.RLock()
	subscribers, exists := s.topics[topic]
	if !exists {
		s.mu.RUnlock()
		log.Printf("Will message not delivered: no subscribers for topic '%s'", topic)
		return
	}

	// 获取有效的订阅客户端连接
	validClients := make([]*MQTTConnection, 0)
	for _, subscriberID := range subscribers {
		if subscriberID == clientID {
			// 跳过触发遗嘱的客户端自己
			continue
		}
		if client, exists := s.clients[subscriberID]; exists && client.authenticated {
			validClients = append(validClients, client)
		}
	}
	s.mu.RUnlock()

	// 并发发送遗嘱消息给所有订阅者
	deliveredCount := 0
	for _, client := range validClients {
		go func(c *MQTTConnection) {
			c.mu.RLock()
			defer c.mu.RUnlock()

			if c.conn != nil {
				_, err := c.conn.Write(packet)
				if err != nil {
					log.Printf("Failed to deliver will message to client %s: %v", c.ID, err)
				} else {
					log.Printf("Will message delivered to client %s", c.ID)
				}
			}
		}(client)
		deliveredCount++
	}

	if deliveredCount > 0 {
		log.Printf("Will message from client %s delivered to %d subscribers of topic '%s'",
			clientID, deliveredCount, topic)
	} else {
		log.Printf("Will message from client %s not delivered: no active subscribers for topic '%s'",
			clientID, topic)
	}

	// 记录遗嘱消息发布操作到日志
	LogOperation(nil, "WILL_MESSAGE_PUBLISHED", "MQTT",
		fmt.Sprintf("客户端 %s 的遗嘱消息已发布到主题 %s", clientID, topic),
		clientID, topic, true, "", "", "", "", "")

	// 遗嘱消息发布后，从内存中删除遗嘱记录
	s.willMu.Lock()
	delete(s.willMessages, clientID)
	s.willMu.Unlock()
	log.Printf("Will message record deleted from memory after delivery for client %s", clientID)
}

// statsUpdater 定期广播MQTT统计信息更新
func (s *MQTTServer) statsUpdater() {
	ticker := time.NewTicker(5 * time.Second) // 每5秒更新一次
	defer ticker.Stop()

	for {
		select {
		case <-s.stopChan:
			return
		case <-ticker.C:
			s.broadcastStatsUpdate()
		}
	}
}

// broadcastStatsUpdate 广播统计信息更新
func (s *MQTTServer) broadcastStatsUpdate() {
	s.mu.RLock()
	clientCount := len(s.clients)
	topicCount := len(s.topics)

	// 获取客户端列表 - 只包含活跃连接的客户端
	clientList := make([]map[string]interface{}, 0, len(s.clients))
	activeClientCount := 0
	for _, client := range s.clients {
		client.mu.RLock()
		// 检查客户端是否仍然活跃（连接未关闭）
		isActive := client.conn != nil
		if isActive {
			clientInfo := map[string]interface{}{
				"id":                client.ID,
				"username":          client.username,
				"client_ip":         client.clientIP,
				"authenticated":     client.authenticated,
				"topics":            len(client.topics),
				"subscribed_topics": client.topics,               // 添加订阅的主题列表
				"last_ping":         client.lastPing.UnixMilli(), // 使用毫秒时间戳提高精度
			}
			clientList = append(clientList, clientInfo)
			activeClientCount++
		}
		client.mu.RUnlock()
	}

	// 使用活跃客户端数量而不是总数量
	clientCount = activeClientCount

	// 获取主题列表 - 基于活跃客户端重新构建，避免延迟清理问题
	topicSet := make(map[string]bool)
	for _, client := range s.clients {
		client.mu.RLock()
		// 只统计活跃连接的客户端的主题
		if client.conn != nil {
			for _, topic := range client.topics {
				topicSet[topic] = true
			}
		}
		client.mu.RUnlock()
	}

	// 转换为切片
	topicList := make([]string, 0, len(topicSet))
	for topic := range topicSet {
		topicList = append(topicList, topic)
	}
	s.mu.RUnlock()

	s.securityMu.RLock()
	blacklistCount := len(s.blacklist)
	failedAuthCount := len(s.failedAuth)

	// 获取黑名单列表
	blacklist := make([]map[string]interface{}, 0, len(s.blacklist))
	for ip, entry := range s.blacklist {
		blacklist = append(blacklist, map[string]interface{}{
			"ip":         ip,
			"reason":     entry.Reason,
			"added_at":   entry.AddedAt.Unix(),
			"expires_at": entry.ExpiresAt.Unix(),
			"username":   entry.Username,
		})
	}

	// 获取失败认证列表
	failedAuth := make([]map[string]interface{}, 0, len(s.failedAuth))
	for ip, entry := range s.failedAuth {
		failedAuth = append(failedAuth, map[string]interface{}{
			"ip":           ip,
			"count":        entry.Count,
			"last_attempt": entry.LastAttempt.Unix(),
			"username":     entry.Username,
		})
	}
	s.securityMu.RUnlock()

	stats := map[string]interface{}{
		"port":              s.Port,
		"running":           s.running,
		"clients":           clientCount,
		"topics":            topicCount,
		"blacklist_count":   blacklistCount,
		"failed_auth_count": failedAuthCount,
		"client_list":       clientList,
		"topic_list":        topicList,
		"blacklist":         blacklist,
		"failed_auth":       failedAuth,
		"config":            s.config,
	}

	s.broadcastMQTTEvent("mqtt_stats_updated", map[string]interface{}{
		"stats":     stats,
		"timestamp": time.Now().Unix(),
	})
}

// updateHeartbeatStats 更新心跳包统计信息
func (c *MQTTConnection) updateHeartbeatStats(interval time.Duration) {
	c.heartbeatStats.TotalHeartbeats++

	// 计算延迟（如果有上次的PINGRESP时间）
	if !c.lastPingResponse.IsZero() {
		latency := c.lastPingRequest.Sub(c.lastPingResponse)
		if latency > 0 {
			c.heartbeatStats.LastLatency = latency

			// 更新最大最小延迟
			if c.heartbeatStats.MaxLatency == 0 || latency > c.heartbeatStats.MaxLatency {
				c.heartbeatStats.MaxLatency = latency
			}
			if c.heartbeatStats.MinLatency == 0 || latency < c.heartbeatStats.MinLatency {
				c.heartbeatStats.MinLatency = latency
			}

			// 添加到历史记录
			c.heartbeatStats.LatencyHistory = append(c.heartbeatStats.LatencyHistory, latency)
			if len(c.heartbeatStats.LatencyHistory) > 10 {
				c.heartbeatStats.LatencyHistory = c.heartbeatStats.LatencyHistory[1:]
			}

			// 计算平均延迟
			var total time.Duration
			for _, l := range c.heartbeatStats.LatencyHistory {
				total += l
			}
			c.heartbeatStats.AverageLatency = total / time.Duration(len(c.heartbeatStats.LatencyHistory))
		}
	}
}

// assessNetworkQuality 评估网络质量
func (c *MQTTConnection) assessNetworkQuality(interval time.Duration) {
	now := time.Now()

	// 基于心跳间隔稳定性评估
	expectedInterval := c.getExpectedInterval()
	deviation := float64(interval-expectedInterval) / float64(expectedInterval)
	if deviation < 0 {
		deviation = -deviation
	}

	// 基于延迟评估
	latencyScore := 1.0
	if c.heartbeatStats.LastLatency > 0 {
		// 延迟越低分数越高
		latencyMs := float64(c.heartbeatStats.LastLatency.Milliseconds())
		if latencyMs > 1000 {
			latencyScore = 0.1
		} else if latencyMs > 500 {
			latencyScore = 0.5
		} else if latencyMs > 100 {
			latencyScore = 0.8
		}
	}

	// 基于丢包率评估
	lossScore := 1.0
	if c.heartbeatStats.TotalHeartbeats > 0 {
		lossRate := float64(c.heartbeatStats.MissedHeartbeats) / float64(c.heartbeatStats.TotalHeartbeats)
		lossScore = 1.0 - lossRate
		c.networkQuality.PacketLossRate = lossRate
	}

	// 综合评分
	stabilityScore := 1.0 - deviation
	if stabilityScore < 0 {
		stabilityScore = 0
	}

	c.networkQuality.Score = (stabilityScore + latencyScore + lossScore) / 3.0
	c.networkQuality.AverageRTT = c.heartbeatStats.AverageLatency
	c.networkQuality.LastUpdated = now
}

// adjustAdaptiveInterval 自适应调整心跳间隔
func (c *MQTTConnection) adjustAdaptiveInterval() {
	baseInterval := c.keepAliveInterval
	if baseInterval == 0 {
		baseInterval = 30 * time.Second // 默认30秒
	}

	// 根据网络质量调整
	qualityFactor := c.networkQuality.Score
	if qualityFactor < 0.3 {
		// 网络质量差，增加心跳频率
		c.adaptiveInterval = time.Duration(float64(baseInterval) * 0.7)
	} else if qualityFactor > 0.8 {
		// 网络质量好，可以适当降低心跳频率
		c.adaptiveInterval = time.Duration(float64(baseInterval) * 1.2)
	} else {
		// 网络质量一般，保持默认
		c.adaptiveInterval = baseInterval
	}

	// 限制调整范围
	minInterval := 10 * time.Second
	maxInterval := 120 * time.Second
	if c.adaptiveInterval < minInterval {
		c.adaptiveInterval = minInterval
	}
	if c.adaptiveInterval > maxInterval {
		c.adaptiveInterval = maxInterval
	}
}

// getExpectedInterval 获取期望的心跳间隔
func (c *MQTTConnection) getExpectedInterval() time.Duration {
	// 优先使用客户端声明的Keep Alive间隔
	if c.keepAliveInterval > 0 {
		return c.keepAliveInterval
	}
	// 如果客户端没有设置，使用自适应间隔
	if c.adaptiveInterval > 0 {
		return c.adaptiveInterval
	}
	return 30 * time.Second // 默认30秒
}

// initializeHeartbeatStats 初始化心跳统计
func (c *MQTTConnection) initializeHeartbeatStats() {
	c.heartbeatStats = HeartbeatStats{
		LatencyHistory: make([]time.Duration, 0, 10),
	}
	c.networkQuality = NetworkQuality{
		Score:       1.0, // 初始假设网络质量良好
		LastUpdated: time.Now(),
	}
	c.connectedAt = time.Now()
	c.adaptiveInterval = 30 * time.Second // 默认30秒
}

// generateHeartbeatDiagnostics 生成心跳包诊断信息
func (c *MQTTConnection) generateHeartbeatDiagnostics(actualInterval time.Duration) HeartbeatDiagnostics {
	expectedInterval := c.getExpectedInterval()
	deviation := float64(actualInterval-expectedInterval) / float64(expectedInterval) * 100
	if deviation < 0 {
		deviation = -deviation
	}

	// 判断是否异常 - 基于合理范围（1-60秒）
	const minReasonableInterval = 1 * time.Second
	const maxReasonableInterval = 60 * time.Second

	// 主要基于合理范围判断，而不是期望间隔
	isAbnormal := actualInterval < minReasonableInterval || actualInterval > maxReasonableInterval

	// 计算稳定性
	stabilityLevel := "STABLE"
	if deviation > 50 {
		stabilityLevel = "VERY_UNSTABLE"
	} else if deviation > 20 {
		stabilityLevel = "UNSTABLE"
	} else if deviation > 10 {
		stabilityLevel = "SLIGHTLY_UNSTABLE"
	}

	// 分析趋势
	trend := "NORMAL"
	if len(c.heartbeatStats.LatencyHistory) >= 3 {
		recent := c.heartbeatStats.LatencyHistory[len(c.heartbeatStats.LatencyHistory)-3:]
		if recent[2] > recent[1] && recent[1] > recent[0] {
			trend = "INCREASING"
		} else if recent[2] < recent[1] && recent[1] < recent[0] {
			trend = "DECREASING"
		} else {
			trend = "FLUCTUATING"
		}
	}

	// 计算抖动
	jitter := time.Duration(0)
	if len(c.heartbeatStats.LatencyHistory) >= 2 {
		var jitterSum time.Duration
		for i := 1; i < len(c.heartbeatStats.LatencyHistory); i++ {
			diff := c.heartbeatStats.LatencyHistory[i] - c.heartbeatStats.LatencyHistory[i-1]
			if diff < 0 {
				diff = -diff
			}
			jitterSum += diff
		}
		jitter = jitterSum / time.Duration(len(c.heartbeatStats.LatencyHistory)-1)
	}

	// 生成分析和建议
	analysis, likelyCause, recommendation := c.analyzeHeartbeatPattern(actualInterval, expectedInterval, deviation)

	return HeartbeatDiagnostics{
		ExpectedInterval: expectedInterval,
		ActualInterval:   actualInterval,
		DeviationPercent: deviation,
		IsAbnormal:       isAbnormal,
		StabilityLevel:   stabilityLevel,
		Trend:            trend,
		Jitter:           jitter,
		Analysis:         analysis,
		LikelyCause:      likelyCause,
		Recommendation:   recommendation,
		ClientKeepAlive:  c.keepAliveInterval,
		ServerTimeout:    time.Duration(c.server.config.HeartbeatTimeout) * time.Second,
	}
}

// analyzeHeartbeatPattern 分析心跳包模式
func (c *MQTTConnection) analyzeHeartbeatPattern(actual, expected time.Duration, deviation float64) (analysis, likelyCause, recommendation string) {
	// 使用配置的最小心跳包间隔作为合理范围的下限
	minReasonableInterval := time.Duration(c.server.config.HeartbeatPacketInterval) * time.Millisecond
	const maxReasonableInterval = 60 * time.Second

	// 首先检查是否在合理范围内
	if actual < minReasonableInterval {
		analysis = fmt.Sprintf("心跳间隔过短（<%v），可能影响服务器性能", minReasonableInterval)
		likelyCause = "客户端心跳配置过于激进，或存在逻辑错误"
		recommendation = fmt.Sprintf("建议将心跳间隔调整到%v-60秒范围内", minReasonableInterval)
	} else if actual > maxReasonableInterval {
		analysis = "心跳间隔过长（>60秒），连接检测效果较差"
		likelyCause = "客户端心跳配置过于保守，或网络连接不稳定"
		recommendation = "建议将心跳间隔调整到1-60秒范围内，或检查网络连接"
	} else {
		// 在合理范围内，进一步分析稳定性和一致性
		actualSeconds := actual.Seconds()
		expectedSeconds := expected.Seconds()

		// 计算与客户端声明间隔的偏差
		clientDeviationPercent := math.Abs(actualSeconds-expectedSeconds) / expectedSeconds * 100

		if clientDeviationPercent > 50 {
			analysis = "心跳间隔与客户端声明值偏差较大，但在合理范围内"
			likelyCause = "网络延迟、系统负载波动、或客户端实现不够精确"
			recommendation = "监控网络质量和系统性能，考虑优化客户端心跳实现"
		} else if deviation > 30 && actualSeconds >= 10 {
			// 对于较长间隔，要求更高的稳定性
			analysis = "心跳间隔稳定性一般，存在一定波动"
			likelyCause = "网络抖动或系统负载变化"
			recommendation = "监控网络稳定性，考虑优化网络环境"
		} else if deviation > 50 && actualSeconds < 10 {
			// 短间隔允许更大波动
			analysis = "心跳间隔波动较大，但短间隔下属正常现象"
			likelyCause = "系统调度延迟或网络微小抖动"
			recommendation = "继续监控，短间隔心跳允许适度波动"
		} else {
			analysis = "心跳间隔正常，在合理范围内且稳定"
			likelyCause = "正常运行状态"
			recommendation = "继续监控"
		}
	}

	return analysis, likelyCause, recommendation
}

// cleanupClientSubscriptions 清理客户端的订阅（调用时必须持有s.mu锁）
func (s *MQTTServer) cleanupClientSubscriptions(clientID string) {
	subscribedTopics := make([]string, 0)
	for topic, clients := range s.topics {
		newClients := make([]string, 0)
		for _, id := range clients {
			if id != clientID {
				newClients = append(newClients, id)
			} else {
				subscribedTopics = append(subscribedTopics, topic)
			}
		}
		if len(newClients) > 0 {
			s.topics[topic] = newClients
		} else {
			delete(s.topics, topic)
		}
	}

	if len(subscribedTopics) > 0 {
		log.Printf("Client %s: Cleaned up subscriptions for topics: %v", clientID, subscribedTopics)
	}
}
