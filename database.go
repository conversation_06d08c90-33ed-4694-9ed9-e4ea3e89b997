package main

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"regexp"
	"strconv"
	"strings"
	"time"

	_ "modernc.org/sqlite"
)

var db *sql.DB
var safeDB *SafeDB

// InputValidator 输入验证器
type InputValidator struct {
	maxLengths map[string]int
	patterns   map[string]*regexp.Regexp
}

// NewInputValidator 创建输入验证器
func NewInputValidator() *InputValidator {
	return &InputValidator{
		maxLengths: map[string]int{
			"username": GetConfigInt("validation", "max_username_length", 50),
			"password": GetConfigInt("validation", "max_password_length", 128),
			"phone":    GetConfigInt("validation", "max_phone_length", 20),
			"remark":   GetConfigInt("validation", "max_remark_length", 200),
		},
		patterns: map[string]*regexp.Regexp{
			"username": regexp.MustCompile(`^[a-zA-Z0-9_-]+$`),
			"phone":    regexp.MustCompile(`^[0-9+\-\s()]+$`),
		},
	}
}

// ValidateInput 验证输入
func (v *InputValidator) ValidateInput(field, value string) error {
	if value == "" {
		return nil // 空值由业务逻辑处理
	}

	// 长度验证
	if maxLen, exists := v.maxLengths[field]; exists && len(value) > maxLen {
		return fmt.Errorf("%s exceeds maximum length of %d", field, maxLen)
	}

	// 格式验证
	if pattern, exists := v.patterns[field]; exists && !pattern.MatchString(value) {
		return fmt.Errorf("%s format is invalid", field)
	}

	// XSS防护
	if GetConfigBool("validation", "enable_xss_protection", true) {
		if containsXSS(value) {
			return fmt.Errorf("%s contains potentially dangerous content", field)
		}
	}

	return nil
}

// containsXSS 检查是否包含XSS攻击代码
func containsXSS(input string) bool {
	xssPatterns := []string{
		`<script`,
		`javascript:`,
		`onload=`,
		`onerror=`,
		`onclick=`,
		`onmouseover=`,
		`<iframe`,
		`<object`,
		`<embed`,
		`eval(`,
		`expression(`,
	}

	lowerInput := strings.ToLower(input)
	for _, pattern := range xssPatterns {
		if strings.Contains(lowerInput, pattern) {
			return true
		}
	}
	return false
}

// SafeDB 安全数据库操作封装
type SafeDB struct {
	db        *sql.DB
	validator *InputValidator
}

// NewSafeDB 创建安全数据库操作实例
func NewSafeDB(database *sql.DB) *SafeDB {
	return &SafeDB{
		db:        database,
		validator: NewInputValidator(),
	}
}

// QueryWithValidation 带验证的查询
func (sdb *SafeDB) QueryWithValidation(query string, args ...interface{}) (*sql.Rows, error) {
	// 验证查询语句
	if err := sdb.validateQuery(query); err != nil {
		return nil, err
	}

	return sdb.db.Query(query, args...)
}

// QueryRowWithValidation 带验证的单行查询
func (sdb *SafeDB) QueryRowWithValidation(query string, args ...interface{}) *sql.Row {
	// 这里可以添加查询验证，但为了简化先直接调用
	return sdb.db.QueryRow(query, args...)
}

// ExecWithValidation 带验证的执行
func (sdb *SafeDB) ExecWithValidation(query string, args ...interface{}) (sql.Result, error) {
	// 验证查询语句
	if err := sdb.validateQuery(query); err != nil {
		return nil, err
	}

	return sdb.db.Exec(query, args...)
}

// validateQuery 验证查询语句
func (sdb *SafeDB) validateQuery(query string) error {
	// 检查是否包含危险的SQL关键字组合
	dangerousPatterns := []string{
		`;\s*DROP`,
		`;\s*DELETE`,
		`;\s*UPDATE`,
		`;\s*INSERT`,
		`UNION.*SELECT`,
		`--`,
		`/\*`,
		`\*/`,
	}

	upperQuery := strings.ToUpper(query)
	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, upperQuery); matched {
			return fmt.Errorf("potentially dangerous SQL pattern detected: %s", pattern)
		}
	}

	return nil
}

// User 用户模型
type User struct {
	ID                  int        `json:"id"`
	Username            string     `json:"username"`
	Password            string     `json:"-"`
	Email               string     `json:"email"`
	Phone               string     `json:"phone"`
	Remark              string     `json:"remark"`
	MaxUsage            int        `json:"max_usage"`             // 最大使用次数
	CurrentUsage        int        `json:"current_usage"`         // 当前使用次数
	ExpireTime          *time.Time `json:"expire_time"`           // 到期时间
	UsageControlEnabled bool       `json:"usage_control_enabled"` // 用户级控制开关
	CanLoginBackend     bool       `json:"can_login_backend"`     // 是否可以登录后台
	CanLoginMqtt        bool       `json:"can_login_mqtt"`        // 是否可以登录MQTT
	IsActive            bool       `json:"is_active"`
	CreatedAt           time.Time  `json:"created_at"`
	// 授权信息发送配置
	AccountInfoEncryption bool   `json:"account_info_encryption"` // 是否启用加密
	AccountInfoContent    string `json:"account_info_content"`    // 包含的内容，逗号分隔
}

// Authorization 授权模型
type Authorization struct {
	ID           int       `json:"id"`
	UserID       int       `json:"user_id"`
	UsageCount   int       `json:"usage_count"`
	MaxUsage     int       `json:"max_usage"`
	ExpiresAt    time.Time `json:"expires_at"`
	IsActive     bool      `json:"is_active"`
	EncryptedKey string    `json:"encrypted_key"`
	CreatedAt    time.Time `json:"created_at"`
}

// Device 设备模型
type Device struct {
	ID         int       `json:"id"`
	DeviceID   string    `json:"device_id"`
	DeviceName string    `json:"device_name"`
	UserID     int       `json:"user_id"`
	DeviceType string    `json:"device_type"`
	IsActive   bool      `json:"is_active"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

// DeviceType 设备类型模型
type DeviceType struct {
	ID          int       `json:"id"`
	TypeCode    string    `json:"type_code"`
	TypeName    string    `json:"type_name"`
	Description string    `json:"description"`
	Icon        string    `json:"icon"`
	Color       string    `json:"color"`
	IsActive    bool      `json:"is_active"`
	IsSystem    bool      `json:"is_system"`
	SortOrder   int       `json:"sort_order"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// DeviceAuthorization 设备授权模型
type DeviceAuthorization struct {
	ID                    int        `json:"id"`
	DeviceID              string     `json:"device_id"`
	MaxUsage              int        `json:"max_usage"`
	CurrentUsage          int        `json:"current_usage"`
	ExpireTime            *time.Time `json:"expire_time"`
	UsageControlEnabled   bool       `json:"usage_control_enabled"`
	AccountInfoEncryption bool       `json:"account_info_encryption"`
	AccountInfoContent    string     `json:"account_info_content"`
	IsActive              bool       `json:"is_active"`
	CreatedAt             time.Time  `json:"created_at"`
	UpdatedAt             time.Time  `json:"updated_at"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	Success bool          `json:"success"`
	Token   string        `json:"token"`
	User    User          `json:"user"`
	Auth    Authorization `json:"auth"`
}

// InitDatabase 初始化数据库
func InitDatabase() {
	// 创建db目录（如果不存在）
	if err := os.MkdirAll("./db", 0755); err != nil {
		log.Fatal("Failed to create db directory:", err)
	}

	var err error
	db, err = sql.Open("sqlite", "./db/ipc_management.db")
	if err != nil {
		log.Fatal("Failed to open database:", err)
	}

	// 启用外键约束
	_, err = db.Exec("PRAGMA foreign_keys = ON")
	if err != nil {
		log.Fatal("Failed to enable foreign keys:", err)
	}

	// 设置性能优化参数
	_, err = db.Exec("PRAGMA journal_mode = WAL") // 启用WAL模式提高并发性能
	if err != nil {
		log.Printf("Warning: Failed to enable WAL mode: %v", err)
	}

	_, err = db.Exec("PRAGMA synchronous = NORMAL") // 平衡性能和安全性
	if err != nil {
		log.Printf("Warning: Failed to set synchronous mode: %v", err)
	}

	_, err = db.Exec("PRAGMA cache_size = 10000") // 增加缓存大小
	if err != nil {
		log.Printf("Warning: Failed to set cache size: %v", err)
	}

	// 设置连接池参数 - 从配置读取
	configureDatabasePool(db)

	// 创建用户表
	createUserTable := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT UNIQUE NOT NULL,
		password TEXT NOT NULL,
		email TEXT,
		phone TEXT,
		remark TEXT,
		is_active BOOLEAN DEFAULT 1,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 创建授权表
	createAuthTable := `
	CREATE TABLE IF NOT EXISTS authorizations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id INTEGER NOT NULL,
		usage_count INTEGER DEFAULT 0,
		max_usage INTEGER NOT NULL,
		expires_at DATETIME NOT NULL,
		is_active BOOLEAN DEFAULT 1,
		encrypted_key TEXT NOT NULL,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id)
	);`

	// 创建验证码日志表
	createCaptchaLogTable := `
	CREATE TABLE IF NOT EXISTS captcha_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id INTEGER NOT NULL,
		captcha_id TEXT NOT NULL,
		success BOOLEAN NOT NULL,
		ip_address TEXT,
		user_agent TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id)
	);`

	// 扩展为通用操作日志表
	createOperationLogTable := `
	CREATE TABLE IF NOT EXISTS operation_logs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id INTEGER,
		operation_type TEXT NOT NULL,
		module TEXT NOT NULL,
		operation_detail TEXT,
		target_id TEXT,
		target_name TEXT,
		success BOOLEAN NOT NULL,
		ip_address TEXT,
		user_agent TEXT,
		request_data TEXT,
		response_data TEXT,
		error_message TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id)
	);`

	// 创建MQTT保持消息表
	createRetainedMessagesTable := `
	CREATE TABLE IF NOT EXISTS mqtt_retained_messages (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		topic TEXT UNIQUE NOT NULL,
		message TEXT NOT NULL,
		qos INTEGER DEFAULT 0,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 创建系统配置表
	createSystemConfigTable := `
	CREATE TABLE IF NOT EXISTS system_configs (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		category TEXT NOT NULL,
		key_name TEXT NOT NULL,
		value TEXT NOT NULL,
		value_type TEXT NOT NULL, -- string, int, bool, float
		description TEXT,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		UNIQUE(category, key_name)
	);`

	// 创建备份元数据表
	createBackupMetadataTable := `
	CREATE TABLE IF NOT EXISTS backup_metadata (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		filename TEXT NOT NULL UNIQUE,
		type TEXT NOT NULL,
		description TEXT,
		file_size INTEGER,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 创建设备表
	createDevicesTable := `
	CREATE TABLE IF NOT EXISTS devices (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT UNIQUE NOT NULL,
		device_name TEXT NOT NULL,
		user_id INTEGER NOT NULL,
		device_type TEXT DEFAULT 'terminal',
		is_active BOOLEAN DEFAULT 1,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id)
	);`

	// 创建设备授权配置表
	createDeviceAuthorizationsTable := `
	CREATE TABLE IF NOT EXISTS device_authorizations (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT NOT NULL,
		max_usage INTEGER DEFAULT 0,
		current_usage INTEGER DEFAULT 0,
		expire_time DATETIME,
		usage_control_enabled BOOLEAN DEFAULT 1,
		account_info_encryption BOOLEAN DEFAULT 0,
		account_info_content TEXT DEFAULT 'device_name,max_usage,current_usage,expire_time',
		is_active BOOLEAN DEFAULT 1,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (device_id) REFERENCES devices (device_id),
		UNIQUE(device_id)
	);`

	// 创建设备类型管理表
	createDeviceTypesTable := `
	CREATE TABLE IF NOT EXISTS device_types (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		type_code TEXT UNIQUE NOT NULL,
		type_name TEXT NOT NULL,
		description TEXT,
		icon TEXT DEFAULT 'fas fa-microchip',
		color TEXT DEFAULT '#6b7280',
		is_active BOOLEAN DEFAULT 1,
		is_system BOOLEAN DEFAULT 0,
		sort_order INTEGER DEFAULT 0,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	if _, err = db.Exec(createUserTable); err != nil {
		log.Fatal("Failed to create users table:", err)
	}

	if _, err = db.Exec(createAuthTable); err != nil {
		log.Fatal("Failed to create authorizations table:", err)
	}

	if _, err = db.Exec(createCaptchaLogTable); err != nil {
		log.Fatal("Failed to create captcha_logs table:", err)
	}

	if _, err = db.Exec(createOperationLogTable); err != nil {
		log.Fatal("Failed to create operation_logs table:", err)
	}

	if _, err = db.Exec(createRetainedMessagesTable); err != nil {
		log.Fatal("Failed to create mqtt_retained_messages table:", err)
	}

	if _, err = db.Exec(createSystemConfigTable); err != nil {
		log.Fatal("Failed to create system_configs table:", err)
	}

	if _, err = db.Exec(createBackupMetadataTable); err != nil {
		log.Fatal("Failed to create backup_metadata table:", err)
	}

	if _, err = db.Exec(createDevicesTable); err != nil {
		log.Fatal("Failed to create devices table:", err)
	}

	if _, err = db.Exec(createDeviceAuthorizationsTable); err != nil {
		log.Fatal("Failed to create device_authorizations table:", err)
	}

	if _, err = db.Exec(createDeviceTypesTable); err != nil {
		log.Fatal("Failed to create device_types table:", err)
	}

	// 创建操作日志表索引
	createOperationLogIndexes()

	// 初始化默认配置
	initDefaultConfigs()

	// 升级数据库结构
	upgradeDatabase()

	// 修改设备表允许 user_id 为空
	migrateDevicesTable()

	// 创建默认管理员用户
	createDefaultUser()

	// 初始化默认设备类型
	initDefaultDeviceTypes()

	// 修复默认设备类型的排序值
	fixDefaultDeviceTypeSortOrder()

	// 初始化安全数据库操作实例
	safeDB = NewSafeDB(db)

	log.Println("Database initialized successfully")
}

// configureDatabasePool 配置数据库连接池
func configureDatabasePool(database *sql.DB) {
	maxOpenConns := GetConfigInt("database", "max_open_conns", 25)
	maxIdleConns := GetConfigInt("database", "max_idle_conns", 5)
	connMaxLifetime := time.Duration(GetConfigInt("database", "conn_max_lifetime", 300)) * time.Second
	connMaxIdleTime := time.Duration(GetConfigInt("database", "conn_max_idle_time", 60)) * time.Second

	database.SetMaxOpenConns(maxOpenConns)
	database.SetMaxIdleConns(maxIdleConns)
	database.SetConnMaxLifetime(connMaxLifetime)
	database.SetConnMaxIdleTime(connMaxIdleTime)

	log.Printf("Database connection pool configured: MaxOpen=%d, MaxIdle=%d, MaxLifetime=%v, MaxIdleTime=%v",
		maxOpenConns, maxIdleConns, connMaxLifetime, connMaxIdleTime)
}

// upgradeDatabase 升级数据库结构
func upgradeDatabase() {
	// 定义需要添加的列
	columnsToAdd := []struct {
		table      string
		column     string
		definition string
	}{
		{"users", "phone", "TEXT"},
		{"users", "remark", "TEXT"},
		{"users", "max_usage", "INTEGER DEFAULT 20"},
		{"users", "current_usage", "INTEGER DEFAULT 0"},
		{"users", "expire_time", "DATETIME"},
		{"users", "usage_control_enabled", "BOOLEAN DEFAULT 1"},
		{"users", "can_login_backend", "BOOLEAN DEFAULT 0"},
		{"users", "can_login_mqtt", "BOOLEAN DEFAULT 1"},
		{"users", "account_info_encryption", "BOOLEAN DEFAULT 0"},
		{"users", "account_info_content", "TEXT DEFAULT 'username,max_usage,current_usage,expire_time'"},
	}

	// 批量添加列
	for _, col := range columnsToAdd {
		if err := addColumnIfNotExists(col.table, col.column, col.definition); err != nil {
			log.Printf("Failed to add column %s.%s: %v", col.table, col.column, err)
		}
	}

	// 确保admin超级管理员账户有正确的权限设置和密码哈希
	err := updateAdminUserSecurity()
	if err != nil {
		log.Printf("Failed to update admin user security: %v", err)
	}

	// 更新所有用户的密码格式
	err = updateAllUsersPasswordSecurity()
	if err != nil {
		log.Printf("Failed to update users password security: %v", err)
	}
}

// addColumnIfNotExists 添加列（如果不存在）
func addColumnIfNotExists(table, column, definition string) error {
	// 验证表名和列名，防止SQL注入
	if !isValidIdentifier(table) || !isValidIdentifier(column) {
		return fmt.Errorf("invalid table or column name: %s.%s", table, column)
	}

	var exists bool
	err := db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info(?)
		WHERE name = ?
	`, table, column).Scan(&exists)

	if err != nil {
		return err
	}

	if !exists {
		// 使用白名单验证definition，防止SQL注入
		if !isValidColumnDefinition(definition) {
			return fmt.Errorf("invalid column definition: %s", definition)
		}

		query := fmt.Sprintf("ALTER TABLE %s ADD COLUMN %s %s", table, column, definition)
		_, err = db.Exec(query)
		if err != nil {
			return err
		}
		log.Printf("Added column %s.%s", table, column)
	}

	return nil
}

// isValidIdentifier 验证标识符是否安全
func isValidIdentifier(identifier string) bool {
	// 只允许字母、数字和下划线
	matched, _ := regexp.MatchString(`^[a-zA-Z_][a-zA-Z0-9_]*$`, identifier)
	return matched && len(identifier) <= 64
}

// isValidColumnDefinition 验证列定义是否安全
func isValidColumnDefinition(definition string) bool {
	// 允许的列定义模式
	allowedPatterns := []string{
		`^TEXT$`,
		`^TEXT DEFAULT .+$`,
		`^INTEGER$`,
		`^INTEGER DEFAULT \d+$`,
		`^BOOLEAN$`,
		`^BOOLEAN DEFAULT [01]$`,
		`^DATETIME$`,
		`^DATETIME DEFAULT .+$`,
	}

	upperDef := strings.ToUpper(strings.TrimSpace(definition))
	for _, pattern := range allowedPatterns {
		if matched, _ := regexp.MatchString(pattern, upperDef); matched {
			return true
		}
	}
	return false
}

// updateAdminUserSecurity 更新admin用户的安全设置
func updateAdminUserSecurity() error {
	// 检查admin用户是否存在以及密码是否已经是哈希格式
	var currentPassword string
	err := db.QueryRow("SELECT password FROM users WHERE username = 'admin'").Scan(&currentPassword)
	if err != nil {
		log.Printf("Admin user not found, will be created by createDefaultUser")
		return nil
	}

	// 检查密码是否是明文（bcrypt哈希通常以$2开头且长度较长）
	needsPasswordUpdate := false
	if len(currentPassword) < 50 || !strings.HasPrefix(currentPassword, "$2") {
		needsPasswordUpdate = true
		log.Printf("Admin user has plaintext password, updating to bcrypt hash")
	}

	if needsPasswordUpdate {
		// 哈希默认密码
		hashedPassword, err := HashPassword("admin123")
		if err != nil {
			return fmt.Errorf("failed to hash admin password: %v", err)
		}

		// 更新密码和权限
		_, err = db.Exec(`
			UPDATE users
			SET password = ?, usage_control_enabled = 0, can_login_backend = 1, can_login_mqtt = 1
			WHERE username = 'admin'`, hashedPassword)
		if err != nil {
			return fmt.Errorf("failed to update admin user: %v", err)
		}

		log.Println("Updated admin user: password hashed, disabled usage control, enabled backend and MQTT login")
	} else {
		// 只更新权限
		_, err = db.Exec(`
			UPDATE users
			SET usage_control_enabled = 0, can_login_backend = 1, can_login_mqtt = 1
			WHERE username = 'admin'`)
		if err != nil {
			return fmt.Errorf("failed to update admin user permissions: %v", err)
		}

		log.Println("Updated admin user permissions: disabled usage control, enabled backend and MQTT login")
	}

	return nil
}

// updateAllUsersPasswordSecurity 更新所有用户的密码安全格式
func updateAllUsersPasswordSecurity() error {
	// 查询所有用户
	rows, err := db.Query("SELECT id, username, password FROM users")
	if err != nil {
		return fmt.Errorf("failed to query users: %v", err)
	}
	defer rows.Close()

	var usersToUpdate []struct {
		id       int
		username string
		password string
	}

	// 收集需要更新的用户
	for rows.Next() {
		var id int
		var username, password string
		err := rows.Scan(&id, &username, &password)
		if err != nil {
			log.Printf("Failed to scan user row: %v", err)
			continue
		}

		// 检查密码是否是明文（bcrypt哈希通常以$2开头且长度较长）
		if len(password) < 50 || !strings.HasPrefix(password, "$2") {
			usersToUpdate = append(usersToUpdate, struct {
				id       int
				username string
				password string
			}{id, username, password})
		}
	}

	// 更新需要哈希的用户密码
	for _, user := range usersToUpdate {
		hashedPassword, err := HashPassword(user.password)
		if err != nil {
			log.Printf("Failed to hash password for user %s: %v", user.username, err)
			continue
		}

		_, err = db.Exec("UPDATE users SET password = ? WHERE id = ?", hashedPassword, user.id)
		if err != nil {
			log.Printf("Failed to update password for user %s: %v", user.username, err)
			continue
		}

		log.Printf("Updated password hash for user: %s", user.username)
	}

	if len(usersToUpdate) > 0 {
		log.Printf("Updated password security for %d users", len(usersToUpdate))
	} else {
		log.Printf("All user passwords are already in secure hash format")
	}

	return nil
}

// createDefaultUser 创建默认用户
func createDefaultUser() {
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM users").Scan(&count)
	if err != nil {
		log.Printf("Failed to check user count: %v", err)
		return
	}

	if count == 0 {
		// 哈希默认密码
		hashedPassword, err := HashPassword("admin123")
		if err != nil {
			log.Printf("Failed to hash default password: %v", err)
			return
		}

		// 创建默认管理员 - admin账户无账户限制，所有权限开启
		_, err = db.Exec(`
			INSERT INTO users (username, password, usage_control_enabled, can_login_backend, can_login_mqtt, is_active)
			VALUES (?, ?, ?, ?, ?, ?)`,
			"admin", hashedPassword, false, true, true, true)
		if err != nil {
			log.Printf("Failed to create default admin user: %v", err)
			return
		}

		// 为管理员创建授权
		expiresAt := time.Now().AddDate(1, 0, 0) // 1年后过期
		encryptedKey, err := CreateAuthorizationToken(1, "admin", 1000, expiresAt)
		if err != nil {
			log.Printf("Failed to create authorization token: %v", err)
			return
		}

		_, err = db.Exec(`
			INSERT INTO authorizations (user_id, max_usage, expires_at, encrypted_key, is_active)
			VALUES (?, ?, ?, ?, ?)`,
			1, 1000, expiresAt, encryptedKey, true)
		if err != nil {
			log.Printf("Failed to create default authorization: %v", err)
			return
		}

		log.Println("Default admin user created: admin/admin123")
	}
}

// migrateDevicesTable 迁移设备表，允许 user_id 为空
func migrateDevicesTable() {
	// 检查是否需要迁移 - 尝试插入一个 user_id 为 NULL 的测试记录
	testQuery := "INSERT INTO devices (device_id, device_name, user_id, device_type, is_active) VALUES ('__test__', '__test__', NULL, 'test', 0)"
	_, err := db.Exec(testQuery)
	if err == nil {
		// 如果插入成功，说明表已经支持 NULL，删除测试记录
		db.Exec("DELETE FROM devices WHERE device_id = '__test__'")
		return
	}

	log.Println("Migrating devices table to allow NULL user_id...")

	// 创建新的设备表结构（允许 user_id 为 NULL）
	createNewDevicesTable := `
	CREATE TABLE IF NOT EXISTS devices_new (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		device_id TEXT UNIQUE NOT NULL,
		device_name TEXT NOT NULL,
		user_id INTEGER,  -- 移除 NOT NULL 约束
		device_type TEXT DEFAULT 'terminal',
		is_active BOOLEAN DEFAULT 1,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users (id)
	);`

	// 执行迁移
	tx, err := db.Begin()
	if err != nil {
		log.Printf("Failed to begin transaction for devices table migration: %v", err)
		return
	}
	defer tx.Rollback()

	// 创建新表
	_, err = tx.Exec(createNewDevicesTable)
	if err != nil {
		log.Printf("Failed to create new devices table: %v", err)
		return
	}

	// 复制数据
	_, err = tx.Exec(`
		INSERT INTO devices_new (id, device_id, device_name, user_id, device_type, is_active, created_at, updated_at)
		SELECT id, device_id, device_name, user_id, device_type, is_active, created_at, updated_at
		FROM devices
	`)
	if err != nil {
		log.Printf("Failed to copy data to new devices table: %v", err)
		return
	}

	// 删除旧表
	_, err = tx.Exec("DROP TABLE devices")
	if err != nil {
		log.Printf("Failed to drop old devices table: %v", err)
		return
	}

	// 重命名新表
	_, err = tx.Exec("ALTER TABLE devices_new RENAME TO devices")
	if err != nil {
		log.Printf("Failed to rename new devices table: %v", err)
		return
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		log.Printf("Failed to commit devices table migration: %v", err)
		return
	}

	log.Println("Successfully migrated devices table to allow NULL user_id")
}

// GetUserByID 通过用户ID获取用户
func GetUserByID(userID int) (*User, error) {
	user := &User{}
	var email, phone, remark sql.NullString
	var maxUsage, currentUsage sql.NullInt64
	var expireTime sql.NullTime
	var usageControlEnabled, canLoginBackend, canLoginMqtt sql.NullBool

	var accountInfoEncryption sql.NullBool
	var accountInfoContent sql.NullString

	err := db.QueryRow(`
		SELECT id, username, password, email, phone, remark, 
		       max_usage, current_usage, expire_time, usage_control_enabled,
		       can_login_backend, can_login_mqtt, is_active, created_at,
		       account_info_encryption, account_info_content
		FROM users WHERE id = ?`, userID).Scan(
		&user.ID, &user.Username, &user.Password, &email, &phone, &remark,
		&maxUsage, &currentUsage, &expireTime, &usageControlEnabled,
		&canLoginBackend, &canLoginMqtt, &user.IsActive, &user.CreatedAt,
		&accountInfoEncryption, &accountInfoContent)
	if err != nil {
		return nil, err
	}

	if email.Valid {
		user.Email = email.String
	}
	if phone.Valid {
		user.Phone = phone.String
	}
	if remark.Valid {
		user.Remark = remark.String
	}
	if maxUsage.Valid {
		user.MaxUsage = int(maxUsage.Int64)
	} else {
		user.MaxUsage = 20
	}
	if currentUsage.Valid {
		user.CurrentUsage = int(currentUsage.Int64)
	}
	if expireTime.Valid {
		user.ExpireTime = &expireTime.Time
	}
	if usageControlEnabled.Valid {
		user.UsageControlEnabled = usageControlEnabled.Bool
	} else {
		user.UsageControlEnabled = true
	}
	if canLoginBackend.Valid {
		user.CanLoginBackend = canLoginBackend.Bool
	}
	if canLoginMqtt.Valid {
		user.CanLoginMqtt = canLoginMqtt.Bool
	} else {
		user.CanLoginMqtt = true
	}

	// 处理授权信息发送配置字段
	if accountInfoEncryption.Valid {
		user.AccountInfoEncryption = accountInfoEncryption.Bool
	} else {
		user.AccountInfoEncryption = true
	}
	if accountInfoContent.Valid {
		user.AccountInfoContent = accountInfoContent.String
	} else {
		user.AccountInfoContent = "expire_time,max_usage"
	}

	return user, nil
}

// GetUserByUsername 通过用户名获取用户
func GetUserByUsername(username string) (*User, error) {
	user := &User{}
	var email, phone, remark sql.NullString
	var maxUsage, currentUsage sql.NullInt64
	var expireTime sql.NullTime
	var usageControlEnabled, canLoginBackend, canLoginMqtt sql.NullBool

	var accountInfoEncryption sql.NullBool
	var accountInfoContent sql.NullString

	err := db.QueryRow(`
		SELECT id, username, password, email, phone, remark, 
		       max_usage, current_usage, expire_time, usage_control_enabled,
		       can_login_backend, can_login_mqtt, is_active, created_at,
		       account_info_encryption, account_info_content
		FROM users WHERE username = ?`, username).Scan(
		&user.ID, &user.Username, &user.Password, &email, &phone, &remark,
		&maxUsage, &currentUsage, &expireTime, &usageControlEnabled,
		&canLoginBackend, &canLoginMqtt, &user.IsActive, &user.CreatedAt,
		&accountInfoEncryption, &accountInfoContent)
	if err != nil {
		return nil, err
	}

	if email.Valid {
		user.Email = email.String
	}
	if phone.Valid {
		user.Phone = phone.String
	}
	if remark.Valid {
		user.Remark = remark.String
	}
	if maxUsage.Valid {
		user.MaxUsage = int(maxUsage.Int64)
	} else {
		user.MaxUsage = 20
	}
	if currentUsage.Valid {
		user.CurrentUsage = int(currentUsage.Int64)
	}
	if expireTime.Valid {
		user.ExpireTime = &expireTime.Time
	}
	if usageControlEnabled.Valid {
		user.UsageControlEnabled = usageControlEnabled.Bool
	} else {
		user.UsageControlEnabled = true
	}
	if canLoginBackend.Valid {
		user.CanLoginBackend = canLoginBackend.Bool
	}
	if canLoginMqtt.Valid {
		user.CanLoginMqtt = canLoginMqtt.Bool
	} else {
		user.CanLoginMqtt = true
	}

	// 处理授权信息发送配置字段
	if accountInfoEncryption.Valid {
		user.AccountInfoEncryption = accountInfoEncryption.Bool
	} else {
		user.AccountInfoEncryption = true
	}
	if accountInfoContent.Valid {
		user.AccountInfoContent = accountInfoContent.String
	} else {
		user.AccountInfoContent = "expire_time,max_usage"
	}

	return user, nil
}

// GetDeviceByDeviceID 通过设备ID获取设备信息
func GetDeviceByDeviceID(deviceID string) (*Device, error) {
	device := &Device{}
	err := db.QueryRow(`
		SELECT id, device_id, device_name, user_id, device_type, is_active, created_at, updated_at
		FROM devices WHERE device_id = ?`, deviceID).Scan(
		&device.ID, &device.DeviceID, &device.DeviceName, &device.UserID,
		&device.DeviceType, &device.IsActive, &device.CreatedAt, &device.UpdatedAt)
	if err != nil {
		return nil, err
	}
	return device, nil
}

// GetDeviceAuthorization 获取设备授权信息
func GetDeviceAuthorization(deviceID string) (*DeviceAuthorization, error) {
	auth := &DeviceAuthorization{}
	var expireTime sql.NullTime

	err := db.QueryRow(`
		SELECT id, device_id, max_usage, current_usage, expire_time, usage_control_enabled,
		       account_info_encryption, account_info_content, is_active, created_at, updated_at
		FROM device_authorizations WHERE device_id = ?`, deviceID).Scan(
		&auth.ID, &auth.DeviceID, &auth.MaxUsage, &auth.CurrentUsage, &expireTime,
		&auth.UsageControlEnabled, &auth.AccountInfoEncryption, &auth.AccountInfoContent,
		&auth.IsActive, &auth.CreatedAt, &auth.UpdatedAt)
	if err != nil {
		return nil, err
	}

	if expireTime.Valid {
		auth.ExpireTime = &expireTime.Time
	}

	return auth, nil
}

// ValidateUserPassword 验证用户密码
func ValidateUserPassword(user *User, password string) bool {
	return CheckPasswordHash(password, user.Password)
}

// GetUserAuthorization 获取用户授权
func GetUserAuthorization(userID int) (*Authorization, error) {
	auth := &Authorization{}
	err := db.QueryRow(`
		SELECT id, user_id, usage_count, max_usage, expires_at, is_active, encrypted_key, created_at
		FROM authorizations WHERE user_id = ? AND is_active = 1`, userID).Scan(
		&auth.ID, &auth.UserID, &auth.UsageCount, &auth.MaxUsage, &auth.ExpiresAt, &auth.IsActive, &auth.EncryptedKey, &auth.CreatedAt)
	if err != nil {
		return nil, err
	}
	return auth, nil
}

// CreateDevice 创建设备
func CreateDevice(deviceID, deviceName string, userID int, deviceType string, isActive bool) error {
	// 如果 userID 为 0，表示没有分配用户，设置为 NULL
	var userIDParam interface{}
	if userID == 0 {
		userIDParam = nil
	} else {
		userIDParam = userID
	}

	_, err := db.Exec(`
		INSERT INTO devices (device_id, device_name, user_id, device_type, is_active, updated_at)
		VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
		deviceID, deviceName, userIDParam, deviceType, isActive)
	return err
}

// CreateDeviceAuthorization 创建设备授权配置
func CreateDeviceAuthorization(deviceID string, maxUsage int, expireTime *time.Time,
	usageControlEnabled, accountInfoEncryption bool, accountInfoContent string) error {
	_, err := db.Exec(`
		INSERT INTO device_authorizations (device_id, max_usage, expire_time, usage_control_enabled,
		                                   account_info_encryption, account_info_content, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
		deviceID, maxUsage, expireTime, usageControlEnabled, accountInfoEncryption, accountInfoContent)
	return err
}

// AutoCreateDeviceOnMQTTLogin MQTT客户端登录时自动创建设备记录
// 如果设备不存在，自动创建设备并绑定到登录用户，提供默认配置
func AutoCreateDeviceOnMQTTLogin(clientID, username string) error {
	// 检查设备是否已存在
	_, err := GetDeviceByDeviceID(clientID)
	if err == nil {
		// 设备已存在，无需创建
		return nil
	}

	// 获取用户信息
	user, err := GetUserByUsername(username)
	if err != nil {
		return fmt.Errorf("用户 %s 不存在: %v", username, err)
	}

	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		return fmt.Errorf("开始事务失败: %v", err)
	}
	defer tx.Rollback()

	// 设置默认过期时间（一个月后）
	expireTime := time.Now().AddDate(0, 1, 0) // 延期一个月

	// 获取第一个可用的设备类型，如果没有则使用 "default"
	var deviceType string = "default"
	err = db.QueryRow("SELECT type_code FROM device_types WHERE is_active = 1 ORDER BY sort_order ASC, id ASC LIMIT 1").Scan(&deviceType)
	if err != nil {
		log.Printf("No active device types found, using default type: %v", err)
		// 如果没有设备类型，使用 "default" 作为默认值
	}

	// 创建设备记录
	_, err = tx.Exec(`
		INSERT INTO devices (device_id, device_name, user_id, device_type, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
		clientID, "自创设备", user.ID, deviceType, true)
	if err != nil {
		return fmt.Errorf("创建设备记录失败: %v", err)
	}

	// 创建设备授权配置（默认5次使用，一个月过期）
	_, err = tx.Exec(`
		INSERT INTO device_authorizations (device_id, max_usage, current_usage, expire_time,
		                                   usage_control_enabled, account_info_encryption,
		                                   account_info_content, is_active, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
		clientID, 5, 0, expireTime, true, true,
		"device_name,max_usage,current_usage,expire_time", true)
	if err != nil {
		return fmt.Errorf("创建设备授权配置失败: %v", err)
	}

	// 提交事务
	err = tx.Commit()
	if err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	log.Printf("MQTT自动创建设备成功: device_id=%s, user=%s, max_usage=5, expire_time=%s",
		clientID, username, expireTime.Format("2006-01-02 15:04:05"))

	return nil
}

// GetDevicesByUserID 获取用户的所有设备
func GetDevicesByUserID(userID int) ([]Device, error) {
	rows, err := db.Query(`
		SELECT id, device_id, device_name, user_id, device_type, is_active, created_at, updated_at
		FROM devices WHERE user_id = ? ORDER BY created_at DESC`, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var devices []Device
	for rows.Next() {
		var device Device
		err := rows.Scan(&device.ID, &device.DeviceID, &device.DeviceName, &device.UserID,
			&device.DeviceType, &device.IsActive, &device.CreatedAt, &device.UpdatedAt)
		if err != nil {
			return nil, err
		}
		devices = append(devices, device)
	}
	return devices, nil
}

// GetAllDevicesWithAuth 获取所有设备及其授权信息
func GetAllDevicesWithAuth() ([]map[string]interface{}, error) {
	query := `
		SELECT d.id, d.device_id, d.device_name, d.user_id, d.device_type, d.is_active, 
		       d.created_at, d.updated_at, u.username,
		       da.max_usage, da.current_usage, da.expire_time, da.usage_control_enabled,
		       da.account_info_encryption, da.account_info_content, da.is_active as auth_active
		FROM devices d
		LEFT JOIN users u ON d.user_id = u.id
		LEFT JOIN device_authorizations da ON d.device_id = da.device_id
		ORDER BY d.created_at DESC`

	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var devices []map[string]interface{}
	for rows.Next() {
		var device Device
		var username string
		var maxUsage, currentUsage sql.NullInt64
		var expireTime sql.NullTime
		var usageControlEnabled, accountInfoEncryption, authActive sql.NullBool
		var accountInfoContent sql.NullString

		err := rows.Scan(&device.ID, &device.DeviceID, &device.DeviceName, &device.UserID,
			&device.DeviceType, &device.IsActive, &device.CreatedAt, &device.UpdatedAt,
			&username, &maxUsage, &currentUsage, &expireTime, &usageControlEnabled,
			&accountInfoEncryption, &accountInfoContent, &authActive)
		if err != nil {
			return nil, err
		}

		deviceInfo := map[string]interface{}{
			"id":          device.ID,
			"device_id":   device.DeviceID,
			"device_name": device.DeviceName,
			"user_id":     device.UserID,
			"username":    username,
			"device_type": device.DeviceType,
			"is_active":   device.IsActive,
			"created_at":  device.CreatedAt,
			"updated_at":  device.UpdatedAt,
		}

		// 添加授权信息
		if maxUsage.Valid {
			deviceInfo["authorization"] = map[string]interface{}{
				"max_usage":               maxUsage.Int64,
				"current_usage":           currentUsage.Int64,
				"expire_time":             expireTime.Time,
				"usage_control_enabled":   usageControlEnabled.Bool,
				"account_info_encryption": accountInfoEncryption.Bool,
				"account_info_content":    accountInfoContent.String,
				"is_active":               authActive.Bool,
			}
		}

		devices = append(devices, deviceInfo)
	}
	return devices, nil
}

// UpdateDeviceUsage 更新设备使用次数
func UpdateDeviceUsage(deviceID string) error {
	_, err := db.Exec("UPDATE device_authorizations SET current_usage = current_usage + 1 WHERE device_id = ?", deviceID)
	return err
}

// ResetDeviceUsage 重置设备使用次数
func ResetDeviceUsage(deviceID string) error {
	_, err := db.Exec("UPDATE device_authorizations SET current_usage = 0 WHERE device_id = ?", deviceID)
	return err
}

// UpdateAuthorizationUsage 更新授权使用次数
func UpdateAuthorizationUsage(authID int) error {
	_, err := db.Exec("UPDATE authorizations SET usage_count = usage_count + 1 WHERE id = ?", authID)
	return err
}

// LogCaptchaAttempt 记录验证码尝试
func LogCaptchaAttempt(userID int, captchaID string, success bool, ipAddress, userAgent string) error {
	_, err := db.Exec(`
		INSERT INTO captcha_logs (user_id, captcha_id, success, ip_address, user_agent)
		VALUES (?, ?, ?, ?, ?)`,
		userID, captchaID, success, ipAddress, userAgent)
	return err
}

// createOperationLogIndexes 创建操作日志表索引
func createOperationLogIndexes() {
	indexes := []string{
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_user_id ON operation_logs(user_id)",
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_operation_type ON operation_logs(operation_type)",
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_module ON operation_logs(module)",
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_success ON operation_logs(success)",
		"CREATE INDEX IF NOT EXISTS idx_operation_logs_user_time ON operation_logs(user_id, created_at)",
	}

	for _, indexSQL := range indexes {
		if _, err := db.Exec(indexSQL); err != nil {
			log.Printf("Failed to create index: %v", err)
		}
	}
}

// OperationLog 操作日志结构体
type OperationLog struct {
	ID              int    `json:"id"`
	UserID          *int   `json:"user_id"`
	Username        string `json:"username"`
	OperationType   string `json:"operation_type"`
	Module          string `json:"module"`
	OperationDetail string `json:"operation_detail"`
	TargetID        string `json:"target_id"`
	TargetName      string `json:"target_name"`
	Success         bool   `json:"success"`
	IPAddress       string `json:"ip_address"`
	UserAgent       string `json:"user_agent"`
	RequestData     string `json:"request_data"`
	ResponseData    string `json:"response_data"`
	ErrorMessage    string `json:"error_message"`
	CreatedAt       string `json:"created_at"`
}

// LogOperation 记录操作日志
func LogOperation(userID *int, operationType, module, detail, targetID, targetName string, success bool, ipAddress, userAgent, requestData, responseData, errorMessage string) error {
	_, err := db.Exec(`
		INSERT INTO operation_logs (user_id, operation_type, module, operation_detail, target_id, target_name, success, ip_address, user_agent, request_data, response_data, error_message)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		userID, operationType, module, detail, targetID, targetName, success, ipAddress, userAgent, requestData, responseData, errorMessage)
	return err
}

// buildWhereClause 安全构建WHERE子句
func buildWhereClause(conditions map[string]interface{}) (string, []interface{}) {
	var clauses []string
	var args []interface{}

	for field, value := range conditions {
		switch field {
		case "user_id":
			if value != nil {
				if userID, ok := value.(*int); ok && userID != nil {
					clauses = append(clauses, "ol.user_id = ?")
					args = append(args, *userID)
				}
			}
		case "operation_type":
			if str, ok := value.(string); ok && str != "" {
				clauses = append(clauses, "ol.operation_type = ?")
				args = append(args, str)
			}
		case "module":
			if str, ok := value.(string); ok && str != "" {
				clauses = append(clauses, "ol.module = ?")
				args = append(args, str)
			}
		case "start_time":
			if str, ok := value.(string); ok && str != "" {
				clauses = append(clauses, "ol.created_at >= ?")
				args = append(args, str)
			}
		case "end_time":
			if str, ok := value.(string); ok && str != "" {
				clauses = append(clauses, "ol.created_at <= ?")
				args = append(args, str)
			}
		case "keyword":
			if str, ok := value.(string); ok && str != "" {
				clauses = append(clauses, "(ol.operation_detail LIKE ? OR ol.target_name LIKE ?)")
				args = append(args, "%"+str+"%", "%"+str+"%")
			}
		}
	}

	if len(clauses) == 0 {
		return "1=1", nil
	}

	return strings.Join(clauses, " AND "), args
}

// GetOperationLogs 获取操作日志列表
func GetOperationLogs(page, pageSize int, userID *int, operationType, module string, startTime, endTime, keyword string) ([]OperationLog, int, error) {
	var logs []OperationLog
	var totalCount int

	// 构建查询条件
	conditions := map[string]interface{}{
		"user_id":        userID,
		"operation_type": operationType,
		"module":         module,
		"start_time":     startTime,
		"end_time":       endTime,
		"keyword":        keyword,
	}

	whereClause, args := buildWhereClause(conditions)

	// 查询总数
	countQuery := "SELECT COUNT(*) FROM operation_logs ol WHERE " + whereClause
	err := db.QueryRow(countQuery, args...).Scan(&totalCount)
	if err != nil {
		return nil, 0, err
	}

	// 查询分页数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT ol.id, ol.user_id, COALESCE(u.username, '系统') as username, ol.operation_type, ol.module,
			   ol.operation_detail, ol.target_id, ol.target_name, ol.success, ol.ip_address, ol.user_agent,
			   ol.request_data, ol.response_data, ol.error_message, ol.created_at
		FROM operation_logs ol
		LEFT JOIN users u ON ol.user_id = u.id
		WHERE ` + whereClause + `
		ORDER BY ol.created_at DESC
		LIMIT ? OFFSET ?`

	queryArgs := append(args, pageSize, offset)
	rows, err := db.Query(dataQuery, queryArgs...)
	if err != nil {
		return nil, 0, err
	}
	defer rows.Close()

	for rows.Next() {
		var log OperationLog
		err := rows.Scan(&log.ID, &log.UserID, &log.Username, &log.OperationType, &log.Module,
			&log.OperationDetail, &log.TargetID, &log.TargetName, &log.Success, &log.IPAddress, &log.UserAgent,
			&log.RequestData, &log.ResponseData, &log.ErrorMessage, &log.CreatedAt)
		if err != nil {
			return nil, 0, err
		}
		logs = append(logs, log)
	}

	return logs, totalCount, nil
}

// GetOperationLogStats 获取操作日志统计
func GetOperationLogStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 今日操作数
	var todayCount int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM operation_logs 
		WHERE DATE(created_at) = DATE('now')
	`).Scan(&todayCount)
	if err != nil {
		return nil, err
	}
	stats["today_count"] = todayCount

	// 成功率
	var successCount, totalCount int
	err = db.QueryRow(`
		SELECT COUNT(CASE WHEN success = 1 THEN 1 END) as success_count, COUNT(*) as total_count
		FROM operation_logs 
		WHERE DATE(created_at) = DATE('now')
	`).Scan(&successCount, &totalCount)
	if err != nil {
		return nil, err
	}

	successRate := 0.0
	if totalCount > 0 {
		successRate = float64(successCount) / float64(totalCount) * 100
	}
	stats["success_rate"] = successRate

	// 按操作类型统计
	rows, err := db.Query(`
		SELECT operation_type, COUNT(*) as count
		FROM operation_logs 
		WHERE DATE(created_at) = DATE('now')
		GROUP BY operation_type
		ORDER BY count DESC
	`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	operationTypes := make(map[string]int)
	for rows.Next() {
		var opType string
		var count int
		err := rows.Scan(&opType, &count)
		if err != nil {
			return nil, err
		}
		operationTypes[opType] = count
	}
	stats["operation_types"] = operationTypes

	return stats, nil
}

// IsAuthorizationValid 检查授权是否有效
func (a *Authorization) IsValid() bool {
	return a.IsActive && time.Now().Before(a.ExpiresAt) && a.UsageCount < a.MaxUsage
}

// GetRemainingUses 获取剩余使用次数
func (a *Authorization) GetRemainingUses() int {
	return a.MaxUsage - a.UsageCount
}

// MQTT保持消息数据库操作函数

// SaveRetainedMessage 保存保持消息到数据库
func SaveRetainedMessage(topic, message string, qos int) error {
	_, err := db.Exec(`
		INSERT OR REPLACE INTO mqtt_retained_messages (topic, message, qos, updated_at)
		VALUES (?, ?, ?, CURRENT_TIMESTAMP)`,
		topic, message, qos)
	return err
}

// DeleteRetainedMessage 从数据库删除保持消息
func DeleteRetainedMessageFromDB(topic string) error {
	_, err := db.Exec("DELETE FROM mqtt_retained_messages WHERE topic = ?", topic)
	return err
}

// ClearAllRetainedMessagesFromDB 从数据库清空所有保持消息
func ClearAllRetainedMessagesFromDB() (int, error) {
	result, err := db.Exec("DELETE FROM mqtt_retained_messages")
	if err != nil {
		return 0, err
	}

	rowsAffected, err := result.RowsAffected()
	return int(rowsAffected), err
}

// LoadRetainedMessagesFromDB 从数据库加载所有保持消息
func LoadRetainedMessagesFromDB() (map[string]*RetainedMessage, error) {
	rows, err := db.Query(`
		SELECT topic, message, qos, created_at 
		FROM mqtt_retained_messages 
		ORDER BY updated_at DESC`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	messages := make(map[string]*RetainedMessage)
	for rows.Next() {
		var msg RetainedMessage
		var createdAt string

		err := rows.Scan(&msg.Topic, &msg.Message, &msg.QoS, &createdAt)
		if err != nil {
			return nil, err
		}

		// 解析时间
		if timestamp, err := time.Parse("2006-01-02 15:04:05", createdAt); err == nil {
			msg.Timestamp = timestamp
		} else {
			msg.Timestamp = time.Now()
		}

		messages[msg.Topic] = &msg
	}

	return messages, nil
}

// SystemConfig 系统配置结构体
type SystemConfig struct {
	ID          int    `json:"id"`
	Category    string `json:"category"`
	KeyName     string `json:"key_name"`
	Value       string `json:"value"`
	ValueType   string `json:"value_type"`
	Description string `json:"description"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// 配置管理函数

// SetConfig 设置配置项
func SetConfig(category, key, value, valueType, description string) error {
	_, err := db.Exec(`
		INSERT OR REPLACE INTO system_configs (category, key_name, value, value_type, description, updated_at)
		VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
		category, key, value, valueType, description)
	return err
}

// SetConfigValue 只更新配置项的值
func SetConfigValue(category, key, value string) error {
	_, err := db.Exec(`
		UPDATE system_configs
		SET value = ?, updated_at = CURRENT_TIMESTAMP
		WHERE category = ? AND key_name = ?`,
		value, category, key)
	return err
}

// needsValueTypeCorrection 检查配置项是否需要value_type修正
func needsValueTypeCorrection(key string) bool {
	boolKeys := []string{
		"cors_allow_credentials", "cors_dev_mode",
		"enable_rate_limit",
		"enable_xss_protection",
		"frontend_debug_logs", "debug_mode",
	}

	for _, boolKey := range boolKeys {
		if key == boolKey {
			return true
		}
	}
	return false
}

// SetConfigWithCorrectType 设置配置项并确保value_type正确
func SetConfigWithCorrectType(category, key, value string) error {
	// 根据配置项确定正确的value_type
	var valueType string
	switch {
	case key == "cors_allow_credentials" || key == "cors_dev_mode" ||
		key == "enable_rate_limit" ||
		key == "enable_xss_protection" ||
		key == "frontend_debug_logs" || key == "debug_mode":
		valueType = "bool"
	case key == "cors_max_age" ||
		key == "requests_per_minute" || key == "burst_size" || key == "cleanup_interval" ||
		key == "max_open_conns" || key == "max_idle_conns" || key == "conn_max_lifetime" || key == "conn_max_idle_time" ||
		key == "max_username_length" || key == "max_password_length" || key == "max_phone_length" || key == "max_remark_length":
		valueType = "int"
	default:
		valueType = "string"
	}

	_, err := db.Exec(`
		INSERT OR REPLACE INTO system_configs (category, key_name, value, value_type, description, updated_at)
		VALUES (?, ?, ?, ?, COALESCE((SELECT description FROM system_configs WHERE category = ? AND key_name = ?), '配置项'), CURRENT_TIMESTAMP)`,
		category, key, value, valueType, category, key)
	return err
}

// GetConfig 获取配置项
func GetConfig(category, key string) (string, error) {
	var value string
	err := db.QueryRow("SELECT value FROM system_configs WHERE category = ? AND key_name = ?",
		category, key).Scan(&value)
	return value, err
}

// GetConfigWithDefault 获取配置项，如果不存在则返回默认值
func GetConfigWithDefault(category, key, defaultValue string) string {
	value, err := GetConfig(category, key)
	if err != nil {
		return defaultValue
	}
	return value
}

// GetConfigInt 获取整数类型配置
func GetConfigInt(category, key string, defaultValue int) int {
	valueStr, err := GetConfig(category, key)
	if err != nil {
		return defaultValue
	}

	if value, err := strconv.Atoi(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// GetConfigBool 获取布尔类型配置
func GetConfigBool(category, key string, defaultValue bool) bool {
	valueStr, err := GetConfig(category, key)
	if err != nil {
		return defaultValue
	}

	if value, err := strconv.ParseBool(valueStr); err == nil {
		return value
	}
	return defaultValue
}

// GetAllConfigs 获取所有配置
func GetAllConfigs() ([]SystemConfig, error) {
	rows, err := db.Query(`
		SELECT id, category, key_name, value, value_type, description, created_at, updated_at
		FROM system_configs 
		ORDER BY category, key_name`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var configs []SystemConfig
	for rows.Next() {
		var config SystemConfig
		err := rows.Scan(&config.ID, &config.Category, &config.KeyName, &config.Value,
			&config.ValueType, &config.Description, &config.CreatedAt, &config.UpdatedAt)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}

	return configs, nil
}

// GetConfigsByCategory 按分类获取配置
func GetConfigsByCategory(category string) ([]SystemConfig, error) {
	rows, err := db.Query(`
		SELECT id, category, key_name, value, value_type, description, created_at, updated_at
		FROM system_configs 
		WHERE category = ?
		ORDER BY key_name`, category)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var configs []SystemConfig
	for rows.Next() {
		var config SystemConfig
		err := rows.Scan(&config.ID, &config.Category, &config.KeyName, &config.Value,
			&config.ValueType, &config.Description, &config.CreatedAt, &config.UpdatedAt)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}

	return configs, nil
}

// initDefaultConfigs 初始化默认配置
func initDefaultConfigs() {
	// 检查是否已有配置
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM system_configs").Scan(&count)
	if err != nil {
		log.Printf("Failed to check config count: %v", err)
		return
	}

	// 如果已有配置，跳过初始化
	if count > 0 {
		log.Println("System configs already exist, skipping initialization")
		return
	}

	// MQTT服务器配置
	defaultConfigs := []struct {
		category, key, value, valueType, description string
	}{
		// MQTT配置
		{"mqtt", "require_auth", "true", "bool", "是否需要认证"},
		{"mqtt", "allow_anonymous", "false", "bool", "是否允许匿名连接"},
		{"mqtt", "max_failed_attempts", "3", "int", "最大失败认证次数"},
		{"mqtt", "blacklist_duration", "24", "int", "黑名单时长（小时）"},
		{"mqtt", "cleanup_interval", "30", "int", "清理间隔（分钟）"},
		{"mqtt", "port", "1883", "int", "MQTT服务器端口"},
		{"mqtt", "heartbeat_interval", "30", "int", "心跳检查间隔（秒）"},
		{"mqtt", "heartbeat_timeout", "120", "int", "心跳超时时间（秒）"},
		{"mqtt", "heartbeat_packet_interval", "1000", "int", "心跳包间隔（毫秒）"},

		// 验证码配置
		{"captcha", "img_width", "300", "int", "验证码图片宽度"},
		{"captcha", "img_height", "150", "int", "验证码图片高度"},
		{"captcha", "point_radius", "20", "int", "红圈半径"},
		{"captcha", "tolerance_radius", "20", "int", "容错半径"},
		{"captcha", "min_distance", "80", "int", "圆圈之间最小距离"},
		{"captcha", "num_points", "3", "int", "验证码点数"},
		{"captcha", "expire_minutes", "3", "int", "验证码过期时间（分钟）"},
		{"captcha", "debug_output", "false", "bool", "启用验证码调试信息输出（生产环境建议关闭）"},

		// 系统配置
		{"system", "log_retention_days", "30", "int", "日志保留天数"},
		{"system", "max_user_sessions", "10", "int", "用户最大会话数"},
		{"system", "session_timeout_hours", "24", "int", "会话超时时间（小时）"},
		{"system", "api_rate_limit", "1000", "int", "API速率限制（每分钟）"},
		{"system", "frontend_debug_logs", "false", "bool", "启用前端调试日志输出（开发调试用）"},
		{"system", "debug_mode", "false", "bool", "调试模式（禁用前端缓存，开发调试用）"},

		// 安全配置
		{"security", "password_min_length", "6", "int", "密码最小长度"},
		{"security", "login_max_attempts", "5", "int", "登录最大尝试次数"},
		{"security", "lockout_duration_minutes", "30", "int", "账户锁定时长（分钟）"},
		{"security", "jwt_expire_hours", "24", "int", "JWT过期时间（小时）"},

		// 账户信息发送配置
		{"account_info", "enable_send", "true", "bool", "是否启用账户信息发送功能"},
		{"account_info", "enable_encryption", "true", "bool", "是否启用RSA加密"},
		{"account_info", "topic_prefix", "account", "string", "账户信息主题前缀"},
		{"account_info", "send_on_login", "true", "bool", "登录时是否发送账户信息"},
		{"account_info", "include_expire_time", "true", "bool", "是否包含过期时间"},
		{"account_info", "include_usage_count", "true", "bool", "是否包含使用次数"},

		// CORS安全配置
		{"security", "cors_allowed_origins", "http://localhost:5173,http://127.0.0.1:5173", "string", "CORS允许的源地址，逗号分隔"},
		{"security", "cors_allowed_methods", "GET,POST,PUT,DELETE,OPTIONS", "string", "CORS允许的HTTP方法"},
		{"security", "cors_allowed_headers", "Origin,Content-Type,Authorization", "string", "CORS允许的请求头"},
		{"security", "cors_allow_credentials", "true", "bool", "是否允许携带凭证"},
		{"security", "cors_max_age", "86400", "int", "预检请求缓存时间（秒）"},
		{"security", "cors_dev_mode", "false", "bool", "CORS开发模式（允许所有来源，仅开发环境使用）"},

		// 频率限制配置
		{"rate_limit", "enable_rate_limit", "true", "bool", "是否启用频率限制"},
		{"rate_limit", "requests_per_minute", "120", "int", "每分钟最大请求数"},
		{"rate_limit", "burst_size", "20", "int", "突发请求缓冲大小"},
		{"rate_limit", "cleanup_interval", "300", "int", "清理间隔（秒）"},

		// 数据库连接池配置
		{"database", "max_open_conns", "25", "int", "最大打开连接数"},
		{"database", "max_idle_conns", "5", "int", "最大空闲连接数"},
		{"database", "conn_max_lifetime", "300", "int", "连接最大生存时间（秒）"},
		{"database", "conn_max_idle_time", "60", "int", "连接最大空闲时间（秒）"},

		// 输入验证配置
		{"validation", "max_username_length", "50", "int", "用户名最大长度"},
		{"validation", "max_password_length", "128", "int", "密码最大长度"},
		{"validation", "max_phone_length", "20", "int", "手机号最大长度"},
		{"validation", "max_remark_length", "200", "int", "备注最大长度"},
		{"validation", "enable_xss_protection", "true", "bool", "启用XSS防护"},
	}

	for _, config := range defaultConfigs {
		err := SetConfig(config.category, config.key, config.value, config.valueType, config.description)
		if err != nil {
			log.Printf("Failed to set default config %s.%s: %v", config.category, config.key, err)
		}
	}

	log.Printf("Initialized %d default system configs", len(defaultConfigs))
}

// initDefaultDeviceTypes 初始化默认设备类型
func initDefaultDeviceTypes() {
	// 检查是否已有设备类型
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM device_types").Scan(&count)
	if err != nil {
		log.Printf("Failed to check device types count: %v", err)
		return
	}

	// 如果已有设备类型，跳过初始化
	if count > 0 {
		log.Println("Device types already exist, skipping initialization")
		return
	}

	// 创建默认设备类型（设置为系统类型，不可删除和编辑）
	_, err = db.Exec(`
		INSERT INTO device_types (type_code, type_name, description, icon, color, is_active, is_system, sort_order, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, 1, 1, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
		"default", "默认类型", "没有过分类的设备", "fas fa-question-circle", "#6b7280", 0)

	if err != nil {
		log.Printf("Failed to create default device type: %v", err)
		return
	}

	log.Println("Created default device type: default")
}

// fixDefaultDeviceTypeSortOrder 修复默认设备类型的排序值
func fixDefaultDeviceTypeSortOrder() {
	// 确保默认类型的 sort_order = 0
	_, err := db.Exec(`
		UPDATE device_types
		SET sort_order = 0
		WHERE type_code = 'default' AND is_system = 1
	`)
	if err != nil {
		log.Printf("Failed to fix default device type sort order: %v", err)
		return
	}

	log.Println("Fixed default device type sort order to 0")
}

// 设备类型管理函数

// GetAllDeviceTypes 获取所有设备类型
func GetAllDeviceTypes() ([]DeviceType, error) {
	rows, err := db.Query(`
		SELECT id, type_code, type_name, description, icon, color, is_active, is_system, sort_order, created_at, updated_at
		FROM device_types
		ORDER BY sort_order ASC, type_name ASC`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var deviceTypes []DeviceType
	for rows.Next() {
		var dt DeviceType
		err := rows.Scan(&dt.ID, &dt.TypeCode, &dt.TypeName, &dt.Description,
			&dt.Icon, &dt.Color, &dt.IsActive, &dt.IsSystem, &dt.SortOrder,
			&dt.CreatedAt, &dt.UpdatedAt)
		if err != nil {
			return nil, err
		}
		deviceTypes = append(deviceTypes, dt)
	}

	return deviceTypes, nil
}

// GetActiveDeviceTypes 获取启用的设备类型
func GetActiveDeviceTypes() ([]DeviceType, error) {
	rows, err := db.Query(`
		SELECT id, type_code, type_name, description, icon, color, is_active, is_system, sort_order, created_at, updated_at
		FROM device_types
		WHERE is_active = 1
		ORDER BY is_system DESC, sort_order ASC, type_name ASC`)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var deviceTypes []DeviceType
	for rows.Next() {
		var dt DeviceType
		err := rows.Scan(&dt.ID, &dt.TypeCode, &dt.TypeName, &dt.Description,
			&dt.Icon, &dt.Color, &dt.IsActive, &dt.IsSystem, &dt.SortOrder,
			&dt.CreatedAt, &dt.UpdatedAt)
		if err != nil {
			return nil, err
		}
		deviceTypes = append(deviceTypes, dt)
	}

	return deviceTypes, nil
}

// GetDeviceTypeByCode 根据类型代码获取设备类型
func GetDeviceTypeByCode(typeCode string) (*DeviceType, error) {
	var dt DeviceType
	err := db.QueryRow(`
		SELECT id, type_code, type_name, description, icon, color, is_active, is_system, sort_order, created_at, updated_at
		FROM device_types
		WHERE type_code = ?`, typeCode).Scan(
		&dt.ID, &dt.TypeCode, &dt.TypeName, &dt.Description,
		&dt.Icon, &dt.Color, &dt.IsActive, &dt.IsSystem, &dt.SortOrder,
		&dt.CreatedAt, &dt.UpdatedAt)
	if err != nil {
		return nil, err
	}
	return &dt, nil
}

// CreateDeviceType 创建设备类型
func CreateDeviceType(typeCode, typeName, description, icon, color string, sortOrder int) error {
	_, err := db.Exec(`
		INSERT INTO device_types (type_code, type_name, description, icon, color, is_active, is_system, sort_order, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, 1, 0, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
		typeCode, typeName, description, icon, color, sortOrder)
	return err
}

// UpdateDeviceType 更新设备类型
func UpdateDeviceType(id int, typeName, description, icon, color string, isActive bool, sortOrder int) error {
	_, err := db.Exec(`
		UPDATE device_types
		SET type_name = ?, description = ?, icon = ?, color = ?, is_active = ?, sort_order = ?, updated_at = CURRENT_TIMESTAMP
		WHERE id = ?`,
		typeName, description, icon, color, isActive, sortOrder, id)
	return err
}

// DeleteDeviceType 删除设备类型（仅限非系统类型）
func DeleteDeviceType(id int) error {
	// 检查是否为系统类型
	var isSystem bool
	err := db.QueryRow("SELECT is_system FROM device_types WHERE id = ?", id).Scan(&isSystem)
	if err != nil {
		return err
	}
	if isSystem {
		return fmt.Errorf("cannot delete system device type")
	}

	// 检查是否有设备使用此类型
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM devices WHERE device_type = (SELECT type_code FROM device_types WHERE id = ?)", id).Scan(&count)
	if err != nil {
		return err
	}
	if count > 0 {
		return fmt.Errorf("cannot delete device type: %d devices are using this type", count)
	}

	_, err = db.Exec("DELETE FROM device_types WHERE id = ?", id)
	return err
}

// ClearAllDeviceTypes 清空所有设备类型
func ClearAllDeviceTypes() error {
	_, err := db.Exec("DELETE FROM device_types")
	return err
}
