.mqtt-management {
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.stat-icon.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.success {
  background: linear-gradient(135deg, #6fcf97 0%, #6fcf97 100%);
}

.stat-icon.danger {
  background: linear-gradient(135deg, #f25b5b 0%, #f25b5b 100%);
}

.stat-icon.warning {
  background: linear-gradient(135deg, #f2c94c 0%, #f2c94c 100%);
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.stat-value.text-success {
  color: #6fcf97;
}

.stat-value.text-danger {
  color: #f25b5b;
}

.mqtt-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
}

:deep(.el-tabs__header) {
  margin: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-weight: 500;
  color: #666;
}

:deep(.el-tabs__item.is-active) {
  color: #667eea;
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  background-color: #667eea;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
  margin-bottom: 20px;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eef2f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.header-btn.refresh {
  background: #909399;
  color: white;
}

.header-btn.danger {
  background: #f25b5b;
  color: white;
}

.header-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.header-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.security-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.add-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #667eea;
  color: white;
}

.add-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.config-management-container {
  position: relative;
}

.config-content-area {
  padding-bottom: 80px;
}

.grid-layout-auto {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.height-full-minus-300 {
  height: calc(100% - 300px);
}

.height-full-minus-360 {
  height: calc(100% - 360px);
}

.min-height-350 {
  min-height: 350px;
}

.card-content {
  padding: 20px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.modern-form-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.switch-group {
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.switch-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-grid-2col {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.unit {
  color: #999;
  font-size: 14px;
}

:deep(.el-input-number) {
  width: 150px;
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.form-help {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 12px;
}

.range {
  color: #667eea;
  font-weight: 500;
}

.desc {
  color: #999;
}

.config-actions-bar {
  position: fixed;
  bottom: 0;
  left: 240px;
  right: 20px;
  background: white;
  border-top: 1px solid #eef2f7;
  padding: 20px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.config-actions-content {
  display: flex;
  justify-content: center;
}

.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.modern-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modern-btn.large {
  padding: 15px 30px;
  font-size: 16px;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.grid-layout-2col {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.form-row-compact {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.form-label-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
}

.form-actions {
  margin-top: 10px;
}

.modern-input {
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
  width: 100%;
}

.modern-input:focus {
  outline: none;
  border-color: #667eea;
}

:deep(.el-select) {
  width: 100%;
}

.select-prefix {
  color: #999;
  font-size: 12px;
}

.client-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.client-info {
  display: flex;
  flex-direction: column;
}

.client-id {
  font-weight: 500;
}

.client-username {
  font-size: 12px;
  color: #999;
}

.client-topics {
  font-size: 12px;
  color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mqtt-management {
    padding: 15px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .switch-group {
    flex-direction: column;
    gap: 15px;
  }
  
  .form-grid-2col {
    grid-template-columns: 1fr;
  }
  
  .config-actions-bar {
    left: 20px;
    right: 20px;
    padding: 15px;
  }
  
  .config-actions-content .modern-btn {
    padding: 12px 20px;
    font-size: 14px;
  }
  
  .grid-layout-2col {
    grid-template-columns: 1fr;
  }
  
  .form-row-compact {
    flex-direction: column;
    gap: 15px;
  }
}