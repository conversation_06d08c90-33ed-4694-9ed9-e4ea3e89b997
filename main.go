package main

import (
	"bytes"
	"crypto/ecdsa"
	"crypto/rsa"
	"crypto/x509"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/png"
	"io"
	"log"
	"math"
	"math/rand"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

type CaptchaData struct {
	Points []image.Point // 正确点的坐标
	Time   time.Time     // 生成时间
	UserID int           // 用户ID
}

// RateLimiter 频率限制器
type RateLimiter struct {
	visitors map[string]*Visitor
	mu       sync.RWMutex
	rate     int
	burst    int
	stopChan chan struct{}
}

// Visitor 访问者信息
type Visitor struct {
	limiter  *rate.Limiter
	lastSeen time.Time
}

// NewRateLimiter 创建频率限制器
func NewRateLimiter() *RateLimiter {
	requestsPerMinute := GetConfigInt("rate_limit", "requests_per_minute", 60)
	burstSize := GetConfigInt("rate_limit", "burst_size", 10)

	rl := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     requestsPerMinute,
		burst:    burstSize,
		stopChan: make(chan struct{}),
	}

	// 启动清理goroutine
	go rl.cleanupVisitors()

	return rl
}

// Stop 停止频率限制器
func (rl *RateLimiter) Stop() {
	close(rl.stopChan)

	rl.mu.Lock()
	rl.visitors = make(map[string]*Visitor)
	rl.mu.Unlock()

	log.Println("Rate limiter stopped and cleaned up")
}

// getVisitor 获取或创建访问者
func (rl *RateLimiter) getVisitor(ip string) *rate.Limiter {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	visitor, exists := rl.visitors[ip]
	if !exists {
		limiter := rate.NewLimiter(rate.Every(time.Minute/time.Duration(rl.rate)), rl.burst)
		rl.visitors[ip] = &Visitor{
			limiter:  limiter,
			lastSeen: time.Now(),
		}
		return limiter
	}

	visitor.lastSeen = time.Now()
	return visitor.limiter
}

// cleanupVisitors 清理过期的访问者
func (rl *RateLimiter) cleanupVisitors() {
	cleanupInterval := time.Duration(GetConfigInt("rate_limit", "cleanup_interval", 300)) * time.Second
	ticker := time.NewTicker(cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-rl.stopChan:
			log.Println("Rate limiter cleanup goroutine stopped")
			return
		case <-ticker.C:
			rl.mu.Lock()
			cleanedCount := 0
			for ip, visitor := range rl.visitors {
				if time.Since(visitor.lastSeen) > cleanupInterval {
					delete(rl.visitors, ip)
					cleanedCount++
				}
			}
			rl.mu.Unlock()

			if cleanedCount > 0 {
				log.Printf("Rate limiter cleaned up %d expired visitors", cleanedCount)
			}
		}
	}
}

// SSE相关结构
type SSEClient struct {
	ID     string
	Events chan string
}

type SSEHub struct {
	clients    map[string]*SSEClient
	register   chan *SSEClient
	unregister chan *SSEClient
	broadcast  chan string
	mu         sync.RWMutex
}

// GoroutineManager 统一的goroutine生命周期管理器
type GoroutineManager struct {
	wg       sync.WaitGroup
	stopChan chan struct{}
	mu       sync.RWMutex
	running  map[string]bool // 跟踪正在运行的goroutine
}

// NewGoroutineManager 创建新的goroutine管理器
func NewGoroutineManager() *GoroutineManager {
	return &GoroutineManager{
		stopChan: make(chan struct{}),
		running:  make(map[string]bool),
	}
}

// Start 启动一个受管理的goroutine
func (gm *GoroutineManager) Start(name string, fn func()) {
	gm.mu.Lock()
	if gm.running[name] {
		gm.mu.Unlock()
		log.Printf("Goroutine %s is already running", name)
		return
	}
	gm.running[name] = true
	gm.mu.Unlock()

	gm.wg.Add(1)
	go func() {
		defer func() {
			gm.wg.Done()
			gm.mu.Lock()
			delete(gm.running, name)
			gm.mu.Unlock()

			if r := recover(); r != nil {
				log.Printf("Goroutine %s panicked: %v", name, r)
			}
		}()

		log.Printf("Starting goroutine: %s", name)
		fn()
		log.Printf("Goroutine %s finished", name)
	}()
}

// StartWithContext 启动一个带上下文的受管理goroutine
func (gm *GoroutineManager) StartWithContext(name string, fn func(stopChan <-chan struct{})) {
	gm.Start(name, func() {
		fn(gm.stopChan)
	})
}

// Stop 停止所有goroutine
func (gm *GoroutineManager) Stop() {
	log.Println("Stopping all managed goroutines...")
	close(gm.stopChan)
	gm.wg.Wait()
	log.Println("All managed goroutines stopped")
}

// GetRunningCount 获取正在运行的goroutine数量
func (gm *GoroutineManager) GetRunningCount() int {
	gm.mu.RLock()
	defer gm.mu.RUnlock()
	return len(gm.running)
}

// GetRunningNames 获取正在运行的goroutine名称列表
func (gm *GoroutineManager) GetRunningNames() []string {
	gm.mu.RLock()
	defer gm.mu.RUnlock()

	names := make([]string, 0, len(gm.running))
	for name := range gm.running {
		names = append(names, name)
	}
	return names
}

// HealthChecker 健康检查器
type HealthChecker struct {
	checks map[string]func() (bool, string)
	mu     sync.RWMutex
}

// NewHealthChecker 创建新的健康检查器
func NewHealthChecker() *HealthChecker {
	return &HealthChecker{
		checks: make(map[string]func() (bool, string)),
	}
}

// RegisterCheck 注册健康检查
func (hc *HealthChecker) RegisterCheck(name string, checkFunc func() (bool, string)) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	hc.checks[name] = checkFunc
}

// CheckHealth 执行所有健康检查
func (hc *HealthChecker) CheckHealth() map[string]interface{} {
	hc.mu.RLock()
	defer hc.mu.RUnlock()

	results := make(map[string]interface{})
	overallHealthy := true

	for name, checkFunc := range hc.checks {
		healthy, message := checkFunc()
		results[name] = map[string]interface{}{
			"healthy": healthy,
			"message": message,
		}
		if !healthy {
			overallHealthy = false
		}
	}

	results["overall"] = map[string]interface{}{
		"healthy":   overallHealthy,
		"timestamp": time.Now().Unix(),
		"uptime":    time.Since(startTime).Seconds(),
	}

	return results
}

// LogLevel 日志级别
type LogLevel int

const (
	LogLevelDebug LogLevel = iota
	LogLevelInfo
	LogLevelWarn
	LogLevelError
	LogLevelFatal
)

// String 返回日志级别的字符串表示
func (l LogLevel) String() string {
	switch l {
	case LogLevelDebug:
		return "DEBUG"
	case LogLevelInfo:
		return "INFO"
	case LogLevelWarn:
		return "WARN"
	case LogLevelError:
		return "ERROR"
	case LogLevelFatal:
		return "FATAL"
	default:
		return "UNKNOWN"
	}
}

// StructuredLogger 结构化日志记录器
type StructuredLogger struct {
	level LogLevel
	mu    sync.RWMutex
}

// NewStructuredLogger 创建新的结构化日志记录器
func NewStructuredLogger(level LogLevel) *StructuredLogger {
	return &StructuredLogger{
		level: level,
	}
}

// SetLevel 设置日志级别
func (sl *StructuredLogger) SetLevel(level LogLevel) {
	sl.mu.Lock()
	defer sl.mu.Unlock()
	sl.level = level
}

// shouldLog 检查是否应该记录该级别的日志
func (sl *StructuredLogger) shouldLog(level LogLevel) bool {
	sl.mu.RLock()
	defer sl.mu.RUnlock()
	return level >= sl.level
}

// logf 格式化日志输出
func (sl *StructuredLogger) logf(level LogLevel, component, message string, fields map[string]interface{}) {
	if !sl.shouldLog(level) {
		return
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	// 构建结构化日志
	logEntry := fmt.Sprintf("[%s] %s [%s] %s", timestamp, level.String(), component, message)

	// 添加字段信息
	if len(fields) > 0 {
		var fieldStrs []string
		for k, v := range fields {
			fieldStrs = append(fieldStrs, fmt.Sprintf("%s=%v", k, v))
		}
		logEntry += " | " + strings.Join(fieldStrs, " ")
	}

	log.Println(logEntry)
}

// Debug 记录调试日志
func (sl *StructuredLogger) Debug(component, message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	sl.logf(LogLevelDebug, component, message, f)
}

// Info 记录信息日志
func (sl *StructuredLogger) Info(component, message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	sl.logf(LogLevelInfo, component, message, f)
}

// Warn 记录警告日志
func (sl *StructuredLogger) Warn(component, message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	sl.logf(LogLevelWarn, component, message, f)
}

// Error 记录错误日志
func (sl *StructuredLogger) Error(component, message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	sl.logf(LogLevelError, component, message, f)
}

// Fatal 记录致命错误日志并退出
func (sl *StructuredLogger) Fatal(component, message string, fields ...map[string]interface{}) {
	var f map[string]interface{}
	if len(fields) > 0 {
		f = fields[0]
	}
	sl.logf(LogLevelFatal, component, message, f)
	os.Exit(1)
}

var (
	captchaManager   *CaptchaManager // 使用新的验证码管理器
	GlobalSSEHub     *SSEHub
	goroutineManager *GoroutineManager // 统一的goroutine管理器
	structuredLogger *StructuredLogger // 全局结构化日志记录器
	healthChecker    *HealthChecker    // 健康检查器
	startTime        time.Time         // 服务启动时间
)

// 应用配置变量 - 在main函数中初始化
var (
	imgW, imgH      int
	pointRadius     int
	toleranceRadius int
	minDistance     int
	numPoints       int
	captchaExpire   time.Duration
)

// 错误响应结构
type ErrorResponse struct {
	Error   string `json:"error"`
	Code    int    `json:"code,omitempty"`
	Details string `json:"details,omitempty"`
}

// 成功响应结构
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// isCaptchaDebugEnabled 检查验证码调试输出是否启用
func isCaptchaDebugEnabled() bool {
	debugEnabled := GetConfigWithDefault("captcha", "debug_output", "false")
	return debugEnabled == "true"
}

// AppError 应用错误结构
type AppError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
	Err     error  `json:"-"`
}

func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %v", e.Message, e.Err)
	}
	return e.Message
}

// NewAppError 创建应用错误
func NewAppError(code int, message string, err error) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// NewAppErrorWithDetails 创建带详情的应用错误
func NewAppErrorWithDetails(code int, message, details string, err error) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
		Err:     err,
	}
}

// 统一错误处理函数
func handleError(c *gin.Context, statusCode int, message string, details string) {
	response := ErrorResponse{
		Error:   message,
		Code:    statusCode,
		Details: details,
	}
	c.JSON(statusCode, response)
}

// 统一成功响应函数
func handleSuccess(c *gin.Context, message string, data interface{}) {
	response := SuccessResponse{
		Message: message,
		Data:    data,
	}
	c.JSON(200, response)
}

// setupCORS 设置CORS配置
func setupCORS() gin.HandlerFunc {
	// 从数据库读取CORS配置，默认包含更多常用的本地IP
	defaultOrigins := "http://localhost:5173,http://127.0.0.1:5173,http://***********:5173,http://***********:5173,http://********:5173"
	allowedOrigins := strings.Split(GetConfigWithDefault("security", "cors_allowed_origins", defaultOrigins), ",")
	allowedMethods := strings.Split(GetConfigWithDefault("security", "cors_allowed_methods", "GET,POST,PUT,DELETE,OPTIONS"), ",")
	allowedHeaders := strings.Split(GetConfigWithDefault("security", "cors_allowed_headers", "Origin,Content-Type,Authorization"), ",")
	allowCredentials := GetConfigBool("security", "cors_allow_credentials", true)
	maxAge := time.Duration(GetConfigInt("security", "cors_max_age", 86400)) * time.Second

	// 清理空白字符
	for i, origin := range allowedOrigins {
		allowedOrigins[i] = strings.TrimSpace(origin)
	}
	for i, method := range allowedMethods {
		allowedMethods[i] = strings.TrimSpace(method)
	}
	for i, header := range allowedHeaders {
		allowedHeaders[i] = strings.TrimSpace(header)
	}

	// 检查是否启用开发模式（允许所有来源）
	devMode := GetConfigBool("security", "cors_dev_mode", false)

	var config cors.Config
	if devMode {
		// 开发模式：允许所有来源（仅用于开发环境）
		config = cors.Config{
			AllowAllOrigins:  true,
			AllowMethods:     allowedMethods,
			AllowHeaders:     allowedHeaders,
			AllowCredentials: allowCredentials,
			MaxAge:           maxAge,
		}
		structuredLogger.Warn("CORS", "Development mode enabled - allowing all origins", map[string]interface{}{
			"warning": "This should not be used in production",
		})
	} else {
		// 生产模式：使用配置的允许来源
		config = cors.Config{
			AllowOrigins:     allowedOrigins,
			AllowMethods:     allowedMethods,
			AllowHeaders:     allowedHeaders,
			AllowCredentials: allowCredentials,
			MaxAge:           maxAge,
		}
	}

	// 记录CORS配置
	structuredLogger.Info("CORS", "CORS configuration loaded", map[string]interface{}{
		"dev_mode":          devMode,
		"allowed_origins":   allowedOrigins,
		"allowed_methods":   allowedMethods,
		"allowed_headers":   allowedHeaders,
		"allow_credentials": allowCredentials,
	})

	return cors.New(config)
}

// 全局频率限制器
var globalRateLimiter *RateLimiter

// RateLimitMiddleware 频率限制中间件
func RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否启用频率限制
		if !GetConfigBool("rate_limit", "enable_rate_limit", true) {
			c.Next()
			return
		}

		// 豁免某些关键路径和配置相关路径
		path := c.Request.URL.Path
		exemptPaths := []string{
			"/api/events",             // SSE连接
			"/debug/status",           // 调试状态
			"/api/auth/status",        // 认证状态检查
			"/api/security/config",    // 系统配置（页面初始化需要）
			"/api/backup/list",        // 备份列表
			"/api/backup/auto-config", // 自动备份配置
		}

		for _, exemptPath := range exemptPaths {
			if strings.HasPrefix(path, exemptPath) {
				c.Next()
				return
			}
		}

		// 获取客户端IP
		clientIP := c.ClientIP()

		// 获取访问者的限制器
		limiter := globalRateLimiter.getVisitor(clientIP)

		// 检查是否允许请求
		if !limiter.Allow() {
			// 记录频率限制日志
			requestData := fmt.Sprintf(`{"ip":"%s","path":"%s","method":"%s"}`,
				clientIP, c.Request.URL.Path, c.Request.Method)
			LogOperation(nil, "RATE_LIMIT", "security", "Rate limit exceeded",
				"", "", false, clientIP, c.GetHeader("User-Agent"),
				requestData, "", "Rate limit exceeded for IP: "+clientIP)

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Too many requests. Please try again later.",
				"retry_after": "60",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// getEnvInt 获取环境变量整数值
func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		log.Printf("Warning: Invalid integer value for %s: %s, using default: %d", key, value, defaultValue)
	}
	return defaultValue
}

// SSE Hub 实现
func NewSSEHub() *SSEHub {
	return &SSEHub{
		clients:    make(map[string]*SSEClient),
		register:   make(chan *SSEClient),
		unregister: make(chan *SSEClient),
		broadcast:  make(chan string),
	}
}

func (h *SSEHub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mu.Lock()
			h.clients[client.ID] = client
			h.mu.Unlock()
			log.Printf("SSE client %s connected", client.ID)

		case client := <-h.unregister:
			h.mu.Lock()
			if _, ok := h.clients[client.ID]; ok {
				delete(h.clients, client.ID)
				close(client.Events)
			}
			h.mu.Unlock()
			log.Printf("SSE client %s disconnected", client.ID)

		case message := <-h.broadcast:
			h.mu.RLock()
			for _, client := range h.clients {
				select {
				case client.Events <- message:
				default:
					// 客户端缓冲区满，关闭连接
					close(client.Events)
					delete(h.clients, client.ID)
				}
			}
			h.mu.RUnlock()
		}
	}
}

func (h *SSEHub) BroadcastEvent(eventType, data string) {
	message := fmt.Sprintf("event: %s\ndata: %s\n\n", eventType, data)
	log.Printf("SSE Broadcasting event: %s to %d clients", eventType, len(h.clients))
	select {
	case h.broadcast <- message:
		log.Printf("SSE event sent to broadcast channel")
	default:
		log.Printf("SSE broadcast channel full, dropping message")
	}
}

// 生成随机颜色
func randColor() color.RGBA {
	return color.RGBA{uint8(rand.Intn(200)), uint8(rand.Intn(200)), uint8(rand.Intn(200)), 255}
}

// 绘制带极强干扰的数字，人类易读但AI完全无法识别
func drawNumber(img *image.RGBA, x, y, num int) {
	// 使用更强对比度的颜色
	colors := []color.RGBA{
		{255, 255, 255, 255}, // 白色
		{255, 255, 0, 255},   // 黄色
		{0, 255, 255, 255},   // 青色
	}

	numColor := colors[num-1]

	// 先画大量干扰线条
	for i := 0; i < 20; i++ {
		lineX := x + rand.Intn(41) - 20
		lineY := y + rand.Intn(41) - 20
		lineLen := rand.Intn(15) + 5

		for j := 0; j < lineLen; j++ {
			dx := rand.Intn(3) - 1
			dy := rand.Intn(3) - 1
			if lineX+dx >= 0 && lineX+dx < imgW && lineY+dy >= 0 && lineY+dy < imgH {
				img.Set(lineX+dx, lineY+dy, color.RGBA{
					uint8(rand.Intn(255)),
					uint8(rand.Intn(255)),
					uint8(rand.Intn(255)),
					uint8(rand.Intn(200) + 55),
				})
			}
		}
	}

	// 简单但清晰的数字图案
	switch num {
	case 1:
		// 画一个清晰的 "1"
		for i := -6; i <= 6; i++ {
			img.Set(x, y+i, numColor)   // 垂直线
			img.Set(x-1, y+i, numColor) // 加粗
		}
		// 顶部斜线
		img.Set(x-2, y-5, numColor)
		img.Set(x-1, y-6, numColor)
		// 底部横线
		for i := -3; i <= 3; i++ {
			img.Set(x+i, y+6, numColor)
		}

	case 2:
		// 画一个清晰的 "2"
		// 顶部横线
		for i := -4; i <= 4; i++ {
			img.Set(x+i, y-6, numColor)
		}
		// 右上竖线
		for i := -6; i <= -2; i++ {
			img.Set(x+4, y+i, numColor)
		}
		// 中间横线
		for i := -4; i <= 4; i++ {
			img.Set(x+i, y-1, numColor)
		}
		// 左下竖线
		for i := -1; i <= 5; i++ {
			img.Set(x-4, y+i, numColor)
		}
		// 底部横线
		for i := -4; i <= 4; i++ {
			img.Set(x+i, y+6, numColor)
		}

	case 3:
		// 画一个清晰的 "3"
		// 顶部横线
		for i := -4; i <= 4; i++ {
			img.Set(x+i, y-6, numColor)
		}
		// 右上竖线
		for i := -6; i <= -2; i++ {
			img.Set(x+4, y+i, numColor)
		}
		// 中间横线
		for i := -2; i <= 4; i++ {
			img.Set(x+i, y-1, numColor)
		}
		// 右下竖线
		for i := -1; i <= 5; i++ {
			img.Set(x+4, y+i, numColor)
		}
		// 底部横线
		for i := -4; i <= 4; i++ {
			img.Set(x+i, y+6, numColor)
		}
	}

	// 添加极强的噪声和干扰
	for i := 0; i < 50; i++ {
		noiseX := x + rand.Intn(41) - 20
		noiseY := y + rand.Intn(41) - 20
		if noiseX >= 0 && noiseX < imgW && noiseY >= 0 && noiseY < imgH {
			img.Set(noiseX, noiseY, color.RGBA{
				uint8(rand.Intn(255)),
				uint8(rand.Intn(255)),
				uint8(rand.Intn(255)),
				uint8(rand.Intn(200) + 55),
			})
		}
	}

	// 添加假数字干扰
	fakeColors := []color.RGBA{
		{200, 200, 200, 150}, // 灰色假数字
		{150, 150, 150, 120}, // 更淡的灰色
	}

	for fake := 0; fake < 2; fake++ {
		fakeX := x + rand.Intn(21) - 10
		fakeY := y + rand.Intn(21) - 10
		fakeColor := fakeColors[fake]

		// 画假的数字片段
		for i := 0; i < 8; i++ {
			fx := fakeX + rand.Intn(11) - 5
			fy := fakeY + rand.Intn(11) - 5
			if fx >= 0 && fx < imgW && fy >= 0 && fy < imgH {
				img.Set(fx, fy, fakeColor)
			}
		}
	}
}

// addOptimizedNoise 添加优化的噪点和干扰线条
func addOptimizedNoise(img *image.RGBA) {
	// 减少噪点数量从300到100，提高性能
	for i := 0; i < 100; i++ {
		noiseX := rand.Intn(imgW)
		noiseY := rand.Intn(imgH)
		img.Set(noiseX, noiseY, color.RGBA{
			uint8(rand.Intn(255)),
			uint8(rand.Intn(255)),
			uint8(rand.Intn(255)),
			uint8(rand.Intn(150) + 50),
		})
	}

	// 减少干扰线条数量从50到20，但增加线条密度
	for i := 0; i < 20; i++ {
		startX := rand.Intn(imgW)
		startY := rand.Intn(imgH)
		length := rand.Intn(40) + 20 // 增加线条长度

		// 使用更高效的线条绘制算法
		drawOptimizedLine(img, startX, startY, length)
	}
}

// drawOptimizedLine 绘制优化的干扰线条
func drawOptimizedLine(img *image.RGBA, startX, startY, length int) {
	// 预计算颜色，避免在循环中重复计算
	lineColor := color.RGBA{
		uint8(rand.Intn(255)),
		uint8(rand.Intn(255)),
		uint8(rand.Intn(255)),
		uint8(rand.Intn(180) + 75),
	}

	// 使用更高效的线条绘制
	angle := rand.Float64() * 2 * math.Pi
	dx := math.Cos(angle)
	dy := math.Sin(angle)

	for j := 0; j < length; j++ {
		x := int(float64(startX) + float64(j)*dx)
		y := int(float64(startY) + float64(j)*dy)

		if x >= 0 && x < imgW && y >= 0 && y < imgH {
			img.Set(x, y, lineColor)
		}
	}
}

// 生成验证码图片和点
func generateCaptcha() (string, string, []image.Point) {
	img := image.NewRGBA(image.Rect(0, 0, imgW, imgH))
	bg := randColor()
	draw.Draw(img, img.Bounds(), &image.Uniform{bg}, image.Point{}, draw.Src)

	points := make([]image.Point, numPoints)
	for i := 0; i < numPoints; i++ {
		var x, y int
		maxAttempts := 100 // 防止无限循环

		for attempt := 0; attempt < maxAttempts; attempt++ {
			x = rand.Intn(imgW-2*pointRadius) + pointRadius
			y = rand.Intn(imgH-2*pointRadius) + pointRadius

			// 检查与已有点的距离
			valid := true
			for j := 0; j < i; j++ {
				dx := x - points[j].X
				dy := y - points[j].Y
				distance := dx*dx + dy*dy
				if distance < minDistance*minDistance {
					valid = false
					break
				}
			}

			if valid {
				break
			}
		}

		points[i] = image.Point{x, y}
		// 画随机颜色的圆点
		circleColor := color.RGBA{
			uint8(rand.Intn(100) + 100), // 红色分量 100-199
			uint8(rand.Intn(100) + 50),  // 绿色分量 50-149
			uint8(rand.Intn(100) + 50),  // 蓝色分量 50-149
			255,
		}

		for dx := -pointRadius; dx <= pointRadius; dx++ {
			for dy := -pointRadius; dy <= pointRadius; dy++ {
				if dx*dx+dy*dy <= pointRadius*pointRadius {
					img.Set(x+dx, y+dy, circleColor)
				}
			}
		}

		// 在红点中心画数字标记（简单的像素画法）
		drawNumber(img, x, y, i+1)
	}

	// 优化后的干扰生成 - 减少噪点数量，提高性能
	addOptimizedNoise(img)

	// 添加假圆圈干扰
	for i := 0; i < 5; i++ {
		fakeX := rand.Intn(imgW-40) + 20
		fakeY := rand.Intn(imgH-40) + 20
		fakeRadius := rand.Intn(15) + 5

		for dx := -fakeRadius; dx <= fakeRadius; dx++ {
			for dy := -fakeRadius; dy <= fakeRadius; dy++ {
				if dx*dx+dy*dy <= fakeRadius*fakeRadius {
					if fakeX+dx >= 0 && fakeX+dx < imgW && fakeY+dy >= 0 && fakeY+dy < imgH {
						img.Set(fakeX+dx, fakeY+dy, color.RGBA{
							uint8(rand.Intn(150) + 50),
							uint8(rand.Intn(150) + 50),
							uint8(rand.Intn(150) + 50),
							uint8(rand.Intn(100) + 50),
						})
					}
				}
			}
		}
	}

	buf := new(bytes.Buffer)
	png.Encode(buf, img)
	b64 := base64.StdEncoding.EncodeToString(buf.Bytes())
	id := RandString(16)
	return id, b64, points
}

// 生成随机字符串
var letters = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")

func RandString(n int) string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	b := make([]rune, n)
	for i := range b {
		b[i] = letters[r.Intn(len(letters))]
	}
	return string(b)
}

// 准备验证码API（登录流程用，无需认证）
func captchaPrepareHandler(c *gin.Context) {
	type PrepareReq struct {
		Username string `json:"username"`
		Password string `json:"password"`
	}
	var req PrepareReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, 400, "Invalid request format", err.Error())
		return
	}

	id, b64, points := generateCaptcha()
	err := captchaManager.Store(id, CaptchaData{
		Points: points,
		Time:   time.Now(),
		UserID: 0, // 登录流程时暂时设为0
	})
	if err != nil {
		c.JSON(500, gin.H{"error": "验证码存储失败: " + err.Error()})
		return
	}

	// 打印红圆圈的坐标用于调试（可通过系统设置控制）
	if isCaptchaDebugEnabled() {
		fmt.Printf("生成的红圆圈坐标: ")
		for i, p := range points {
			fmt.Printf("Point%d(%d,%d) ", i, p.X, p.Y)
		}
		fmt.Println()
	}

	c.JSON(200, gin.H{
		"id":    id,
		"img":   "data:image/png;base64," + b64,
		"count": numPoints,
	})
}

// 生成验证码API（已登录用户用）
func captchaHandler(c *gin.Context) {
	userID := c.GetInt("user_id")

	// 检查用户授权
	auth, err := GetUserAuthorization(userID)
	if err != nil {
		c.JSON(401, gin.H{"error": "No valid authorization found"})
		return
	}

	if !auth.IsValid() {
		c.JSON(401, gin.H{"error": "Authorization expired or exhausted"})
		return
	}

	id, b64, points := generateCaptcha()
	err = captchaManager.Store(id, CaptchaData{Points: points, Time: time.Now(), UserID: userID})
	if err != nil {
		c.JSON(500, gin.H{"error": "验证码存储失败: " + err.Error()})
		return
	}

	// 打印红圆圈的坐标用于调试（可通过系统设置控制）
	if isCaptchaDebugEnabled() {
		fmt.Printf("生成的红圆圈坐标: ")
		for i, p := range points {
			fmt.Printf("Point%d(%d,%d) ", i, p.X, p.Y)
		}
		fmt.Println()
	}

	c.JSON(200, gin.H{
		"id":             id,
		"img":            "data:image/png;base64," + b64,
		"count":          numPoints,
		"remaining_uses": auth.GetRemainingUses(),
	})
}

// 校准坐标API
func calibrateHandler(c *gin.Context) {
	type CalibrationReq struct {
		Click image.Point `json:"click"`
	}
	var req CalibrationReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, 400, "参数错误", err.Error())
		return
	}

	// 校准点击调试输出（可通过系统设置控制）
	if isCaptchaDebugEnabled() {
		fmt.Printf("校准点击: 浏览器坐标(%d,%d)\n", req.Click.X, req.Click.Y)
	}
	c.JSON(200, gin.H{
		"msg":      "校准成功",
		"received": req.Click,
	})
}

// 验证码+登录验证API
func verifyLoginHandler(c *gin.Context) {
	type Req struct {
		ID       string        `json:"id"`
		Clicks   []image.Point `json:"clicks"`
		Username string        `json:"username"`
		Password string        `json:"password"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil {
		handleError(c, http.StatusBadRequest, "参数错误", err.Error())
		return
	}

	// 首先验证验证码
	data, ok := captchaManager.Load(req.ID)
	if !ok {
		c.JSON(400, gin.H{"error": "验证码已过期"})
		return
	}
	if time.Since(data.Time) > captchaExpire {
		captchaManager.Delete(req.ID) // 清理过期验证码
		c.JSON(400, gin.H{"error": "验证码已过期"})
		return
	}
	if len(req.Clicks) != len(data.Points) {
		c.JSON(400, gin.H{"error": "点数不符"})
		return
	}

	// 验证点击位置
	for i, click := range req.Clicks {
		point := data.Points[i]
		dx, dy := point.X-click.X, point.Y-click.Y
		distance := math.Sqrt(float64(dx*dx + dy*dy))

		// 验证码点击验证调试输出（可通过系统设置控制）
		if isCaptchaDebugEnabled() {
			fmt.Printf("Click %d: (%d,%d) -> Point%d(%d,%d) Distance:%.1f\n",
				i, click.X, click.Y, i, point.X, point.Y, distance)
		}

		if distance > float64(toleranceRadius) {
			c.JSON(400, gin.H{"error": fmt.Sprintf("请按顺序点击第%d个红点", i+1)})
			return
		}
	}

	// 验证码通过，立即清理验证码
	captchaManager.Delete(req.ID)

	// 现在验证用户名密码
	user, err := GetUserByUsername(req.Username)
	if err != nil {
		// 记录登录失败日志
		LogOperation(nil, "LOGIN_FAILED", "AUTH", "用户名不存在", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "账号或密码错误")
		c.JSON(401, gin.H{"error": "账号或密码错误"})
		return
	}

	if !ValidateUserPassword(user, req.Password) {
		// 记录登录失败日志
		LogOperation(&user.ID, "LOGIN_FAILED", "AUTH", "密码错误", "", user.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "账号或密码错误")
		c.JSON(401, gin.H{"error": "账号或密码错误"})
		return
	}

	if !user.IsActive {
		// 记录登录失败日志
		LogOperation(&user.ID, "LOGIN_FAILED", "AUTH", "账号已被禁用", "", user.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "账号已被禁用")
		c.JSON(401, gin.H{"error": "账号已被禁用"})
		return
	}

	// 检查后台登录权限
	if !user.CanLoginBackend {
		// 记录登录失败日志
		LogOperation(&user.ID, "LOGIN_FAILED", "AUTH", "无后台登录权限", "", user.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "该账户无后台登录权限")
		c.JSON(401, gin.H{"error": "该账户无后台登录权限"})
		return
	}

	// 检查账户限制
	if user.UsageControlEnabled {
		// 检查是否过期
		if user.ExpireTime != nil && time.Now().After(*user.ExpireTime) {
			// 记录登录失败日志
			LogOperation(&user.ID, "LOGIN_FAILED", "AUTH", "账户已过期", "", user.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "账户已过期")
			c.JSON(401, gin.H{"error": "账户已过期，请联系管理员"})
			return
		}

		// 检查使用次数限制
		if user.CurrentUsage >= user.MaxUsage {
			// 记录登录失败日志
			LogOperation(&user.ID, "LOGIN_FAILED", "AUTH", "使用次数已达上限", "", user.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "使用次数已达上限")
			c.JSON(401, gin.H{"error": "使用次数已达上限，请联系管理员"})
			return
		}

		// 增加使用次数
		_, err = db.Exec("UPDATE users SET current_usage = current_usage + 1 WHERE id = ?", user.ID)
		if err != nil {
			log.Printf("Failed to update user usage count: %v", err)
		}

		// 更新用户当前使用次数（用于返回给前端）
		user.CurrentUsage++
	}

	// 获取用户授权
	auth, err := GetUserAuthorization(user.ID)
	if err != nil {
		c.JSON(401, gin.H{"error": "无有效授权"})
		return
	}

	if !auth.IsValid() {
		c.JSON(401, gin.H{"error": "授权已过期或已用尽"})
		return
	}

	// 生成JWT令牌
	token, err := GenerateToken(user.ID, user.Username)
	if err != nil {
		c.JSON(500, gin.H{"error": "生成令牌失败"})
		return
	}

	// 记录验证码日志
	LogCaptchaAttempt(user.ID, req.ID, true, c.ClientIP(), c.GetHeader("User-Agent"))

	// 记录登录成功日志
	LogOperation(&user.ID, "LOGIN", "AUTH", "用户登录成功", "", user.Username, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	// 发送MQTT消息
	if GlobalMQTTServer != nil {
		authResult := map[string]interface{}{
			"type":           "auth_result",
			"user_id":        user.ID,
			"username":       user.Username,
			"success":        true,
			"token":          token,
			"remaining_uses": auth.GetRemainingUses(),
			"expires_at":     auth.ExpiresAt.Unix(),
			"timestamp":      time.Now().Unix(),
		}
		GlobalMQTTServer.PublishMessage("ipc/auth/result", authResult)
	}

	c.JSON(200, gin.H{
		"success": true,
		"token":   token,
		"user":    *user,
		"auth":    *auth,
	})
}

// 校验验证码API（已登录用户用）
func verifyHandler(c *gin.Context) {
	type Req struct {
		ID     string        `json:"id"`
		Clicks []image.Point `json:"clicks"`
	}
	var req Req
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"msg": "参数错误"})
		return
	}
	data, ok := captchaManager.Load(req.ID)
	if !ok {
		c.JSON(400, gin.H{"msg": "验证码已过期"})
		return
	}
	if time.Since(data.Time) > captchaExpire {
		captchaManager.Delete(req.ID) // 清理过期验证码
		c.JSON(400, gin.H{"msg": "验证码已过期"})
		return
	}
	if len(req.Clicks) != len(data.Points) {
		c.JSON(400, gin.H{"msg": "点数不符"})
		return
	}
	// 要求严格按顺序点击
	for i, click := range req.Clicks {
		point := data.Points[i]
		dx, dy := point.X-click.X, point.Y-click.Y
		distance := math.Sqrt(float64(dx*dx + dy*dy))

		// 验证码点击验证调试输出（可通过系统设置控制）
		if isCaptchaDebugEnabled() {
			fmt.Printf("Click %d: (%d,%d) -> Point%d(%d,%d) Distance:%.1f\n",
				i, click.X, click.Y, i, point.X, point.Y, distance)
		}

		// 检查是否在容错范围内
		if distance > float64(toleranceRadius) {
			c.JSON(400, gin.H{"msg": fmt.Sprintf("请按顺序点击第%d个红点", i+1)})
			return
		}
	}

	// 验证码通过，立即清理验证码
	captchaManager.Delete(req.ID)

	// 更新使用次数
	userID := data.UserID
	if userID > 0 {
		auth, err := GetUserAuthorization(userID)
		if err == nil {
			UpdateAuthorizationUsage(auth.ID)

			// 记录验证码日志
			LogCaptchaAttempt(userID, req.ID, true, c.ClientIP(), c.GetHeader("User-Agent"))

			// 发送MQTT消息
			if GlobalMQTTServer != nil {
				username := c.GetString("username")
				captchaResult := map[string]interface{}{
					"type":      "captcha_result",
					"user_id":   userID,
					"username":  username,
					"success":   true,
					"ip":        c.ClientIP(),
					"timestamp": time.Now().Unix(),
				}
				GlobalMQTTServer.PublishMessage("ipc/captcha/result", captchaResult)
			}

			c.JSON(200, gin.H{
				"msg":            "验证通过",
				"remaining_uses": auth.GetRemainingUses() - 1,
			})
			return
		}
	}

	c.JSON(200, gin.H{"msg": "验证通过"})
}

// 登录API
func loginHandler(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	user, err := GetUserByUsername(req.Username)
	if err != nil {
		c.JSON(401, gin.H{"error": "Invalid username or password"})
		return
	}

	// 使用bcrypt验证密码
	if !ValidateUserPassword(user, req.Password) {
		c.JSON(401, gin.H{"error": "Invalid username or password"})
		return
	}

	if !user.IsActive {
		c.JSON(401, gin.H{"error": "Account is disabled"})
		return
	}

	// 获取用户授权
	auth, err := GetUserAuthorization(user.ID)
	if err != nil {
		c.JSON(401, gin.H{"error": "No valid authorization found"})
		return
	}

	if !auth.IsValid() {
		c.JSON(401, gin.H{"error": "Authorization expired or exhausted"})
		return
	}

	// 生成JWT令牌
	token, err := GenerateToken(user.ID, user.Username)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to generate token"})
		return
	}

	// 发送MQTT消息
	if GlobalMQTTServer != nil {
		authResult := map[string]interface{}{
			"type":           "auth_result",
			"user_id":        user.ID,
			"username":       user.Username,
			"success":        true,
			"token":          token,
			"remaining_uses": auth.GetRemainingUses(),
			"expires_at":     auth.ExpiresAt.Unix(),
			"timestamp":      time.Now().Unix(),
		}
		GlobalMQTTServer.PublishMessage("ipc/auth/result", authResult)
	}

	c.JSON(200, LoginResponse{
		Success: true,
		Token:   token,
		User:    *user,
		Auth:    *auth,
	})
}

// 获取用户授权状态API
func authStatusHandler(c *gin.Context) {
	userID := c.GetInt("user_id")

	auth, err := GetUserAuthorization(userID)
	if err != nil {
		c.JSON(404, gin.H{"error": "Authorization not found"})
		return
	}

	c.JSON(200, gin.H{
		"is_valid":       auth.IsValid(),
		"remaining_uses": auth.GetRemainingUses(),
		"expires_at":     auth.ExpiresAt,
		"max_usage":      auth.MaxUsage,
		"usage_count":    auth.UsageCount,
	})
}

// 获取用户列表API
func getUsersHandler(c *gin.Context) {
	// 获取分页参数
	page := 1
	pageSize := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取搜索关键词
	keyword := c.Query("keyword")

	// 构建安全的查询条件
	var whereClause string
	var args []interface{}

	if keyword != "" {
		// 验证关键词输入
		validator := NewInputValidator()
		if err := validator.ValidateInput("keyword", keyword); err != nil {
			c.JSON(400, gin.H{"error": "Invalid search keyword: " + err.Error()})
			return
		}

		whereClause = "(username LIKE ? OR phone LIKE ? OR remark LIKE ?)"
		likeKeyword := "%" + keyword + "%"
		args = append(args, likeKeyword, likeKeyword, likeKeyword)
	} else {
		whereClause = "1=1"
	}

	// 查询总数
	var totalCount int
	countQuery := "SELECT COUNT(*) FROM users WHERE " + whereClause
	err := db.QueryRow(countQuery, args...).Scan(&totalCount)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to count users"})
		return
	}

	// 查询分页数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT id, username, email, phone, remark, max_usage, current_usage,
		       expire_time, usage_control_enabled, can_login_backend, can_login_mqtt,
		       is_active, created_at, account_info_encryption, account_info_content
		FROM users
		WHERE ` + whereClause + `
		ORDER BY username ASC
		LIMIT ? OFFSET ?`

	queryArgs := append(args, pageSize, offset)
	rows, err := db.Query(dataQuery, queryArgs...)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch users"})
		return
	}
	defer rows.Close()

	users := []User{}
	for rows.Next() {
		var user User
		var email, phone, remark sql.NullString
		var maxUsage, currentUsage sql.NullInt64
		var expireTime sql.NullTime
		var usageControlEnabled, canLoginBackend, canLoginMqtt sql.NullBool
		var accountInfoEncryption sql.NullBool
		var accountInfoContent sql.NullString

		err := rows.Scan(&user.ID, &user.Username, &email, &phone, &remark,
			&maxUsage, &currentUsage, &expireTime, &usageControlEnabled,
			&canLoginBackend, &canLoginMqtt, &user.IsActive, &user.CreatedAt,
			&accountInfoEncryption, &accountInfoContent)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to scan user data"})
			return
		}

		if email.Valid {
			user.Email = email.String
		}
		if phone.Valid {
			user.Phone = phone.String
		}
		if remark.Valid {
			user.Remark = remark.String
		}
		if maxUsage.Valid {
			user.MaxUsage = int(maxUsage.Int64)
		} else {
			user.MaxUsage = 20
		}
		if currentUsage.Valid {
			user.CurrentUsage = int(currentUsage.Int64)
		}
		if expireTime.Valid {
			user.ExpireTime = &expireTime.Time
		}
		if usageControlEnabled.Valid {
			user.UsageControlEnabled = usageControlEnabled.Bool
		} else {
			user.UsageControlEnabled = true
		}
		if canLoginBackend.Valid {
			user.CanLoginBackend = canLoginBackend.Bool
		}
		if canLoginMqtt.Valid {
			user.CanLoginMqtt = canLoginMqtt.Bool
		} else {
			user.CanLoginMqtt = true
		}

		// 处理授权信息发送配置字段
		if accountInfoEncryption.Valid {
			user.AccountInfoEncryption = accountInfoEncryption.Bool
		} else {
			user.AccountInfoEncryption = true
		}
		if accountInfoContent.Valid {
			user.AccountInfoContent = accountInfoContent.String
		} else {
			user.AccountInfoContent = "expire_time,max_usage"
		}

		users = append(users, user)
	}

	c.JSON(200, gin.H{
		"users":       users,
		"total":       totalCount,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (totalCount + pageSize - 1) / pageSize,
	})
}

// 创建新用户API
func createUserHandler(c *gin.Context) {
	var req struct {
		Username              string   `json:"username" binding:"required"`
		Password              string   `json:"password" binding:"required"`
		Email                 string   `json:"email"`
		Phone                 string   `json:"phone"`
		Remark                string   `json:"remark"`
		MaxUsage              int      `json:"max_usage"`
		UsageControlEnabled   bool     `json:"usage_control_enabled"`
		CanLoginBackend       bool     `json:"can_login_backend"`
		CanLoginMqtt          bool     `json:"can_login_mqtt"`
		ExpireTime            string   `json:"expire_time"`
		IsActive              bool     `json:"is_active"`
		AccountInfoEncryption bool     `json:"account_info_encryption"`
		AccountInfoContent    []string `json:"account_info_content"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 输入验证
	validator := NewInputValidator()

	// 验证用户名
	if err := validator.ValidateInput("username", req.Username); err != nil {
		c.JSON(400, gin.H{"error": "Invalid username: " + err.Error()})
		return
	}

	// 验证密码
	if err := validator.ValidateInput("password", req.Password); err != nil {
		c.JSON(400, gin.H{"error": "Invalid password: " + err.Error()})
		return
	}

	// 验证邮箱
	if req.Email != "" {
		if err := validator.ValidateInput("email", req.Email); err != nil {
			c.JSON(400, gin.H{"error": "Invalid email: " + err.Error()})
			return
		}
	}

	// 验证手机号
	if req.Phone != "" {
		if err := validator.ValidateInput("phone", req.Phone); err != nil {
			c.JSON(400, gin.H{"error": "Invalid phone: " + err.Error()})
			return
		}
	}

	// 验证备注
	if req.Remark != "" {
		if err := validator.ValidateInput("remark", req.Remark); err != nil {
			c.JSON(400, gin.H{"error": "Invalid remark: " + err.Error()})
			return
		}
	}

	// 检查用户名是否已存在
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM users WHERE username = ?", req.Username).Scan(&count)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to check username"})
		return
	}

	if count > 0 {
		// 记录创建用户失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_USER_FAILED", "USER_MANAGEMENT", "用户名已存在", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "用户名已存在")
		c.JSON(400, gin.H{"error": "Username already exists"})
		return
	}

	// 设置默认值
	if req.MaxUsage <= 0 {
		req.MaxUsage = 20
	}

	// 解析到期时间
	var expireTime *time.Time
	if req.ExpireTime != "" {
		if parsed, err := time.Parse("2006-01-02 15:04:05", req.ExpireTime); err == nil {
			expireTime = &parsed
		} else {
			log.Printf("Failed to parse expire time '%s': %v", req.ExpireTime, err)
			// 记录创建用户失败日志
			currentUser := getCurrentUser(c)
			LogOperation(currentUser, "CREATE_USER_FAILED", "USER_MANAGEMENT", "到期时间格式错误", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Invalid expire time format")
			c.JSON(400, gin.H{"error": "Invalid expire time format"})
			return
		}
	}

	// 处理账户信息内容数组
	accountInfoContent := strings.Join(req.AccountInfoContent, ",")

	// 哈希密码
	hashedPassword, err := HashPassword(req.Password)
	if err != nil {
		// 记录密码哈希失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_USER_FAILED", "USER_MANAGEMENT", "密码哈希失败", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to hash password")
		c.JSON(500, gin.H{"error": "Failed to hash password"})
		return
	}

	// 创建用户
	result, err := db.Exec(`
		INSERT INTO users (username, password, email, phone, remark, max_usage, current_usage,
		                   expire_time, usage_control_enabled, can_login_backend, can_login_mqtt, is_active,
		                   account_info_encryption, account_info_content)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		req.Username, hashedPassword, req.Email, req.Phone, req.Remark, req.MaxUsage, 0,
		expireTime, req.UsageControlEnabled, req.CanLoginBackend, req.CanLoginMqtt, req.IsActive,
		req.AccountInfoEncryption, accountInfoContent)
	if err != nil {
		// 记录创建用户失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_USER_FAILED", "USER_MANAGEMENT", "数据库操作失败", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to create user")
		c.JSON(500, gin.H{"error": "Failed to create user"})
		return
	}

	userID, _ := result.LastInsertId()

	// 为新用户创建默认授权（1年，1000次使用）
	expiresAt := time.Now().AddDate(1, 0, 0)
	encryptedKey, err := CreateAuthorizationToken(int(userID), req.Username, 1000, expiresAt)
	if err != nil {
		// 记录创建用户失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_USER_FAILED", "USER_MANAGEMENT", "创建授权失败", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to create authorization token")
		c.JSON(500, gin.H{"error": "Failed to create authorization token"})
		return
	}

	_, err = db.Exec(`
		INSERT INTO authorizations (user_id, max_usage, expires_at, encrypted_key, is_active)
		VALUES (?, ?, ?, ?, ?)`,
		userID, 1000, expiresAt, encryptedKey, true)
	if err != nil {
		// 记录创建用户失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_USER_FAILED", "USER_MANAGEMENT", "创建授权失败", "", req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to create authorization")
		c.JSON(500, gin.H{"error": "Failed to create authorization"})
		return
	}

	// 记录创建用户成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "CREATE_USER", "USER_MANAGEMENT", "创建用户成功", string(rune(userID)), req.Username, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(201, gin.H{
		"message": "User created successfully",
		"user_id": userID,
	})
}

// getCurrentUser 获取当前用户ID
func getCurrentUser(c *gin.Context) *int {
	userID, exists := c.Get("user_id")
	if !exists {
		return nil
	}
	if uid, ok := userID.(int); ok {
		return &uid
	}
	return nil
}

// 更新用户API
func updateUserHandler(c *gin.Context) {
	userID := c.Param("id")

	var req struct {
		Username              string   `json:"username" binding:"required"`
		Password              *string  `json:"password"`
		Email                 string   `json:"email"`
		Phone                 string   `json:"phone"`
		Remark                string   `json:"remark"`
		MaxUsage              int      `json:"max_usage"`
		CurrentUsage          int      `json:"current_usage"`
		UsageControlEnabled   bool     `json:"usage_control_enabled"`
		CanLoginBackend       bool     `json:"can_login_backend"`
		CanLoginMqtt          bool     `json:"can_login_mqtt"`
		ExpireTime            string   `json:"expire_time"`
		IsActive              bool     `json:"is_active"`
		AccountInfoEncryption bool     `json:"account_info_encryption"`
		AccountInfoContent    []string `json:"account_info_content"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 输入验证
	validator := NewInputValidator()

	// 验证用户名
	if err := validator.ValidateInput("username", req.Username); err != nil {
		c.JSON(400, gin.H{"error": "Invalid username: " + err.Error()})
		return
	}

	// 验证邮箱
	if req.Email != "" {
		if err := validator.ValidateInput("email", req.Email); err != nil {
			c.JSON(400, gin.H{"error": "Invalid email: " + err.Error()})
			return
		}
	}

	// 验证手机号
	if req.Phone != "" {
		if err := validator.ValidateInput("phone", req.Phone); err != nil {
			c.JSON(400, gin.H{"error": "Invalid phone: " + err.Error()})
			return
		}
	}

	// 验证备注
	if req.Remark != "" {
		if err := validator.ValidateInput("remark", req.Remark); err != nil {
			c.JSON(400, gin.H{"error": "Invalid remark: " + err.Error()})
			return
		}
	}

	// 检查用户是否存在并获取用户名
	var currentUsername string
	err := db.QueryRow("SELECT username FROM users WHERE id = ?", userID).Scan(&currentUsername)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(404, gin.H{"error": "User not found"})
		} else {
			c.JSON(500, gin.H{"error": "Failed to check user existence"})
		}
		return
	}

	// 检查用户名是否被其他用户使用
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?", req.Username, userID).Scan(&count)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to check username"})
		return
	}

	if count > 0 {
		c.JSON(400, gin.H{"error": "Username already exists"})
		return
	}

	// 如果是admin超级管理员，禁止修改用户名和状态，强制设置正确的权限
	if currentUsername == "admin" {
		if req.Username != "admin" {
			c.JSON(403, gin.H{"error": "Cannot change admin username"})
			return
		}
		if !req.IsActive {
			c.JSON(403, gin.H{"error": "Cannot disable admin super administrator account"})
			return
		}
		// 强制设置admin账户的正确权限
		req.UsageControlEnabled = false // admin账户无账户限制
		req.CanLoginBackend = true      // admin账户默认可以登录后台
		req.CanLoginMqtt = true         // admin账户默认可以登录MQTT
	}

	// 设置默认值
	if req.MaxUsage <= 0 {
		req.MaxUsage = 20
	}

	// 解析到期时间
	var expireTime *time.Time
	if req.ExpireTime != "" {
		if parsed, err := time.Parse("2006-01-02 15:04:05", req.ExpireTime); err == nil {
			expireTime = &parsed
		} else {
			log.Printf("Failed to parse expire time '%s': %v", req.ExpireTime, err)
			// 记录更新用户失败日志
			currentUser := getCurrentUser(c)
			LogOperation(currentUser, "UPDATE_USER_FAILED", "USER_MANAGEMENT", "到期时间格式错误", userID, req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Invalid expire time format")
			c.JSON(400, gin.H{"error": "Invalid expire time format"})
			return
		}
	}

	// 处理账户信息内容数组
	accountInfoContent := strings.Join(req.AccountInfoContent, ",")

	// 更新用户信息
	if req.Password != nil && *req.Password != "" {
		// 如果提供了密码且不为空，先哈希密码再更新
		hashedPassword, err := HashPassword(*req.Password)
		if err != nil {
			// 记录密码哈希失败日志
			currentUser := getCurrentUser(c)
			LogOperation(currentUser, "UPDATE_USER_FAILED", "USER_MANAGEMENT", "密码哈希失败", userID, req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to hash password")
			c.JSON(500, gin.H{"error": "Failed to hash password"})
			return
		}

		_, err = db.Exec(`
			UPDATE users
			SET username = ?, password = ?, email = ?, phone = ?, remark = ?, max_usage = ?,
			    current_usage = ?, expire_time = ?, usage_control_enabled = ?,
			    can_login_backend = ?, can_login_mqtt = ?, is_active = ?,
			    account_info_encryption = ?, account_info_content = ?
			WHERE id = ?`,
			req.Username, hashedPassword, req.Email, req.Phone, req.Remark, req.MaxUsage,
			req.CurrentUsage, expireTime, req.UsageControlEnabled,
			req.CanLoginBackend, req.CanLoginMqtt, req.IsActive,
			req.AccountInfoEncryption, accountInfoContent, userID)
	} else {
		// 如果没有提供密码或密码为空，不更新密码
		_, err = db.Exec(`
			UPDATE users 
			SET username = ?, email = ?, phone = ?, remark = ?, max_usage = ?, 
			    current_usage = ?, expire_time = ?, usage_control_enabled = ?, 
			    can_login_backend = ?, can_login_mqtt = ?, is_active = ?,
			    account_info_encryption = ?, account_info_content = ?
			WHERE id = ?`,
			req.Username, req.Email, req.Phone, req.Remark, req.MaxUsage,
			req.CurrentUsage, expireTime, req.UsageControlEnabled,
			req.CanLoginBackend, req.CanLoginMqtt, req.IsActive,
			req.AccountInfoEncryption, accountInfoContent, userID)
	}

	if err != nil {
		// 记录更新用户失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "UPDATE_USER_FAILED", "USER_MANAGEMENT", "更新用户失败", userID, req.Username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to update user")
		c.JSON(500, gin.H{"error": "Failed to update user"})
		return
	}

	// 记录更新用户成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "UPDATE_USER", "USER_MANAGEMENT", "更新用户成功", userID, req.Username, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message": "User updated successfully",
	})
}

// 删除用户API
func deleteUserHandler(c *gin.Context) {
	userID := c.Param("id")

	// 检查用户是否存在并获取用户名
	var username string
	err := db.QueryRow("SELECT username FROM users WHERE id = ?", userID).Scan(&username)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(404, gin.H{"error": "User not found"})
		} else {
			c.JSON(500, gin.H{"error": "Failed to check user existence"})
		}
		return
	}

	// 禁止删除admin超级管理员账户
	if username == "admin" {
		// 记录删除用户失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "禁止删除admin账户", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Cannot delete admin super administrator account")
		c.JSON(403, gin.H{"error": "Cannot delete admin super administrator account"})
		return
	}

	// 开始事务
	tx, err := db.Begin()
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "开始事务失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to begin transaction")
		c.JSON(500, gin.H{"error": "Failed to begin transaction"})
		return
	}
	defer tx.Rollback() // 确保在函数退出时回滚未提交的事务

	// 删除用户相关的授权
	_, err = tx.Exec("DELETE FROM authorizations WHERE user_id = ?", userID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "删除授权记录失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to delete user authorizations")
		c.JSON(500, gin.H{"error": "Failed to delete user authorizations"})
		return
	}

	// 删除用户相关的验证码日志
	_, err = tx.Exec("DELETE FROM captcha_logs WHERE user_id = ?", userID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "删除验证码日志失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to delete user logs")
		c.JSON(500, gin.H{"error": "Failed to delete user logs"})
		return
	}

	// 删除用户相关的操作日志（可选，根据业务需求决定）
	_, err = tx.Exec("DELETE FROM operation_logs WHERE user_id = ?", userID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "删除操作日志失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to delete user operation logs")
		c.JSON(500, gin.H{"error": "Failed to delete user operation logs"})
		return
	}

	// 删除用户
	_, err = tx.Exec("DELETE FROM users WHERE id = ?", userID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "删除用户失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to delete user")
		c.JSON(500, gin.H{"error": "Failed to delete user"})
		return
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_USER_FAILED", "USER_MANAGEMENT", "提交事务失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to commit transaction")
		c.JSON(500, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// 记录删除用户成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "DELETE_USER", "USER_MANAGEMENT", "删除用户成功", userID, username, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message": "User deleted successfully",
	})
}

// 重置用户使用次数API
func resetUserUsageHandler(c *gin.Context) {
	userID := c.Param("id")

	// 检查用户是否存在并获取用户名
	var username string
	err := db.QueryRow("SELECT username FROM users WHERE id = ?", userID).Scan(&username)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(404, gin.H{"error": "User not found"})
		} else {
			c.JSON(500, gin.H{"error": "Failed to check user existence"})
		}
		return
	}

	// 重置使用次数
	_, err = db.Exec("UPDATE users SET current_usage = 0 WHERE id = ?", userID)
	if err != nil {
		// 记录重置失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "RESET_USAGE_FAILED", "USER_MANAGEMENT", "重置使用次数失败", userID, username, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "Failed to reset user usage count")
		c.JSON(500, gin.H{"error": "Failed to reset user usage count"})
		return
	}

	// 记录重置成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "RESET_USAGE", "USER_MANAGEMENT", "重置使用次数成功", userID, username, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":  "User usage count reset successfully",
		"username": username,
	})
}

// 批量启停用户API
func batchToggleUsersHandler(c *gin.Context) {
	var req struct {
		UserIDs []int  `json:"user_ids" binding:"required"`
		Action  string `json:"action" binding:"required"` // "enable" or "disable"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证操作类型
	if req.Action != "enable" && req.Action != "disable" {
		c.JSON(400, gin.H{"error": "Action must be 'enable' or 'disable'"})
		return
	}

	// 验证用户ID数组
	if len(req.UserIDs) == 0 {
		c.JSON(400, gin.H{"error": "User IDs array cannot be empty"})
		return
	}

	// 限制批量操作数量
	if len(req.UserIDs) > 100 {
		c.JSON(400, gin.H{"error": "Cannot operate on more than 100 users at once"})
		return
	}

	// 检查是否包含admin用户
	adminUserID := 0
	for _, userID := range req.UserIDs {
		var username string
		err := db.QueryRow("SELECT username FROM users WHERE id = ?", userID).Scan(&username)
		if err != nil {
			continue
		}
		if username == "admin" {
			adminUserID = userID
			break
		}
	}

	// 如果是禁用操作且包含admin用户，则拒绝
	if req.Action == "disable" && adminUserID > 0 {
		c.JSON(403, gin.H{"error": "Cannot disable admin super administrator account"})
		return
	}

	// 构建安全的IN查询参数
	placeholders := make([]string, len(req.UserIDs))
	args := make([]interface{}, len(req.UserIDs))
	for i, userID := range req.UserIDs {
		placeholders[i] = "?"
		args[i] = userID
	}

	// 获取要操作的用户信息
	inClause := strings.Join(placeholders, ",")
	query := "SELECT id, username FROM users WHERE id IN (" + inClause + ")"
	rows, err := db.Query(query, args...)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to query users"})
		return
	}
	defer rows.Close()

	var validUsers []struct {
		ID       int
		Username string
	}
	for rows.Next() {
		var user struct {
			ID       int
			Username string
		}
		if err := rows.Scan(&user.ID, &user.Username); err != nil {
			continue
		}
		validUsers = append(validUsers, user)
	}

	if len(validUsers) == 0 {
		c.JSON(404, gin.H{"error": "No valid users found"})
		return
	}

	// 执行批量操作
	isActive := req.Action == "enable"
	updateQuery := "UPDATE users SET is_active = ? WHERE id IN (" + inClause + ")"
	updateArgs := append([]interface{}{isActive}, args...)

	result, err := db.Exec(updateQuery, updateArgs...)
	if err != nil {
		// 记录批量操作失败日志
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "BATCH_TOGGLE_USERS_FAILED", "USER_MANAGEMENT",
			fmt.Sprintf("批量%s用户失败", req.Action), "", "", false,
			c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to update users"})
		return
	}

	rowsAffected, _ := result.RowsAffected()

	// 记录每个用户的操作日志
	currentUser := getCurrentUser(c)
	for _, user := range validUsers {
		operationType := "ENABLE_USER"
		detail := "启用用户"
		if req.Action == "disable" {
			operationType = "DISABLE_USER"
			detail = "禁用用户"
		}

		LogOperation(currentUser, operationType, "USER_MANAGEMENT", detail,
			fmt.Sprintf("%d", user.ID), user.Username, true,
			c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")
	}

	// 记录批量操作成功日志
	LogOperation(currentUser, "BATCH_TOGGLE_USERS", "USER_MANAGEMENT",
		fmt.Sprintf("批量%s了%d个用户", req.Action, rowsAffected), "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":         fmt.Sprintf("Successfully %sd %d users", req.Action, rowsAffected),
		"affected_count":  rowsAffected,
		"requested_count": len(req.UserIDs),
		"valid_users":     len(validUsers),
	})
}

// 设备管理API处理函数

// 获取用户设备列表API
func getUserDevicesHandler(c *gin.Context) {
	userID := c.Param("userId")
	if userID == "" {
		c.JSON(400, gin.H{"error": "User ID is required"})
		return
	}

	userIDInt, err := strconv.Atoi(userID)
	if err != nil {
		c.JSON(400, gin.H{"error": "Invalid user ID"})
		return
	}

	devices, err := GetDevicesByUserID(userIDInt)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get devices"})
		return
	}

	// 获取每个设备的授权信息
	var deviceList []map[string]interface{}
	for _, device := range devices {
		deviceInfo := map[string]interface{}{
			"id":          device.ID,
			"device_id":   device.DeviceID,
			"device_name": device.DeviceName,
			"user_id":     device.UserID,
			"device_type": device.DeviceType,
			"is_active":   device.IsActive,
			"created_at":  device.CreatedAt,
			"updated_at":  device.UpdatedAt,
		}

		// 获取设备授权信息
		if auth, err := GetDeviceAuthorization(device.DeviceID); err == nil {
			deviceInfo["authorization"] = map[string]interface{}{
				"max_usage":               auth.MaxUsage,
				"current_usage":           auth.CurrentUsage,
				"expire_time":             auth.ExpireTime,
				"usage_control_enabled":   auth.UsageControlEnabled,
				"account_info_encryption": auth.AccountInfoEncryption,
				"account_info_content":    auth.AccountInfoContent,
				"is_active":               auth.IsActive,
			}
		}

		deviceList = append(deviceList, deviceInfo)
	}

	c.JSON(200, gin.H{
		"devices": deviceList,
		"total":   len(deviceList),
	})
}

// 创建设备API
func createDeviceHandler(c *gin.Context) {
	var req struct {
		DeviceID      string `json:"device_id" binding:"required"`
		DeviceName    string `json:"device_name" binding:"required"`
		UserID        *int   `json:"user_id"` // 改为指针类型，允许为空
		DeviceType    string `json:"device_type"`
		IsActive      bool   `json:"is_active"`
		Authorization struct {
			MaxUsage              int        `json:"max_usage"`
			ExpireTime            *time.Time `json:"expire_time"`
			UsageControlEnabled   bool       `json:"usage_control_enabled"`
			AccountInfoEncryption bool       `json:"account_info_encryption"`
			AccountInfoContent    string     `json:"account_info_content"`
		} `json:"authorization"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证用户是否存在
	var userExists bool
	err := db.QueryRow("SELECT EXISTS(SELECT 1 FROM users WHERE id = ?)", req.UserID).Scan(&userExists)
	if err != nil || !userExists {
		c.JSON(400, gin.H{"error": "User does not exist"})
		return
	}

	// 检查设备ID是否已存在
	var deviceExists bool
	err = db.QueryRow("SELECT EXISTS(SELECT 1 FROM devices WHERE device_id = ?)", req.DeviceID).Scan(&deviceExists)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to check device existence"})
		return
	}
	if deviceExists {
		c.JSON(400, gin.H{"error": "Device ID already exists"})
		return
	}

	// 设置默认值
	if req.DeviceType == "" {
		req.DeviceType = "terminal"
	}
	if req.Authorization.AccountInfoContent == "" {
		req.Authorization.AccountInfoContent = "device_name,max_usage,current_usage,expire_time"
	}

	// 创建设备
	var userID int
	if req.UserID != nil {
		userID = *req.UserID
	} else {
		userID = 0 // 0 表示没有分配用户
	}
	err = CreateDevice(req.DeviceID, req.DeviceName, userID, req.DeviceType, req.IsActive)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_DEVICE_FAILED", "DEVICE_MANAGEMENT", "创建设备失败", "", req.DeviceID, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to create device"})
		return
	}

	// 创建设备授权配置
	err = CreateDeviceAuthorization(req.DeviceID, req.Authorization.MaxUsage, req.Authorization.ExpireTime, req.Authorization.UsageControlEnabled, req.Authorization.AccountInfoEncryption, req.Authorization.AccountInfoContent)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CREATE_DEVICE_AUTH_FAILED", "DEVICE_MANAGEMENT", "创建设备授权失败", "", req.DeviceID, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to create device authorization"})
		return
	}

	// 记录成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "CREATE_DEVICE", "DEVICE_MANAGEMENT", "创建设备成功", "", req.DeviceID, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(201, gin.H{
		"message":   "Device created successfully",
		"device_id": req.DeviceID,
	})
}

// 更新设备API
// 注意：deviceId 参数与 MQTT 客户端 ID 相同
func updateDeviceHandler(c *gin.Context) {
	deviceID := c.Param("deviceId") // deviceID 就是 MQTT 的 client_id
	if deviceID == "" {
		c.JSON(400, gin.H{"error": "Device ID is required"})
		return
	}

	var req struct {
		DeviceName    string `json:"device_name"`
		DeviceType    string `json:"device_type"`
		UserID        *int   `json:"user_id"` // 添加用户ID字段
		IsActive      *bool  `json:"is_active"`
		Authorization struct {
			MaxUsage              *int       `json:"max_usage"`
			ExpireTime            *time.Time `json:"expire_time"`
			ExpireTimeProvided    bool       `json:"-"` // 内部标记，用于区分是否提供了expire_time字段
			UsageControlEnabled   *bool      `json:"usage_control_enabled"`
			AccountInfoEncryption *bool      `json:"account_info_encryption"`
			AccountInfoContent    string     `json:"account_info_content"`
		} `json:"authorization"`
	}

	// 获取原始请求数据
	rawData, err := c.GetRawData()
	if err != nil {
		c.JSON(400, gin.H{"error": "Failed to read request data"})
		return
	}

	// 先解析为 map 来检测字段是否存在
	var rawReq map[string]interface{}
	if err := json.Unmarshal(rawData, &rawReq); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 重新绑定到结构体
	c.Request.Body = io.NopCloser(strings.NewReader(string(rawData)))
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 检查 expire_time 字段是否被提供
	if auth, ok := rawReq["authorization"].(map[string]interface{}); ok {
		if _, exists := auth["expire_time"]; exists {
			req.Authorization.ExpireTimeProvided = true
		}
	}

	// 检查设备是否存在
	_, err = GetDeviceByDeviceID(deviceID)
	if err != nil {
		c.JSON(404, gin.H{"error": "Device not found"})
		return
	}

	// 更新设备基本信息
	updateFields := []string{}
	updateArgs := []interface{}{}

	if req.DeviceName != "" {
		updateFields = append(updateFields, "device_name = ?")
		updateArgs = append(updateArgs, req.DeviceName)
	}
	if req.DeviceType != "" {
		updateFields = append(updateFields, "device_type = ?")
		updateArgs = append(updateArgs, req.DeviceType)
	}
	if req.UserID != nil {
		updateFields = append(updateFields, "user_id = ?")
		updateArgs = append(updateArgs, *req.UserID)
	}
	if req.IsActive != nil {
		updateFields = append(updateFields, "is_active = ?")
		updateArgs = append(updateArgs, *req.IsActive)
	}

	if len(updateFields) > 0 {
		updateFields = append(updateFields, "updated_at = CURRENT_TIMESTAMP")
		updateArgs = append(updateArgs, deviceID)

		query := "UPDATE devices SET " + strings.Join(updateFields, ", ") + " WHERE device_id = ?"
		_, err = db.Exec(query, updateArgs...)
		if err != nil {
			currentUser := getCurrentUser(c)
			LogOperation(currentUser, "UPDATE_DEVICE_FAILED", "DEVICE_MANAGEMENT", "更新设备失败", "", deviceID, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
			c.JSON(500, gin.H{"error": "Failed to update device"})
			return
		}
	}

	// 更新设备授权信息
	authUpdateFields := []string{}
	authUpdateArgs := []interface{}{}

	if req.Authorization.MaxUsage != nil {
		authUpdateFields = append(authUpdateFields, "max_usage = ?")
		authUpdateArgs = append(authUpdateArgs, *req.Authorization.MaxUsage)
	}
	if req.Authorization.ExpireTimeProvided {
		authUpdateFields = append(authUpdateFields, "expire_time = ?")
		authUpdateArgs = append(authUpdateArgs, req.Authorization.ExpireTime)
	}
	if req.Authorization.UsageControlEnabled != nil {
		authUpdateFields = append(authUpdateFields, "usage_control_enabled = ?")
		authUpdateArgs = append(authUpdateArgs, *req.Authorization.UsageControlEnabled)
	}
	if req.Authorization.AccountInfoEncryption != nil {
		authUpdateFields = append(authUpdateFields, "account_info_encryption = ?")
		authUpdateArgs = append(authUpdateArgs, *req.Authorization.AccountInfoEncryption)
	}
	if req.Authorization.AccountInfoContent != "" {
		authUpdateFields = append(authUpdateFields, "account_info_content = ?")
		authUpdateArgs = append(authUpdateArgs, req.Authorization.AccountInfoContent)
	}

	if len(authUpdateFields) > 0 {
		authUpdateFields = append(authUpdateFields, "updated_at = CURRENT_TIMESTAMP")
		authUpdateArgs = append(authUpdateArgs, deviceID)

		authQuery := "UPDATE device_authorizations SET " + strings.Join(authUpdateFields, ", ") + " WHERE device_id = ?"
		_, err = db.Exec(authQuery, authUpdateArgs...)
		if err != nil {
			currentUser := getCurrentUser(c)
			LogOperation(currentUser, "UPDATE_DEVICE_AUTH_FAILED", "DEVICE_MANAGEMENT", "更新设备授权失败", "", deviceID, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
			c.JSON(500, gin.H{"error": "Failed to update device authorization"})
			return
		}
	}

	// 记录成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "UPDATE_DEVICE", "DEVICE_MANAGEMENT", "更新设备成功", "", deviceID, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":   "Device updated successfully",
		"device_id": deviceID,
	})
}

// 删除设备API
// 注意：deviceId 参数与 MQTT 客户端 ID 相同
func deleteDeviceHandler(c *gin.Context) {
	deviceID := c.Param("deviceId") // deviceID 就是 MQTT 的 client_id
	if deviceID == "" {
		c.JSON(400, gin.H{"error": "Device ID is required"})
		return
	}

	// 检查设备是否存在
	_, err := GetDeviceByDeviceID(deviceID)
	if err != nil {
		c.JSON(404, gin.H{"error": "Device not found"})
		return
	}

	// 删除设备授权配置
	_, err = db.Exec("DELETE FROM device_authorizations WHERE device_id = ?", deviceID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_DEVICE_AUTH_FAILED", "DEVICE_MANAGEMENT", "删除设备授权失败", "", deviceID, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to delete device authorization"})
		return
	}

	// 删除设备
	_, err = db.Exec("DELETE FROM devices WHERE device_id = ?", deviceID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "DELETE_DEVICE_FAILED", "DEVICE_MANAGEMENT", "删除设备失败", "", deviceID, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to delete device"})
		return
	}

	// 记录成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "DELETE_DEVICE", "DEVICE_MANAGEMENT", "删除设备成功", "", deviceID, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":   "Device deleted successfully",
		"device_id": deviceID,
	})
}

// 获取所有设备列表API
func getAllDevicesHandler(c *gin.Context) {
	// 获取分页参数
	page := 1
	pageSize := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	// 获取搜索关键词
	keyword := c.Query("keyword")
	userID := c.Query("user_id")

	// 构建查询条件
	var whereClause string
	var args []interface{}

	conditions := []string{}
	if keyword != "" {
		conditions = append(conditions, "(d.device_id LIKE ? OR d.device_name LIKE ? OR u.username LIKE ?)")
		likeKeyword := "%" + keyword + "%"
		args = append(args, likeKeyword, likeKeyword, likeKeyword)
	}

	if userID != "" {
		if uid, err := strconv.Atoi(userID); err == nil {
			conditions = append(conditions, "d.user_id = ?")
			args = append(args, uid)
		}
	}

	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// 查询总数
	var totalCount int
	countQuery := `
		SELECT COUNT(*) 
		FROM devices d
		LEFT JOIN users u ON d.user_id = u.id
		` + whereClause
	err := db.QueryRow(countQuery, args...).Scan(&totalCount)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to count devices"})
		return
	}

	// 查询分页数据
	offset := (page - 1) * pageSize
	dataQuery := `
		SELECT d.id, d.device_id, d.device_name, d.user_id, d.device_type, d.is_active, 
		       d.created_at, d.updated_at, u.username,
		       da.max_usage, da.current_usage, da.expire_time, da.usage_control_enabled,
		       da.account_info_encryption, da.account_info_content, da.is_active as auth_active
		FROM devices d
		LEFT JOIN users u ON d.user_id = u.id
		LEFT JOIN device_authorizations da ON d.device_id = da.device_id
		` + whereClause + `
		ORDER BY d.created_at DESC
		LIMIT ? OFFSET ?`

	queryArgs := append(args, pageSize, offset)
	rows, err := db.Query(dataQuery, queryArgs...)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to fetch devices"})
		return
	}
	defer rows.Close()

	var devices []map[string]interface{}
	for rows.Next() {
		var device Device
		var username sql.NullString
		var maxUsage, currentUsage sql.NullInt64
		var expireTime sql.NullTime
		var usageControlEnabled, accountInfoEncryption, authActive sql.NullBool
		var accountInfoContent sql.NullString

		err := rows.Scan(&device.ID, &device.DeviceID, &device.DeviceName, &device.UserID,
			&device.DeviceType, &device.IsActive, &device.CreatedAt, &device.UpdatedAt,
			&username, &maxUsage, &currentUsage, &expireTime, &usageControlEnabled,
			&accountInfoEncryption, &accountInfoContent, &authActive)
		if err != nil {
			c.JSON(500, gin.H{"error": "Failed to scan device data"})
			return
		}

		deviceInfo := map[string]interface{}{
			"id":          device.ID,
			"device_id":   device.DeviceID,
			"device_name": device.DeviceName,
			"user_id":     device.UserID,
			"username":    username.String,
			"device_type": device.DeviceType,
			"is_active":   device.IsActive,
			"created_at":  device.CreatedAt,
			"updated_at":  device.UpdatedAt,
		}

		// 添加授权信息
		if maxUsage.Valid {
			authInfo := map[string]interface{}{
				"max_usage":               maxUsage.Int64,
				"current_usage":           currentUsage.Int64,
				"usage_control_enabled":   usageControlEnabled.Bool,
				"account_info_encryption": accountInfoEncryption.Bool,
				"account_info_content":    accountInfoContent.String,
				"is_active":               authActive.Bool,
			}

			// 只有当过期时间有效时才添加，否则设为 null
			if expireTime.Valid {
				authInfo["expire_time"] = expireTime.Time
			} else {
				authInfo["expire_time"] = nil
			}

			deviceInfo["authorization"] = authInfo
		}

		devices = append(devices, deviceInfo)
	}

	c.JSON(200, gin.H{
		"devices":     devices,
		"total":       totalCount,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (totalCount + pageSize - 1) / pageSize,
	})
}

// 重置设备使用次数API
// 注意：deviceId 参数与 MQTT 客户端 ID 相同
func resetDeviceUsageHandler(c *gin.Context) {
	deviceID := c.Param("deviceId") // deviceID 就是 MQTT 的 client_id
	if deviceID == "" {
		c.JSON(400, gin.H{"error": "Device ID is required"})
		return
	}

	// 检查设备是否存在
	device, err := GetDeviceByDeviceID(deviceID)
	if err != nil {
		c.JSON(404, gin.H{"error": "Device not found"})
		return
	}

	// 重置使用次数
	err = ResetDeviceUsage(deviceID)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "RESET_DEVICE_USAGE_FAILED", "DEVICE_MANAGEMENT", "重置设备使用次数失败", deviceID, device.DeviceName, false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to reset device usage"})
		return
	}

	// 记录成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "RESET_DEVICE_USAGE", "DEVICE_MANAGEMENT", "重置设备使用次数成功", deviceID, device.DeviceName, true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":     "Device usage reset successfully",
		"device_id":   deviceID,
		"device_name": device.DeviceName,
	})
}

// 批量启停设备API
func batchToggleDevicesHandler(c *gin.Context) {
	var req struct {
		DeviceIDs []string `json:"device_ids" binding:"required"`
		Action    string   `json:"action" binding:"required"` // "enable" or "disable"
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证操作类型
	if req.Action != "enable" && req.Action != "disable" {
		c.JSON(400, gin.H{"error": "Action must be 'enable' or 'disable'"})
		return
	}

	// 验证设备ID数组
	if len(req.DeviceIDs) == 0 {
		c.JSON(400, gin.H{"error": "Device IDs array cannot be empty"})
		return
	}

	// 限制批量操作数量
	if len(req.DeviceIDs) > 100 {
		c.JSON(400, gin.H{"error": "Cannot operate on more than 100 devices at once"})
		return
	}

	// 构建安全的IN查询参数
	placeholders := make([]string, len(req.DeviceIDs))
	args := make([]interface{}, len(req.DeviceIDs))
	for i, deviceID := range req.DeviceIDs {
		placeholders[i] = "?"
		args[i] = deviceID
	}

	// 获取要操作的设备信息
	inClause := strings.Join(placeholders, ",")
	query := "SELECT device_id, device_name FROM devices WHERE device_id IN (" + inClause + ")"
	rows, err := db.Query(query, args...)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to query devices"})
		return
	}
	defer rows.Close()

	var validDevices []struct {
		ID   string
		Name string
	}
	for rows.Next() {
		var device struct {
			ID   string
			Name string
		}
		if err := rows.Scan(&device.ID, &device.Name); err != nil {
			continue
		}
		validDevices = append(validDevices, device)
	}

	if len(validDevices) == 0 {
		c.JSON(404, gin.H{"error": "No valid devices found"})
		return
	}

	// 执行批量操作
	isActive := req.Action == "enable"
	updateQuery := "UPDATE devices SET is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE device_id IN (" + inClause + ")"
	updateArgs := append([]interface{}{isActive}, args...)

	result, err := db.Exec(updateQuery, updateArgs...)
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "BATCH_TOGGLE_DEVICES_FAILED", "DEVICE_MANAGEMENT",
			fmt.Sprintf("批量%s设备失败", req.Action), "", "", false,
			c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to update devices"})
		return
	}

	rowsAffected, _ := result.RowsAffected()

	// 记录每个设备的操作日志
	currentUser := getCurrentUser(c)
	for _, device := range validDevices {
		operationType := "ENABLE_DEVICE"
		detail := "启用设备"
		if req.Action == "disable" {
			operationType = "DISABLE_DEVICE"
			detail = "禁用设备"
		}

		LogOperation(currentUser, operationType, "DEVICE_MANAGEMENT", detail,
			device.ID, device.Name, true,
			c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")
	}

	// 记录批量操作成功日志
	LogOperation(currentUser, "BATCH_TOGGLE_DEVICES", "DEVICE_MANAGEMENT",
		fmt.Sprintf("批量%s了%d个设备", req.Action, rowsAffected), "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":         fmt.Sprintf("Successfully %sd %d devices", req.Action, rowsAffected),
		"affected_count":  rowsAffected,
		"requested_count": len(req.DeviceIDs),
		"valid_devices":   len(validDevices),
	})
}

// MQTT服务器统计信息API
func mqttStatsHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	stats := GlobalMQTTServer.GetStats()
	c.JSON(200, gin.H{
		"stats": stats,
	})
}

// MQTT消息发布API
func mqttPublishHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	var req struct {
		Topic   string      `json:"topic" binding:"required"`
		Message interface{} `json:"message" binding:"required"`
		QoS     int         `json:"qos"`
		Retain  bool        `json:"retain"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 将消息转换为字符串
	messageStr := fmt.Sprintf("%v", req.Message)

	// 如果设置了Retain，保存保持消息
	if req.Retain {
		GlobalMQTTServer.AddRetainedMessage(req.Topic, messageStr, req.QoS)
	}

	err := GlobalMQTTServer.PublishMessage(req.Topic, req.Message)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to publish message"})
		return
	}

	c.JSON(200, gin.H{
		"message": "Message published successfully",
		"topic":   req.Topic,
	})
}

// MQTT向指定客户端发送消息API
func mqttSendToClientHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	var req struct {
		ClientID string      `json:"client_id" binding:"required"`
		Topic    string      `json:"topic" binding:"required"`
		Message  interface{} `json:"message" binding:"required"`
		QoS      int         `json:"qos"`
		Retain   bool        `json:"retain"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Printf("MQTT send binding error: %v", err)
		c.JSON(400, gin.H{"error": "Invalid request format", "details": err.Error()})
		return
	}

	// 将消息转换为字符串
	messageStr := fmt.Sprintf("%v", req.Message)

	// 如果设置了Retain，保存保持消息
	if req.Retain {
		GlobalMQTTServer.AddRetainedMessage(req.Topic, messageStr, req.QoS)
	}

	err := GlobalMQTTServer.SendMessageToClient(req.ClientID, req.Topic, req.Message)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"message":   "Message sent successfully",
		"client_id": req.ClientID,
		"topic":     req.Topic,
	})
}

// MQTT配置更新API
func mqttUpdateConfigHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	var config MQTTConfig
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证配置
	if config.MaxFailedAttempts < 1 || config.MaxFailedAttempts > 10 {
		c.JSON(400, gin.H{"error": "MaxFailedAttempts must be between 1 and 10"})
		return
	}

	if config.BlacklistDuration < 1 || config.BlacklistDuration > 168 { // 最大7天
		c.JSON(400, gin.H{"error": "BlacklistDuration must be between 1 and 168 hours"})
		return
	}

	if config.HeartbeatPacketInterval < 500 || config.HeartbeatPacketInterval > 60000 {
		c.JSON(400, gin.H{"error": "HeartbeatPacketInterval must be between 500 and 60000 milliseconds"})
		return
	}

	if config.CleanupInterval < 1 || config.CleanupInterval > 1440 { // 最大24小时
		c.JSON(400, gin.H{"error": "CleanupInterval must be between 1 and 1440 minutes"})
		return
	}

	if config.HeartbeatInterval < 5 || config.HeartbeatInterval > 300 {
		c.JSON(400, gin.H{"error": "HeartbeatInterval must be between 5 and 300 seconds"})
		return
	}

	if config.HeartbeatTimeout < 30 || config.HeartbeatTimeout > 600 {
		c.JSON(400, gin.H{"error": "HeartbeatTimeout must be between 30 and 600 seconds"})
		return
	}

	// 逻辑验证：超时时间应该大于检查间隔
	if config.HeartbeatTimeout <= config.HeartbeatInterval {
		c.JSON(400, gin.H{"error": "HeartbeatTimeout must be greater than HeartbeatInterval"})
		return
	}

	GlobalMQTTServer.UpdateConfig(&config)

	c.JSON(200, gin.H{
		"message": "Configuration updated successfully",
		"config":  config,
	})
}

// MQTT黑名单移除API
func mqttRemoveBlacklistHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	ip := c.Param("ip")
	if ip == "" {
		c.JSON(400, gin.H{"error": "IP address is required"})
		return
	}

	success := GlobalMQTTServer.RemoveFromBlacklist(ip)
	if !success {
		c.JSON(404, gin.H{"error": "IP not found in blacklist"})
		return
	}

	c.JSON(200, gin.H{
		"message": "IP removed from blacklist successfully",
		"ip":      ip,
	})
}

// MQTT添加黑名单API
func mqttAddBlacklistHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	var req struct {
		IP       string `json:"ip" binding:"required"`
		Username string `json:"username"`
		Reason   string `json:"reason" binding:"required"`
		Hours    int    `json:"hours" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	if req.Hours < 1 || req.Hours > 168 { // 最大7天
		c.JSON(400, gin.H{"error": "Hours must be between 1 and 168"})
		return
	}

	GlobalMQTTServer.AddToBlacklistManually(req.IP, req.Username, req.Reason, req.Hours)

	c.JSON(200, gin.H{
		"message": "IP added to blacklist successfully",
		"ip":      req.IP,
		"hours":   req.Hours,
	})
}

// MQTT清除失败认证记录API
func mqttClearFailedAuthHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	ip := c.Param("ip")
	if ip == "" {
		c.JSON(400, gin.H{"error": "IP address is required"})
		return
	}

	success := GlobalMQTTServer.ClearFailedAuth(ip)
	if !success {
		c.JSON(404, gin.H{"error": "IP not found in failed auth records"})
		return
	}

	c.JSON(200, gin.H{
		"message": "Failed auth record cleared successfully",
		"ip":      ip,
	})
}

// 获取操作日志列表API
func getLogsHandler(c *gin.Context) {
	// 获取查询参数
	page := 1
	pageSize := 20

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 && ps <= 100 {
			pageSize = ps
		}
	}

	var userID *int
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uid, err := strconv.Atoi(userIDStr); err == nil {
			userID = &uid
		}
	}

	operationType := c.Query("operation_type")
	module := c.Query("module")
	startTime := c.Query("start_time")
	endTime := c.Query("end_time")
	keyword := c.Query("keyword")

	// 查询日志
	logs, total, err := GetOperationLogs(page, pageSize, userID, operationType, module, startTime, endTime, keyword)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get operation logs"})
		return
	}

	c.JSON(200, gin.H{
		"logs":        logs,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
		"total_pages": (total + pageSize - 1) / pageSize,
	})
}

// 获取操作日志统计API
func getLogStatsHandler(c *gin.Context) {
	stats, err := GetOperationLogStats()
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get operation log stats"})
		return
	}

	c.JSON(200, gin.H{
		"stats": stats,
	})
}

// 清理操作日志API
func clearLogsHandler(c *gin.Context) {
	// 只清理30天前的日志
	days := 30
	if daysStr := c.Query("days"); daysStr != "" {
		if d, err := strconv.Atoi(daysStr); err == nil && d > 0 && d <= 365 {
			days = d
		}
	}

	result, err := db.Exec(`
		DELETE FROM operation_logs 
		WHERE created_at < datetime('now', '-' || ? || ' days')
	`, days)
	if err != nil {
		// 记录清理日志失败
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "CLEAR_LOGS_FAILED", "SYSTEM", "清理操作日志失败", "", "", false, c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())
		c.JSON(500, gin.H{"error": "Failed to clear operation logs"})
		return
	}

	rowsAffected, _ := result.RowsAffected()

	// 记录清理日志成功
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "CLEAR_LOGS", "SYSTEM", fmt.Sprintf("清理了%d天前的操作日志", days), "", "", true, c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":       "Operation logs cleared successfully",
		"rows_affected": rowsAffected,
		"days":          days,
	})
}

// MQTT保持消息管理API处理函数

// mqttGetRetainedHandler 获取保持消息列表
func mqttGetRetainedHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	messages := GlobalMQTTServer.GetRetainedMessages()
	c.JSON(200, gin.H{
		"messages": messages,
	})
}

// mqttDeleteRetainedHandler 删除指定主题的保持消息
func mqttDeleteRetainedHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	var req struct {
		Topic string `json:"topic" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	success := GlobalMQTTServer.DeleteRetainedMessage(req.Topic)
	if !success {
		c.JSON(404, gin.H{"error": "Retained message not found"})
		return
	}

	c.JSON(200, gin.H{
		"message": "Retained message deleted successfully",
		"topic":   req.Topic,
	})
}

// mqttClearAllRetainedHandler 清空所有保持消息
func mqttClearAllRetainedHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	count := GlobalMQTTServer.ClearAllRetainedMessages()
	c.JSON(200, gin.H{
		"message": "All retained messages cleared successfully",
		"count":   count,
	})
}

// MQTT遗嘱消息管理API处理函数

// mqttGetWillHandler 获取遗嘱消息列表
func mqttGetWillHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	messages := GlobalMQTTServer.GetAllWillMessages()
	c.JSON(200, gin.H{
		"messages": messages,
	})
}

// mqttDeleteWillHandler 删除指定客户端的遗嘱消息
func mqttDeleteWillHandler(c *gin.Context) {
	if GlobalMQTTServer == nil {
		c.JSON(503, gin.H{"error": "MQTT server not running"})
		return
	}

	var req struct {
		ClientID string `json:"client_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 先检查遗嘱消息是否存在
	_, exists := GlobalMQTTServer.GetWillMessage(req.ClientID)
	if !exists {
		c.JSON(404, gin.H{"error": "Will message not found"})
		return
	}

	// 删除遗嘱消息
	success := GlobalMQTTServer.DeleteWillMessage(req.ClientID)
	if !success {
		c.JSON(500, gin.H{"error": "Failed to delete will message"})
		return
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "DELETE_WILL_MESSAGE", "MQTT",
		"手动删除遗嘱消息", req.ClientID, req.ClientID, true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":   "Will message deleted successfully",
		"client_id": req.ClientID,
	})
}

// 测试SSE广播的端点
func testSSEHandler(c *gin.Context) {
	if GlobalSSEHub != nil {
		GlobalSSEHub.BroadcastEvent("test_event", `{"message": "This is a test event", "timestamp": `+fmt.Sprintf("%d", time.Now().Unix())+`}`)
		c.JSON(200, gin.H{"message": "Test event sent"})
	} else {
		c.JSON(500, gin.H{"error": "SSE Hub not initialized"})
	}
}

// SSE处理函数
func sseHandler(c *gin.Context) {
	// 检查认证 - 支持URL参数中的token
	token := c.Query("token")
	if token == "" {
		// 如果URL参数中没有token，尝试从Header获取
		authHeader := c.GetHeader("Authorization")
		if bearerToken, found := strings.CutPrefix(authHeader, "Bearer "); found {
			token = bearerToken
		}
	}

	if token == "" {
		c.JSON(401, gin.H{"error": "Authorization token required"})
		return
	}

	// 验证token
	_, err := ValidateToken(token)
	if err != nil {
		c.JSON(401, gin.H{"error": "Invalid token"})
		return
	}

	// 设置SSE响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Headers", "Cache-Control")

	// 创建SSE客户端
	clientID := fmt.Sprintf("client_%d", time.Now().UnixNano())
	client := &SSEClient{
		ID:     clientID,
		Events: make(chan string, 10), // 缓冲10个事件
	}

	// 注册客户端
	GlobalSSEHub.register <- client

	// 发送初始连接确认
	c.SSEvent("connected", fmt.Sprintf(`{"client_id": "%s", "timestamp": %d}`, clientID, time.Now().Unix()))
	c.Writer.Flush()

	// 监听事件并发送给客户端
	for {
		select {
		case message, ok := <-client.Events:
			if !ok {
				return
			}
			c.Writer.WriteString(message)
			c.Writer.Flush()
		case <-c.Request.Context().Done():
			// 客户端断开连接
			GlobalSSEHub.unregister <- client
			return
		}
	}
}

// 系统配置管理API处理函数

// getSystemConfigHandler 获取所有系统配置
func getSystemConfigHandler(c *gin.Context) {
	configs, err := GetAllConfigs()
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get system configs"})
		return
	}

	c.JSON(200, gin.H{
		"configs": configs,
	})
}

// getConfigByCategoryHandler 按分类获取配置
func getConfigByCategoryHandler(c *gin.Context) {
	category := c.Param("category")
	if category == "" {
		c.JSON(400, gin.H{"error": "Category is required"})
		return
	}

	configs, err := GetConfigsByCategory(category)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get configs by category"})
		return
	}

	c.JSON(200, gin.H{
		"category": category,
		"configs":  configs,
	})
}

// getDebugStatusHandler 获取调试开关状态
func getDebugStatusHandler(c *gin.Context) {
	debugEnabled := GetConfigWithDefault("system", "frontend_debug_logs", "false")

	c.JSON(200, gin.H{
		"debug_logs_enabled": debugEnabled == "true",
	})
}

// updateSystemConfigHandler 批量更新系统配置
func updateSystemConfigHandler(c *gin.Context) {
	var req struct {
		Configs []struct {
			Category    string `json:"category" binding:"required"`
			Key         string `json:"key" binding:"required"`
			Value       string `json:"value" binding:"required"`
			ValueType   string `json:"value_type" binding:"required"`
			Description string `json:"description"`
		} `json:"configs" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 更新每个配置项
	for _, config := range req.Configs {
		if err := SetConfig(config.Category, config.Key, config.Value, config.ValueType, config.Description); err != nil {
			c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to update config %s.%s: %v", config.Category, config.Key, err)})
			return
		}
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "UPDATE_CONFIG", "SYSTEM",
		fmt.Sprintf("更新了%d个配置项", len(req.Configs)), "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message": "System configuration updated successfully",
		"updated": len(req.Configs),
	})
}

// getSecurityConfigHandler 获取安全配置
func getSecurityConfigHandler(c *gin.Context) {
	category := c.Query("category")
	if category == "" {
		category = "security"
	}

	configs, err := GetConfigsByCategory(category)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get security configs"})
		return
	}

	c.JSON(200, gin.H{
		"configs": configs,
	})
}

// updateSecurityConfigHandler 更新安全配置
func updateSecurityConfigHandler(c *gin.Context) {
	var req struct {
		Configs []struct {
			Category string `json:"category" binding:"required"`
			Key      string `json:"key" binding:"required"`
			Value    string `json:"value" binding:"required"`
		} `json:"configs" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 验证安全配置
	for _, config := range req.Configs {
		if err := validateSecurityConfig(config.Category, config.Key, config.Value); err != nil {
			c.JSON(400, gin.H{"error": fmt.Sprintf("Invalid config %s.%s: %s", config.Category, config.Key, err.Error())})
			return
		}
	}

	// 验证配置一致性
	if err := validateConfigConsistency(req.Configs); err != nil {
		c.JSON(400, gin.H{"error": fmt.Sprintf("Configuration consistency error: %s", err.Error())})
		return
	}

	// 更新配置
	for _, config := range req.Configs {
		// 对于特定的配置项，需要确保value_type正确
		if needsValueTypeCorrection(config.Key) {
			err := SetConfigWithCorrectType(config.Category, config.Key, config.Value)
			if err != nil {
				c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to update config %s.%s", config.Category, config.Key)})
				return
			}
		} else {
			err := SetConfigValue(config.Category, config.Key, config.Value)
			if err != nil {
				c.JSON(500, gin.H{"error": fmt.Sprintf("Failed to update config %s.%s", config.Category, config.Key)})
				return
			}
		}
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "UPDATE_SECURITY_CONFIG", "SECURITY",
		fmt.Sprintf("更新了%d个安全配置项", len(req.Configs)), "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message": "Security configuration updated successfully",
		"updated": len(req.Configs),
	})
}

// validateSecurityConfig 验证安全配置
func validateSecurityConfig(category, key, value string) error {
	switch category {
	case "security":
		return validateSecurityCategoryConfig(key, value)
	case "rate_limit":
		return validateRateLimitConfig(key, value)
	case "database":
		return validateDatabaseConfig(key, value)
	case "validation":
		return validateValidationConfig(key, value)
	default:
		return fmt.Errorf("unknown security category: %s", category)
	}
}

// validateSecurityCategoryConfig 验证security分类配置
func validateSecurityCategoryConfig(key, value string) error {
	switch key {
	case "cors_allowed_origins":
		// 验证CORS源地址格式
		origins := strings.Split(value, ",")
		for _, origin := range origins {
			origin = strings.TrimSpace(origin)
			if origin != "*" && !strings.HasPrefix(origin, "http://") && !strings.HasPrefix(origin, "https://") {
				return fmt.Errorf("invalid origin format: %s", origin)
			}
		}
	case "cors_allowed_methods":
		// 验证HTTP方法
		methods := strings.Split(value, ",")
		validMethods := map[string]bool{"GET": true, "POST": true, "PUT": true, "DELETE": true, "OPTIONS": true, "HEAD": true, "PATCH": true}
		for _, method := range methods {
			method = strings.TrimSpace(strings.ToUpper(method))
			if !validMethods[method] {
				return fmt.Errorf("invalid HTTP method: %s", method)
			}
		}
	case "cors_allow_credentials":
		if value != "true" && value != "false" {
			return fmt.Errorf("cors_allow_credentials must be true or false")
		}
	case "cors_max_age":
		if age, err := strconv.Atoi(value); err != nil || age < 0 || age > 86400*7 {
			return fmt.Errorf("cors_max_age must be between 0 and 604800")
		}
	}
	return nil
}

// validateRateLimitConfig 验证频率限制配置
func validateRateLimitConfig(key, value string) error {
	switch key {
	case "enable_rate_limit":
		if value != "true" && value != "false" {
			return fmt.Errorf("enable_rate_limit must be true or false")
		}
	case "requests_per_minute":
		if rpm, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("requests_per_minute must be a valid number")
		} else if rpm < 10 {
			return fmt.Errorf("requests_per_minute must be at least 10 (too low may block normal users)")
		} else if rpm > 1000 {
			return fmt.Errorf("requests_per_minute must not exceed 1000 (too high may impact server performance)")
		}
	case "burst_size":
		if burst, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("burst_size must be a valid number")
		} else if burst < 5 {
			return fmt.Errorf("burst_size must be at least 5 (too low may block page initialization)")
		} else if burst > 200 {
			return fmt.Errorf("burst_size must not exceed 200 (too high may allow abuse)")
		}
	case "cleanup_interval":
		if interval, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("cleanup_interval must be a valid number")
		} else if interval < 60 {
			return fmt.Errorf("cleanup_interval must be at least 60 seconds")
		} else if interval > 3600 {
			return fmt.Errorf("cleanup_interval must not exceed 3600 seconds")
		}
	}
	return nil
}

// validateDatabaseConfig 验证数据库配置
func validateDatabaseConfig(key, value string) error {
	switch key {
	case "max_open_conns":
		if conns, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("max_open_conns must be a valid number")
		} else if conns < 5 {
			return fmt.Errorf("max_open_conns must be at least 5 (too low may cause connection shortage)")
		} else if conns > 200 {
			return fmt.Errorf("max_open_conns must not exceed 200 (too high may exhaust system resources)")
		}
	case "max_idle_conns":
		if conns, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("max_idle_conns must be a valid number")
		} else if conns < 1 {
			return fmt.Errorf("max_idle_conns must be at least 1")
		} else if conns > 50 {
			return fmt.Errorf("max_idle_conns must not exceed 50 (too high may waste resources)")
		}
	case "conn_max_lifetime":
		if lifetime, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("conn_max_lifetime must be a valid number")
		} else if lifetime < 60 {
			return fmt.Errorf("conn_max_lifetime must be at least 60 seconds")
		} else if lifetime > 7200 {
			return fmt.Errorf("conn_max_lifetime must not exceed 7200 seconds (2 hours)")
		}
	case "conn_max_idle_time":
		if idleTime, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("conn_max_idle_time must be a valid number")
		} else if idleTime < 30 {
			return fmt.Errorf("conn_max_idle_time must be at least 30 seconds")
		} else if idleTime > 1800 {
			return fmt.Errorf("conn_max_idle_time must not exceed 1800 seconds (30 minutes)")
		}
	}
	return nil
}

// validateValidationConfig 验证输入验证配置
func validateValidationConfig(key, value string) error {
	switch key {
	case "max_username_length":
		if length, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("max_username_length must be a valid number")
		} else if length < 3 {
			return fmt.Errorf("max_username_length must be at least 3 characters")
		} else if length > 100 {
			return fmt.Errorf("max_username_length must not exceed 100 characters")
		}
	case "max_password_length":
		if length, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("max_password_length must be a valid number")
		} else if length < 6 {
			return fmt.Errorf("max_password_length must be at least 6 characters (security requirement)")
		} else if length > 256 {
			return fmt.Errorf("max_password_length must not exceed 256 characters")
		}
	case "max_phone_length":
		if length, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("max_phone_length must be a valid number")
		} else if length < 8 {
			return fmt.Errorf("max_phone_length must be at least 8 characters")
		} else if length > 30 {
			return fmt.Errorf("max_phone_length must not exceed 30 characters")
		}
	case "max_remark_length":
		if length, err := strconv.Atoi(value); err != nil {
			return fmt.Errorf("max_remark_length must be a valid number")
		} else if length < 10 {
			return fmt.Errorf("max_remark_length must be at least 10 characters")
		} else if length > 1000 {
			return fmt.Errorf("max_remark_length must not exceed 1000 characters")
		}
	case "enable_xss_protection":
		if value != "true" && value != "false" {
			return fmt.Errorf("enable_xss_protection must be true or false")
		}
	}
	return nil
}

// validateConfigConsistency 验证配置一致性
func validateConfigConsistency(configs []struct {
	Category string `json:"category" binding:"required"`
	Key      string `json:"key" binding:"required"`
	Value    string `json:"value" binding:"required"`
}) error {
	configMap := make(map[string]map[string]string)

	// 构建配置映射
	for _, config := range configs {
		if configMap[config.Category] == nil {
			configMap[config.Category] = make(map[string]string)
		}
		configMap[config.Category][config.Key] = config.Value
	}

	// 验证数据库配置一致性
	if dbConfig, exists := configMap["database"]; exists {
		if maxOpenStr, hasMaxOpen := dbConfig["max_open_conns"]; hasMaxOpen {
			if maxIdleStr, hasMaxIdle := dbConfig["max_idle_conns"]; hasMaxIdle {
				maxOpen, _ := strconv.Atoi(maxOpenStr)
				maxIdle, _ := strconv.Atoi(maxIdleStr)
				if maxIdle > maxOpen {
					return fmt.Errorf("max_idle_conns (%d) cannot be greater than max_open_conns (%d)", maxIdle, maxOpen)
				}
			}
		}

		if lifetimeStr, hasLifetime := dbConfig["conn_max_lifetime"]; hasLifetime {
			if idleTimeStr, hasIdleTime := dbConfig["conn_max_idle_time"]; hasIdleTime {
				lifetime, _ := strconv.Atoi(lifetimeStr)
				idleTime, _ := strconv.Atoi(idleTimeStr)
				if idleTime > lifetime {
					return fmt.Errorf("conn_max_idle_time (%d) should not exceed conn_max_lifetime (%d)", idleTime, lifetime)
				}
			}
		}
	}

	// 验证频率限制配置一致性
	if rateLimitConfig, exists := configMap["rate_limit"]; exists {
		if rpmStr, hasRpm := rateLimitConfig["requests_per_minute"]; hasRpm {
			if burstStr, hasBurst := rateLimitConfig["burst_size"]; hasBurst {
				rpm, _ := strconv.Atoi(rpmStr)
				burst, _ := strconv.Atoi(burstStr)
				if burst > rpm/2 {
					return fmt.Errorf("burst_size (%d) should not exceed half of requests_per_minute (%d)", burst, rpm)
				}
			}
		}
	}

	return nil
}

// ConfigReloadResult 配置重载结果
type ConfigReloadResult struct {
	Success  bool     `json:"success"`
	Message  string   `json:"message"`
	Reloaded []string `json:"reloaded"`
	Failed   []string `json:"failed"`
	Warnings []string `json:"warnings"`
}

// reloadConfigHandler 重新加载配置（增强版）
func reloadConfigHandler(c *gin.Context) {
	result := ConfigReloadResult{
		Success:  true,
		Reloaded: make([]string, 0),
		Failed:   make([]string, 0),
		Warnings: make([]string, 0),
	}

	// 重新初始化频率限制器
	if globalRateLimiter != nil {
		// 停止旧的频率限制器
		globalRateLimiter.Stop()
		// 创建新的频率限制器
		globalRateLimiter = NewRateLimiter()
		result.Reloaded = append(result.Reloaded, "rate_limiter")
		structuredLogger.Info("CONFIG", "Rate limiter reloaded with new configuration")
	} else {
		result.Warnings = append(result.Warnings, "rate_limiter not initialized")
	}

	// 重新配置数据库连接池
	if db != nil {
		configureDatabasePool(db)
		result.Reloaded = append(result.Reloaded, "database_pool")
		structuredLogger.Info("CONFIG", "Database connection pool reconfigured")
	} else {
		result.Failed = append(result.Failed, "database_pool: database not initialized")
		result.Success = false
	}

	// 重新配置日志级别
	if structuredLogger != nil {
		logLevelStr := GetConfigWithDefault("system", "log_level", "INFO")
		var newLevel LogLevel
		switch strings.ToUpper(logLevelStr) {
		case "DEBUG":
			newLevel = LogLevelDebug
		case "INFO":
			newLevel = LogLevelInfo
		case "WARN":
			newLevel = LogLevelWarn
		case "ERROR":
			newLevel = LogLevelError
		case "FATAL":
			newLevel = LogLevelFatal
		default:
			result.Warnings = append(result.Warnings, fmt.Sprintf("invalid log level: %s, using INFO", logLevelStr))
			newLevel = LogLevelInfo
		}
		structuredLogger.SetLevel(newLevel)
		result.Reloaded = append(result.Reloaded, "log_level")
		structuredLogger.Info("CONFIG", "Log level updated", map[string]interface{}{"level": newLevel.String()})
	}

	// 重新配置验证码管理器
	if captchaManager != nil {
		// 注意：验证码管理器的配置更改需要重启才能生效
		result.Warnings = append(result.Warnings, "captcha_manager configuration requires restart")
	}

	// 尝试重新配置MQTT服务器（如果支持热重载）
	if GlobalMQTTServer != nil {
		// MQTT服务器配置通常需要重启
		result.Warnings = append(result.Warnings, "mqtt_server configuration requires restart")
	}

	// 设置最终结果消息
	if result.Success {
		if len(result.Warnings) > 0 {
			result.Message = fmt.Sprintf("Configuration reloaded successfully with %d warnings", len(result.Warnings))
		} else {
			result.Message = "Configuration reloaded successfully"
		}
	} else {
		result.Message = fmt.Sprintf("Configuration reload completed with %d failures", len(result.Failed))
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "RELOAD_CONFIG", "SYSTEM", "重新加载系统配置", "", "", result.Success,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", result.Message)

	// 根据成功状态返回适当的HTTP状态码
	if result.Success {
		c.JSON(200, result)
	} else {
		c.JSON(500, result)
	}
}

// rotateJWTSecretHandler JWT密钥轮换处理函数
func rotateJWTSecretHandler(c *gin.Context) {
	err := rotateJWTSecret()
	if err != nil {
		currentUser := getCurrentUser(c)
		LogOperation(currentUser, "ROTATE_JWT_SECRET_FAILED", "SECURITY", "JWT密钥轮换失败", "", "", false,
			c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())

		c.JSON(500, gin.H{
			"error":   "Failed to rotate JWT secret",
			"details": err.Error(),
		})
		return
	}

	// 记录成功日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "ROTATE_JWT_SECRET", "SECURITY", "JWT密钥轮换成功", "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	username := c.GetString("username")
	structuredLogger.Info("SECURITY", "JWT secret rotated successfully", map[string]interface{}{
		"user": username,
		"ip":   c.ClientIP(),
	})

	c.JSON(200, gin.H{
		"message": "JWT secret rotated successfully",
		"warning": "All existing tokens will be invalidated",
	})
}

// healthCheckHandler 健康检查处理函数
func healthCheckHandler(c *gin.Context) {
	if healthChecker == nil {
		c.JSON(503, gin.H{"error": "Health checker not initialized"})
		return
	}

	results := healthChecker.CheckHealth()

	// 根据整体健康状态设置HTTP状态码
	if overall, ok := results["overall"].(map[string]interface{}); ok {
		if healthy, ok := overall["healthy"].(bool); ok && !healthy {
			c.JSON(503, results) // Service Unavailable
			return
		}
	}

	c.JSON(200, results)
}

// systemStatsHandler 系统统计信息处理函数
func systemStatsHandler(c *gin.Context) {
	stats := make(map[string]interface{})

	// 基本系统信息
	stats["uptime"] = time.Since(startTime).Seconds()
	stats["timestamp"] = time.Now().Unix()

	// Goroutine统计
	if goroutineManager != nil {
		stats["goroutines"] = map[string]interface{}{
			"managed_count": goroutineManager.GetRunningCount(),
			"managed_names": goroutineManager.GetRunningNames(),
		}
	}

	// 验证码管理器统计
	if captchaManager != nil {
		stats["captcha"] = captchaManager.GetStats()
	}

	// MQTT服务器统计
	if GlobalMQTTServer != nil {
		// 这里需要添加GetStats方法到MQTTServer
		stats["mqtt"] = map[string]interface{}{
			"running": GlobalMQTTServer.running,
			"port":    GlobalMQTTServer.Port,
		}
	}

	// 数据库统计
	if db != nil {
		dbStats := db.Stats()
		stats["database"] = map[string]interface{}{
			"open_connections":     dbStats.OpenConnections,
			"in_use":               dbStats.InUse,
			"idle":                 dbStats.Idle,
			"wait_count":           dbStats.WaitCount,
			"wait_duration":        dbStats.WaitDuration.Seconds(),
			"max_idle_closed":      dbStats.MaxIdleClosed,
			"max_idle_time_closed": dbStats.MaxIdleTimeClosed,
			"max_lifetime_closed":  dbStats.MaxLifetimeClosed,
		}
	}

	// 内存使用统计（简单版本）
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	stats["memory"] = map[string]interface{}{
		"alloc":       m.Alloc,
		"total_alloc": m.TotalAlloc,
		"sys":         m.Sys,
		"num_gc":      m.NumGC,
		"goroutines":  runtime.NumGoroutine(),
	}

	c.JSON(200, stats)
}

// corsConfigHandler 获取CORS配置
func corsConfigHandler(c *gin.Context) {
	defaultOrigins := "http://localhost:5173,http://127.0.0.1:5173,http://***********:5173,http://***********:5173,http://********:5173"
	allowedOrigins := strings.Split(GetConfigWithDefault("security", "cors_allowed_origins", defaultOrigins), ",")
	allowedMethods := strings.Split(GetConfigWithDefault("security", "cors_allowed_methods", "GET,POST,PUT,DELETE,OPTIONS"), ",")
	allowedHeaders := strings.Split(GetConfigWithDefault("security", "cors_allowed_headers", "Origin,Content-Type,Authorization"), ",")
	allowCredentials := GetConfigBool("security", "cors_allow_credentials", true)
	devMode := GetConfigBool("security", "cors_dev_mode", false)

	config := map[string]interface{}{
		"allowed_origins":   allowedOrigins,
		"allowed_methods":   allowedMethods,
		"allowed_headers":   allowedHeaders,
		"allow_credentials": allowCredentials,
		"dev_mode":          devMode,
		"max_age":           GetConfigInt("security", "cors_max_age", 86400),
	}

	c.JSON(200, config)
}

// updateCorsConfigHandler 更新CORS配置
func updateCorsConfigHandler(c *gin.Context) {
	type CorsConfigRequest struct {
		AllowedOrigins   []string `json:"allowed_origins"`
		AllowedMethods   []string `json:"allowed_methods"`
		AllowedHeaders   []string `json:"allowed_headers"`
		AllowCredentials bool     `json:"allow_credentials"`
		DevMode          bool     `json:"dev_mode"`
		MaxAge           int      `json:"max_age"`
	}

	var req CorsConfigRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format"})
		return
	}

	// 更新配置到数据库
	configs := map[string]string{
		"cors_allowed_origins":   strings.Join(req.AllowedOrigins, ","),
		"cors_allowed_methods":   strings.Join(req.AllowedMethods, ","),
		"cors_allowed_headers":   strings.Join(req.AllowedHeaders, ","),
		"cors_allow_credentials": fmt.Sprintf("%t", req.AllowCredentials),
		"cors_dev_mode":          fmt.Sprintf("%t", req.DevMode),
		"cors_max_age":           fmt.Sprintf("%d", req.MaxAge),
	}

	for key, value := range configs {
		// 根据配置项确定正确的value_type
		var valueType string
		switch key {
		case "cors_allow_credentials", "cors_dev_mode":
			valueType = "bool"
		case "cors_max_age":
			valueType = "int"
		default:
			valueType = "string"
		}

		_, err := db.Exec(`
			INSERT OR REPLACE INTO system_configs (category, key_name, value, value_type, description, updated_at)
			VALUES ('security', ?, ?, ?, 'CORS配置', CURRENT_TIMESTAMP)
		`, key, value, valueType)
		if err != nil {
			structuredLogger.Error("CONFIG", "Failed to update CORS config", map[string]interface{}{
				"key":   key,
				"error": err.Error(),
			})
			c.JSON(500, gin.H{"error": "Failed to update CORS configuration"})
			return
		}
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "UPDATE_CORS_CONFIG", "SECURITY", "更新CORS配置", "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	structuredLogger.Info("CONFIG", "CORS configuration updated", map[string]interface{}{
		"user":            c.GetString("username"),
		"allowed_origins": req.AllowedOrigins,
		"dev_mode":        req.DevMode,
	})

	c.JSON(200, gin.H{
		"message": "CORS configuration updated successfully",
		"warning": "Changes will take effect after configuration reload",
	})
}

// ensureDefaultCorsConfig 确保CORS配置包含默认的允许来源
func ensureDefaultCorsConfig() {
	// 检查是否已有CORS配置
	var existingOrigins string
	err := db.QueryRow("SELECT value FROM system_configs WHERE category = 'security' AND key_name = 'cors_allowed_origins'").Scan(&existingOrigins)

	if err == sql.ErrNoRows {
		// 没有配置，插入默认配置
		defaultOrigins := "http://localhost:5173,http://127.0.0.1:5173,http://***********:5173,http://***********:5173,http://********:5173"
		_, err = db.Exec(`
			INSERT INTO system_configs (category, key_name, value, value_type, description, updated_at)
			VALUES ('security', 'cors_allowed_origins', ?, 'string', 'CORS允许的源地址', CURRENT_TIMESTAMP)
		`, defaultOrigins)

		if err != nil {
			structuredLogger.Error("CONFIG", "Failed to insert default CORS config", map[string]interface{}{
				"error": err.Error(),
			})
		} else {
			structuredLogger.Info("CONFIG", "Default CORS configuration inserted", map[string]interface{}{
				"origins": defaultOrigins,
			})
		}
	} else if err != nil {
		structuredLogger.Error("CONFIG", "Failed to check CORS config", map[string]interface{}{
			"error": err.Error(),
		})
	} else {
		// 检查是否包含***********，如果没有则添加
		if !strings.Contains(existingOrigins, "http://***********:5173") {
			newOrigins := existingOrigins + ",http://***********:5173"
			_, err = db.Exec(`
				UPDATE system_config
				SET config_value = ?, updated_at = datetime('now')
				WHERE config_key = 'security.cors_allowed_origins'
			`, newOrigins)

			if err != nil {
				structuredLogger.Error("CONFIG", "Failed to update CORS config with ***********", map[string]interface{}{
					"error": err.Error(),
				})
			} else {
				structuredLogger.Info("CONFIG", "Added *********** to CORS allowed origins", map[string]interface{}{
					"new_origins": newOrigins,
				})
			}
		}
	}
}

// setupHealthChecks 设置健康检查
func setupHealthChecks() {
	healthChecker = NewHealthChecker()

	// 数据库健康检查
	healthChecker.RegisterCheck("database", func() (bool, string) {
		if db == nil {
			return false, "Database connection is nil"
		}

		err := db.Ping()
		if err != nil {
			return false, fmt.Sprintf("Database ping failed: %v", err)
		}

		return true, "Database connection is healthy"
	})

	// MQTT服务器健康检查
	healthChecker.RegisterCheck("mqtt", func() (bool, string) {
		if GlobalMQTTServer == nil {
			return false, "MQTT server is not initialized"
		}

		if !GlobalMQTTServer.running {
			return false, "MQTT server is not running"
		}

		return true, "MQTT server is running"
	})

	// 验证码管理器健康检查
	healthChecker.RegisterCheck("captcha", func() (bool, string) {
		if captchaManager == nil {
			return false, "Captcha manager is not initialized"
		}

		stats := captchaManager.GetStats()
		if usage, ok := stats["usage_ratio"].(float64); ok && usage > 0.9 {
			return false, fmt.Sprintf("Captcha store usage is high: %.2f%%", usage*100)
		}

		return true, "Captcha manager is healthy"
	})

	// Goroutine管理器健康检查
	healthChecker.RegisterCheck("goroutines", func() (bool, string) {
		if goroutineManager == nil {
			return false, "Goroutine manager is not initialized"
		}

		count := goroutineManager.GetRunningCount()
		if count == 0 {
			return false, "No managed goroutines are running"
		}

		return true, fmt.Sprintf("%d managed goroutines are running", count)
	})

	// 频率限制器健康检查
	healthChecker.RegisterCheck("rate_limiter", func() (bool, string) {
		if globalRateLimiter == nil {
			return false, "Rate limiter is not initialized"
		}

		return true, "Rate limiter is healthy"
	})
}

// ErrorHandler 统一错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 处理panic
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 记录错误日志
			log.Printf("Request error: %s %s - %v", c.Request.Method, c.Request.URL.Path, err.Err)

			// 根据错误类型返回不同响应
			if appErr, ok := err.Err.(*AppError); ok {
				// 记录操作日志
				LogOperation(nil, "ERROR", "SYSTEM", appErr.Message, "", "", false,
					c.ClientIP(), c.GetHeader("User-Agent"), "", "", appErr.Error())

				c.JSON(appErr.Code, gin.H{
					"error":   appErr.Message,
					"details": appErr.Details,
				})
			} else {
				// 未知错误
				LogOperation(nil, "UNKNOWN_ERROR", "SYSTEM", "Unknown system error", "", "", false,
					c.ClientIP(), c.GetHeader("User-Agent"), "", "", err.Error())

				c.JSON(500, gin.H{
					"error": "Internal server error",
				})
			}
		}
	}
}

// RecoveryHandler 恢复处理中间件
func RecoveryHandler() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			// 记录panic日志
			log.Printf("Panic recovered: %s %s - %s", c.Request.Method, c.Request.URL.Path, err)

			// 记录操作日志
			LogOperation(nil, "PANIC", "SYSTEM", "System panic recovered", "", "", false,
				c.ClientIP(), c.GetHeader("User-Agent"), "", "", err)

			c.JSON(500, gin.H{
				"error": "Internal server error",
			})
		} else {
			// 记录panic日志
			log.Printf("Panic recovered: %s %s - %v", c.Request.Method, c.Request.URL.Path, recovered)

			// 记录操作日志
			LogOperation(nil, "PANIC", "SYSTEM", "System panic recovered", "", "", false,
				c.ClientIP(), c.GetHeader("User-Agent"), "", "", fmt.Sprintf("%v", recovered))

			c.JSON(500, gin.H{
				"error": "Internal server error",
			})
		}
		c.Abort()
	})
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 自定义日志格式
		return fmt.Sprintf("[%s] %s %s %d %s %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.ClientIP,
			param.Method,
			param.StatusCode,
			param.Latency,
			param.Path,
			param.ErrorMessage,
		)
	})
}

func main() {
	// 记录启动时间
	startTime = time.Now()

	// 设置优雅关闭
	setupGracefulShutdown()

	// 初始化配置变量
	imgW, imgH = getEnvInt("CAPTCHA_WIDTH", 300), getEnvInt("CAPTCHA_HEIGHT", 150)
	pointRadius = getEnvInt("CAPTCHA_POINT_RADIUS", 20)
	toleranceRadius = getEnvInt("CAPTCHA_TOLERANCE_RADIUS", 20)
	minDistance = getEnvInt("CAPTCHA_MIN_DISTANCE", 80)
	numPoints = getEnvInt("CAPTCHA_NUM_POINTS", 3)
	captchaExpire = time.Duration(getEnvInt("CAPTCHA_EXPIRE_MINUTES", 3)) * time.Minute

	// 初始化结构化日志记录器
	structuredLogger = NewStructuredLogger(LogLevelInfo)

	// 初始化goroutine管理器
	goroutineManager = NewGoroutineManager()

	// 初始化验证码管理器（最大1000个验证码）
	captchaManager = NewCaptchaManager(1000)

	// 初始化数据库
	InitDatabase()

	// 初始化JWT密钥（必须在数据库初始化之后）
	initJWTSecret()

	// 初始化RSA密钥对
	if err := InitRSA(); err != nil {
		log.Printf("Warning: RSA key pair initialization failed: %v", err)
	}

	// 初始化全局频率限制器
	globalRateLimiter = NewRateLimiter()

	// 检查test2用户密码格式（调试用）
	checkUserPasswordFormat("test2")

	// 初始化SSE Hub
	GlobalSSEHub = NewSSEHub()
	goroutineManager.Start("sse-hub", func() {
		GlobalSSEHub.Run()
	})

	// 设置MQTT事件广播器
	GlobalEventBroadcaster = GlobalSSEHub

	// 初始化MQTT服务器
	if err := InitMQTTServer(1883); err != nil {
		fmt.Printf("MQTT server initialization failed: %v\n", err)
		// MQTT服务器失败不阻止程序启动
	}

	// 设置健康检查
	setupHealthChecks()

	// 确保CORS配置包含常用的本地IP地址
	ensureDefaultCorsConfig()

	r := gin.New()

	// 配置中间件
	r.Use(RequestLogger())       // 请求日志
	r.Use(RecoveryHandler())     // 恢复处理
	r.Use(ErrorHandler())        // 错误处理
	r.Use(setupCORS())           // CORS配置
	r.Use(RateLimitMiddleware()) // 频率限制

	// 公开API（不需要认证）
	r.POST("/api/login", loginHandler)
	r.POST("/api/calibrate", calibrateHandler)
	r.POST("/api/captcha/prepare", captchaPrepareHandler)   // 准备验证码（登录流程）
	r.POST("/api/captcha/verify-login", verifyLoginHandler) // 验证码+登录验证
	r.GET("/debug/status", getDebugStatusHandler)           // 调试状态不需要认证（使用不同路径避免冲突）
	r.GET("/health", healthCheckHandler)                    // 健康检查端点

	// SSE端点（内部处理认证）
	r.GET("/api/events", sseHandler)

	// 测试SSE的端点
	r.POST("/api/test-sse", AuthMiddleware(), testSSEHandler)

	// 需要认证的API
	auth := r.Group("/api")
	auth.Use(AuthMiddleware())
	{
		auth.GET("/captcha", captchaHandler)
		auth.POST("/captcha/verify", verifyHandler)
		auth.GET("/auth/status", authStatusHandler)

		// 用户管理API
		auth.GET("/users", getUsersHandler)
		auth.POST("/users", createUserHandler)
		auth.PUT("/users/:id", updateUserHandler)
		auth.DELETE("/users/:id", deleteUserHandler)
		auth.POST("/users/:id/reset-usage", resetUserUsageHandler)
		auth.POST("/users/batch-toggle", batchToggleUsersHandler)

		// 设备管理API
		auth.GET("/users/:userId/devices", getUserDevicesHandler)
		auth.GET("/devices", getAllDevicesHandler) // 获取所有设备列表
		auth.POST("/devices", createDeviceHandler)
		auth.PUT("/devices/:deviceId", updateDeviceHandler)
		auth.DELETE("/devices/:deviceId", deleteDeviceHandler)
		auth.POST("/devices/:deviceId/reset-usage", resetDeviceUsageHandler) // 重置设备使用次数
		auth.POST("/devices/batch-toggle", batchToggleDevicesHandler)        // 批量启停设备

		// 设备类型管理API
		auth.GET("/device-types", getDeviceTypesHandler)                   // 获取所有设备类型
		auth.GET("/device-types/active", getActiveDeviceTypesHandler)      // 获取启用的设备类型
		auth.POST("/device-types", createDeviceTypeHandler)                // 创建设备类型
		auth.PUT("/device-types/:id", updateDeviceTypeHandler)             // 更新设备类型
		auth.DELETE("/device-types/:id", deleteDeviceTypeHandler)          // 删除设备类型
		auth.DELETE("/device-types/clear-all", clearAllDeviceTypesHandler) // 清空所有设备类型

		// MQTT服务器管理API
		auth.GET("/mqtt/stats", mqttStatsHandler)
		auth.POST("/mqtt/publish", mqttPublishHandler)
		auth.POST("/mqtt/send", mqttSendToClientHandler)
		auth.PUT("/mqtt/config", mqttUpdateConfigHandler)
		auth.DELETE("/mqtt/blacklist/:ip", mqttRemoveBlacklistHandler)
		auth.POST("/mqtt/blacklist", mqttAddBlacklistHandler)
		auth.DELETE("/mqtt/failedauth/:ip", mqttClearFailedAuthHandler)

		// MQTT保持消息管理API
		auth.GET("/mqtt/retained", mqttGetRetainedHandler)
		auth.DELETE("/mqtt/retained", mqttDeleteRetainedHandler)
		auth.DELETE("/mqtt/retained/all", mqttClearAllRetainedHandler)

		// MQTT遗嘱消息管理API
		auth.GET("/mqtt/will", mqttGetWillHandler)
		auth.DELETE("/mqtt/will", mqttDeleteWillHandler)

		// 操作日志API
		auth.GET("/logs", getLogsHandler)
		auth.GET("/logs/stats", getLogStatsHandler)
		auth.DELETE("/logs/clear", clearLogsHandler)

		// 系统配置管理API
		auth.GET("/config", getSystemConfigHandler)
		auth.GET("/config/:category", getConfigByCategoryHandler)
		auth.PUT("/config", updateSystemConfigHandler)

		// 安全配置管理API
		auth.GET("/security/config", getSecurityConfigHandler)
		auth.PUT("/security/config", updateSecurityConfigHandler)
		auth.POST("/security/config/reload", reloadConfigHandler)
		auth.POST("/security/jwt/rotate", rotateJWTSecretHandler) // JWT密钥轮换

		// CORS配置管理API
		auth.GET("/security/cors", corsConfigHandler)       // 获取CORS配置
		auth.PUT("/security/cors", updateCorsConfigHandler) // 更新CORS配置

		// 系统监控API
		auth.GET("/system/stats", systemStatsHandler) // 系统统计信息

		// RSA证书管理API
		auth.GET("/certificates/rsa-info", getRSACertificateInfoHandler)
		auth.POST("/certificates/generate-rsa", generateRSACertificateHandler)
		auth.POST("/certificates/switch-to/:filename", switchRSACertificateHandler)
		auth.GET("/certificates/download/:filename", downloadCertificateHandler)
		auth.DELETE("/certificates/delete/:filename", deleteCertificateHandler)
		auth.GET("/certificates/debug-status", debugCertificateStatusHandler)
	}

	// 设置优雅关闭
	setupGracefulShutdown()

	fmt.Println("Server starting on 0.0.0.0:8080...")
	r.Run("0.0.0.0:8080")
}

// RSA证书管理API处理函数

// parseRSACertificateFile 解析RSA证书文件获取详细信息
func parseRSACertificateFile(certPath string) *RSACertificateInfo {
	// 读取证书文件
	certData, err := os.ReadFile(certPath)
	if err != nil {
		log.Printf("Failed to read certificate file %s: %v", certPath, err)
		return nil
	}

	// 解析PEM格式
	block, _ := pem.Decode(certData)
	if block == nil {
		log.Printf("Failed to decode PEM block from %s", certPath)
		return nil
	}

	// 解析X.509证书
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		log.Printf("Failed to parse certificate from %s: %v", certPath, err)
		return nil
	}

	// 获取文件名
	fileName := filepath.Base(certPath)

	// 计算到期天数
	daysToExpiry := int(time.Until(cert.NotAfter).Hours() / 24)
	if daysToExpiry < 0 {
		daysToExpiry = 0
	}

	// 获取密钥大小
	keySize := 0
	if rsaKey, ok := cert.PublicKey.(*rsa.PublicKey); ok {
		keySize = rsaKey.Size() * 8 // 转换为位数
	}

	return &RSACertificateInfo{
		Name:            fileName,
		PrivateKeyPath:  "",
		CertificatePath: certPath,
		Subject:         cert.Subject.String(),
		Issuer:          cert.Issuer.String(),
		NotBefore:       cert.NotBefore,
		NotAfter:        cert.NotAfter,
		IsExpired:       time.Now().After(cert.NotAfter),
		DaysToExpiry:    daysToExpiry,
		KeySize:         keySize,
		SerialNumber:    cert.SerialNumber.String(),
		IsCurrentlyUsed: false, // 将在调用处设置
		IsDefaultCert:   false, // 将在调用处设置
	}
}

// generateRSACertificateHandler 生成RSA证书
func generateRSACertificateHandler(c *gin.Context) {
	var req GenerateRSACertificateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request format: " + err.Error()})
		return
	}

	// 生成RSA证书
	certInfo, err := GenerateRSACertificate(req)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to generate RSA certificate: " + err.Error()})
		return
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "GENERATE_RSA_CERT", "SECURITY", "生成RSA证书",
		fmt.Sprintf("CN=%s,O=%s", req.CommonName, req.Organization), "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	// RSA证书生成完成，无需额外操作

	c.JSON(200, gin.H{
		"message":         "RSA certificate generated successfully",
		"certificate":     certInfo,
		"validity_period": req.ValidityPeriod.Label,
	})
}

// getRSACertificateInfoHandler 获取当前RSA证书信息
func getRSACertificateInfoHandler(c *gin.Context) {
	if globalKeyPair == nil {
		c.JSON(404, gin.H{"error": "No RSA key pair initialized"})
		return
	}

	// 查找RSA相关的证书文件
	rsaCerts := []RSACertificateInfo{}

	// 扫描certificate目录中的所有RSA证书文件
	certDir := "./certificate"

	if files, err := os.ReadDir(certDir); err == nil {
		for _, file := range files {
			if file.IsDir() {
				continue
			}

			fileName := file.Name()
			filePath := filepath.Join(certDir, fileName)

			// 检查是否为RSA证书文件
			if strings.HasSuffix(fileName, ".crt") || strings.HasSuffix(fileName, ".pem") {
				// 解析证书文件获取详细信息
				certInfo := parseRSACertificateFile(filePath)
				if certInfo != nil {
					// 判断是否为当前使用的证书
					certInfo.IsCurrentlyUsed = isCurrentlyUsedCertificate(filePath)

					// 判断是否为默认证书
					certInfo.IsDefaultCert = strings.Contains(fileName, "rsa_default")

					rsaCerts = append(rsaCerts, *certInfo)
				}
			}
		}
	}

	// 获取当前使用的RSA密钥对信息
	currentKeyInfo := GetCurrentRSAKeyPairInfo()

	c.JSON(200, gin.H{
		"key_pair_initialized": globalKeyPair != nil,
		"key_info":             currentKeyInfo,
		"rsa_certificates":     rsaCerts,
		"total_rsa_certs":      len(rsaCerts),
		"current_key_info":     currentKeyInfo, // 当前使用的密钥对信息
	})
}

// fileExists 检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// getCurrentCertificatePath 获取当前使用的证书路径
func getCurrentCertificatePath() string {
	statusFile := "./certificate/current_cert.txt"
	data, err := os.ReadFile(statusFile)
	if err != nil {
		// 如果状态文件不存在，默认使用默认证书
		return "./certificate/rsa_default.crt"
	}
	return strings.TrimSpace(string(data))
}

// setCurrentCertificatePath 设置当前使用的证书路径
func setCurrentCertificatePath(certPath string) error {
	statusFile := "./certificate/current_cert.txt"
	return os.WriteFile(statusFile, []byte(certPath), 0644)
}

// isCurrentlyUsedCertificate 检查证书是否为当前系统使用的证书
func isCurrentlyUsedCertificate(certPath string) bool {
	currentCertPath := getCurrentCertificatePath()

	// 转换为绝对路径进行比较
	absCertPath, err := filepath.Abs(certPath)
	if err != nil {
		log.Printf("Error getting absolute path for %s: %v", certPath, err)
		return false
	}

	absCurrentPath, err := filepath.Abs(currentCertPath)
	if err != nil {
		log.Printf("Error getting absolute path for %s: %v", currentCertPath, err)
		return false
	}

	// 标准化路径分隔符
	absCertPath = filepath.Clean(absCertPath)
	absCurrentPath = filepath.Clean(absCurrentPath)

	isMatch := absCertPath == absCurrentPath
	log.Printf("Certificate path comparison: %s vs %s = %v", absCertPath, absCurrentPath, isMatch)

	return isMatch
}

// debugCertificateStatusHandler 调试证书状态
func debugCertificateStatusHandler(c *gin.Context) {
	certDir := "./certificate"
	defaultCertPath := filepath.Join(certDir, "rsa_default.crt")

	// 检查默认证书是否为当前使用的证书
	isDefaultUsed := isCurrentlyUsedCertificate(defaultCertPath)

	// 获取当前globalKeyPair的信息
	var keyPairInfo map[string]interface{}
	if globalKeyPair != nil {
		keyPairInfo = map[string]interface{}{
			"initialized":  true,
			"public_key_n": globalKeyPair.PublicKey.N.String()[:50] + "...", // 只显示前50个字符
		}
	} else {
		keyPairInfo = map[string]interface{}{
			"initialized": false,
		}
	}

	c.JSON(200, gin.H{
		"default_cert_path": defaultCertPath,
		"is_default_used":   isDefaultUsed,
		"global_key_pair":   keyPairInfo,
		"certificate_files": func() []string {
			files, _ := os.ReadDir(certDir)
			var certFiles []string
			for _, file := range files {
				if strings.HasSuffix(file.Name(), ".crt") {
					certFiles = append(certFiles, file.Name())
				}
			}
			return certFiles
		}(),
	})
}

// parseRSAKeySize 从证书文件中解析RSA密钥长度
func parseRSAKeySize(certPath string) int {
	// 读取证书文件
	certData, err := os.ReadFile(certPath)
	if err != nil {
		log.Printf("Failed to read certificate file %s: %v", certPath, err)
		return 2048 // 返回默认值
	}

	// 解析PEM格式的证书
	block, _ := pem.Decode(certData)
	if block == nil {
		log.Printf("Failed to decode PEM block from certificate %s", certPath)
		return 2048 // 返回默认值
	}

	// 解析X.509证书
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		log.Printf("Failed to parse certificate %s: %v", certPath, err)
		return 2048 // 返回默认值
	}

	// 检查公钥类型并获取密钥长度
	switch publicKey := cert.PublicKey.(type) {
	case *rsa.PublicKey:
		// RSA密钥长度以位为单位
		return publicKey.N.BitLen()
	case *ecdsa.PublicKey:
		// ECDSA密钥，返回曲线的位长度
		return publicKey.Curve.Params().BitSize
	default:
		log.Printf("Unknown public key type in certificate %s", certPath)
		return 2048 // 返回默认值
	}
}

// downloadCertificateHandler 下载证书文件
func downloadCertificateHandler(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(400, gin.H{"error": "Filename is required"})
		return
	}

	// 安全检查：只允许下载certificate目录下的文件
	certDir := "./certificate"
	filePath := filepath.Join(certDir, filename)

	// 检查文件是否存在
	if !fileExists(filePath) {
		c.JSON(404, gin.H{"error": "Certificate file not found"})
		return
	}

	// 检查文件是否在certificate目录内（防止路径遍历攻击）
	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to resolve file path"})
		return
	}

	absCertDir, err := filepath.Abs(certDir)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to resolve certificate directory"})
		return
	}

	if !strings.HasPrefix(absFilePath, absCertDir) {
		c.JSON(403, gin.H{"error": "Access denied: file outside certificate directory"})
		return
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "DOWNLOAD_CERT", "SECURITY", "下载证书文件", filename, "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "application/octet-stream")

	// 发送文件
	c.File(filePath)
}

// deleteCertificateHandler 删除证书文件
func deleteCertificateHandler(c *gin.Context) {
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(400, gin.H{"error": "Filename is required"})
		return
	}

	// 安全检查：只允许删除certificate目录下的文件
	certDir := "./certificate"
	filePath := filepath.Join(certDir, filename)

	// 检查文件是否存在
	if !fileExists(filePath) {
		c.JSON(404, gin.H{"error": "Certificate file not found"})
		return
	}

	// 检查文件是否在certificate目录内（防止路径遍历攻击）
	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to resolve file path"})
		return
	}

	absCertDir, err := filepath.Abs(certDir)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to resolve certificate directory"})
		return
	}

	if !strings.HasPrefix(absFilePath, absCertDir) {
		c.JSON(403, gin.H{"error": "Access denied: file outside certificate directory"})
		return
	}

	// 不允许删除系统RSA密钥文件和默认证书
	if filename == "rsa_default.key" || filename == "rsa_default.pub" || filename == "rsa_default.crt" {
		c.JSON(403, gin.H{"error": "Cannot delete system key files or default certificate"})
		return
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		c.JSON(500, gin.H{"error": "Failed to delete certificate file: " + err.Error()})
		return
	}

	// 如果是证书文件，尝试删除对应的私钥文件
	if strings.HasSuffix(filename, ".crt") {
		keyFilename := strings.TrimSuffix(filename, ".crt") + ".key"
		keyFilePath := filepath.Join(certDir, keyFilename)
		if fileExists(keyFilePath) {
			os.Remove(keyFilePath) // 忽略错误，因为主要文件已删除
		}
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "DELETE_CERT", "SECURITY", "删除证书文件", filename, "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	// RSA证书切换完成，无需额外操作

	c.JSON(200, gin.H{
		"message":  "Certificate deleted successfully",
		"filename": filename,
	})
}

// switchRSACertificateHandler 切换系统使用的RSA证书
func switchRSACertificateHandler(c *gin.Context) {
	filename := c.Param("filename")
	log.Printf("Switch certificate request: filename=%s", filename)

	if filename == "" {
		c.JSON(400, gin.H{"error": "Filename is required"})
		return
	}

	// 安全检查：只允许切换certificate目录下的证书文件
	certDir := "./certificate"
	certPath := filepath.Join(certDir, filename)
	log.Printf("Certificate path: %s", certPath)

	// 检查文件是否存在
	if !fileExists(certPath) {
		log.Printf("Certificate file not found: %s", certPath)
		c.JSON(404, gin.H{"error": "Certificate file not found"})
		return
	}
	log.Printf("Certificate file exists: %s", certPath)

	// 检查文件是否在certificate目录内（防止路径遍历攻击）
	absCertPath, err := filepath.Abs(certPath)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to resolve certificate path"})
		return
	}

	absCertDir, err := filepath.Abs(certDir)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to resolve certificate directory"})
		return
	}

	if !strings.HasPrefix(absCertPath, absCertDir) {
		c.JSON(403, gin.H{"error": "Access denied: file outside certificate directory"})
		return
	}

	// 检查是否为证书文件
	if !strings.HasSuffix(filename, ".crt") {
		c.JSON(400, gin.H{"error": "Only certificate files (.crt) can be switched to"})
		return
	}

	// 不允许切换到系统密钥文件
	if filename == "rsa_default.key" || filename == "rsa_default.pub" {
		c.JSON(403, gin.H{"error": "Cannot switch to system key files"})
		return
	}

	// 默认证书也可以通过此API切换，无需特殊处理

	// 执行证书切换
	if err := SwitchToRSACertificate(certPath); err != nil {
		c.JSON(500, gin.H{"error": "Failed to switch certificate: " + err.Error()})
		return
	}

	// 更新当前证书状态
	if err := setCurrentCertificatePath(certPath); err != nil {
		log.Printf("Warning: Failed to update current certificate status: %v", err)
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "SWITCH_RSA_CERT", "SECURITY", "切换系统RSA证书", filename, "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{
		"message":  "RSA certificate switched successfully",
		"filename": filename,
		"note":     "System is now using the new certificate for encryption and authentication",
	})
}

func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}

// setupGracefulShutdown 设置优雅关闭
func setupGracefulShutdown() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("Received shutdown signal, starting graceful shutdown...")

		// 停止MQTT服务器
		if GlobalMQTTServer != nil {
			GlobalMQTTServer.Stop()
		}

		// 停止频率限制器
		if globalRateLimiter != nil {
			globalRateLimiter.Stop()
		}

		// 停止验证码管理器
		if captchaManager != nil {
			captchaManager.Stop()
			log.Println("Captcha manager stopped")
		}

		// 停止所有受管理的goroutine
		if goroutineManager != nil {
			goroutineManager.Stop()
		}

		// 关闭数据库连接
		if db != nil {
			db.Close()
			log.Println("Database connection closed")
		}

		log.Println("Graceful shutdown completed")
		os.Exit(0)
	}()
}

// CaptchaManager 验证码管理器
type CaptchaManager struct {
	store         sync.Map
	stopChan      chan struct{}
	cleanupTicker *time.Ticker
	maxSize       int   // 最大验证码数量限制
	currentSize   int64 // 当前验证码数量（原子操作）
}

// NewCaptchaManager 创建新的验证码管理器
func NewCaptchaManager(maxSize int) *CaptchaManager {
	cm := &CaptchaManager{
		stopChan: make(chan struct{}),
		maxSize:  maxSize,
	}
	cm.startCleanup()
	return cm
}

// Store 存储验证码（带容量检查）
func (cm *CaptchaManager) Store(key string, data CaptchaData) error {
	currentCount := atomic.LoadInt64(&cm.currentSize)
	if int(currentCount) >= cm.maxSize {
		// 触发紧急清理
		cm.emergencyCleanup()
		// 再次检查
		if int(atomic.LoadInt64(&cm.currentSize)) >= cm.maxSize {
			return fmt.Errorf("captcha store is full, max size: %d", cm.maxSize)
		}
	}

	cm.store.Store(key, data)
	atomic.AddInt64(&cm.currentSize, 1)
	return nil
}

// Load 加载验证码
func (cm *CaptchaManager) Load(key string) (CaptchaData, bool) {
	value, ok := cm.store.Load(key)
	if !ok {
		return CaptchaData{}, false
	}
	return value.(CaptchaData), true
}

// Delete 删除验证码
func (cm *CaptchaManager) Delete(key string) {
	if _, loaded := cm.store.LoadAndDelete(key); loaded {
		atomic.AddInt64(&cm.currentSize, -1)
	}
}

// startCleanup 启动清理协程
func (cm *CaptchaManager) startCleanup() {
	cm.cleanupTicker = time.NewTicker(2 * time.Minute) // 缩短清理间隔到2分钟
	go func() {
		defer cm.cleanupTicker.Stop()
		for {
			select {
			case <-cm.stopChan:
				return
			case <-cm.cleanupTicker.C:
				cm.cleanupExpired()
			}
		}
	}()
}

// cleanupExpired 清理过期的验证码
func (cm *CaptchaManager) cleanupExpired() {
	now := time.Now()
	var expiredKeys []string
	var totalCount int

	// 遍历所有验证码，收集过期的key
	cm.store.Range(func(key, value interface{}) bool {
		totalCount++
		data := value.(CaptchaData)
		if now.Sub(data.Time) > captchaExpire {
			expiredKeys = append(expiredKeys, key.(string))
		}
		return true
	})

	// 批量删除过期的验证码
	for _, key := range expiredKeys {
		cm.Delete(key)
	}

	// 更新计数器（防止计数器漂移）
	atomic.StoreInt64(&cm.currentSize, int64(totalCount-len(expiredKeys)))

	if len(expiredKeys) > 0 {
		log.Printf("Cleaned up %d expired captchas, remaining: %d", len(expiredKeys), totalCount-len(expiredKeys))
	}
}

// emergencyCleanup 紧急清理（当容量接近上限时）
func (cm *CaptchaManager) emergencyCleanup() {
	log.Println("Performing emergency captcha cleanup due to capacity limit")
	cm.cleanupExpired()

	// 如果清理后仍然接近上限，清理一些较老的验证码
	currentCount := atomic.LoadInt64(&cm.currentSize)
	if float64(currentCount) > float64(cm.maxSize)*0.8 {
		cm.cleanupOldest(int(currentCount) - cm.maxSize/2)
	}
}

// cleanupOldest 清理最老的验证码
func (cm *CaptchaManager) cleanupOldest(count int) {
	type captchaEntry struct {
		key  string
		time time.Time
	}

	var entries []captchaEntry
	cm.store.Range(func(key, value interface{}) bool {
		data := value.(CaptchaData)
		entries = append(entries, captchaEntry{
			key:  key.(string),
			time: data.Time,
		})
		return true
	})

	// 按时间排序
	sort.Slice(entries, func(i, j int) bool {
		return entries[i].time.Before(entries[j].time)
	})

	// 删除最老的验证码
	deleteCount := count
	if deleteCount > len(entries) {
		deleteCount = len(entries)
	}

	for i := 0; i < deleteCount; i++ {
		cm.Delete(entries[i].key)
	}

	if deleteCount > 0 {
		log.Printf("Emergency cleanup: removed %d oldest captchas", deleteCount)
	}
}

// Stop 停止验证码管理器
func (cm *CaptchaManager) Stop() {
	close(cm.stopChan)
	if cm.cleanupTicker != nil {
		cm.cleanupTicker.Stop()
	}
}

// GetStats 获取统计信息
func (cm *CaptchaManager) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"current_size": atomic.LoadInt64(&cm.currentSize),
		"max_size":     cm.maxSize,
		"usage_ratio":  float64(atomic.LoadInt64(&cm.currentSize)) / float64(cm.maxSize),
	}
}

// checkUserPasswordFormat 检查用户密码格式
func checkUserPasswordFormat(username string) {
	// 打开数据库连接
	localDB, err := sql.Open("sqlite", "./db/ipc_management.db")
	if err != nil {
		log.Printf("Failed to open database for password check: %v", err)
		return
	}
	defer localDB.Close()

	// 查询用户的密码
	var password string
	err = localDB.QueryRow("SELECT password FROM users WHERE username = ?", username).Scan(&password)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("User %s not found", username)
		} else {
			log.Printf("Failed to query user password: %v", err)
		}
		return
	}

	// 检查是否是bcrypt哈希格式
	if strings.HasPrefix(password, "$2a$") || strings.HasPrefix(password, "$2b$") || strings.HasPrefix(password, "$2y$") {
		log.Printf("User %s password format: bcrypt hash (secure)", username)
	} else {
		log.Printf("User %s password format: plaintext or other format (insecure)", username)
	}
}

// 设备类型管理API处理函数

// 获取所有设备类型API
func getDeviceTypesHandler(c *gin.Context) {
	deviceTypes, err := GetAllDeviceTypes()
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get device types: " + err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"device_types": deviceTypes,
		"total":        len(deviceTypes),
	})
}

// 获取启用的设备类型API
func getActiveDeviceTypesHandler(c *gin.Context) {
	deviceTypes, err := GetActiveDeviceTypes()
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to get active device types: " + err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"device_types": deviceTypes,
		"total":        len(deviceTypes),
	})
}

// 创建设备类型API
func createDeviceTypeHandler(c *gin.Context) {
	var req struct {
		TypeCode    string `json:"type_code" binding:"required"`
		TypeName    string `json:"type_name" binding:"required"`
		Description string `json:"description"`
		Icon        string `json:"icon"`
		Color       string `json:"color"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request data: " + err.Error()})
		return
	}

	// 设置默认值
	if req.Icon == "" {
		req.Icon = "fas fa-microchip"
	}
	if req.Color == "" {
		req.Color = "#6b7280"
	}

	// 确保用户创建的设备类型排序值至少为1（系统类型使用0）
	if req.SortOrder < 1 {
		req.SortOrder = 1
	}

	// 检查类型代码是否已存在
	existing, _ := GetDeviceTypeByCode(req.TypeCode)
	if existing != nil {
		c.JSON(400, gin.H{"error": "Device type code already exists"})
		return
	}

	err := CreateDeviceType(req.TypeCode, req.TypeName, req.Description, req.Icon, req.Color, req.SortOrder)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to create device type: " + err.Error()})
		return
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "CREATE_DEVICE_TYPE", "DEVICE_TYPE_MANAGEMENT",
		"创建设备类型", req.TypeCode, req.TypeName, true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(201, gin.H{"message": "Device type created successfully"})
}

// 更新设备类型API
func updateDeviceTypeHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{"error": "Invalid device type ID"})
		return
	}

	// 检查是否为系统类型
	var isSystem bool
	err = db.QueryRow("SELECT is_system FROM device_types WHERE id = ?", id).Scan(&isSystem)
	if err != nil {
		c.JSON(404, gin.H{"error": "Device type not found"})
		return
	}

	if isSystem {
		c.JSON(400, gin.H{"error": "Cannot edit system device type"})
		return
	}

	var req struct {
		TypeName    string `json:"type_name" binding:"required"`
		Description string `json:"description"`
		Icon        string `json:"icon"`
		Color       string `json:"color"`
		IsActive    bool   `json:"is_active"`
		SortOrder   int    `json:"sort_order"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"error": "Invalid request data: " + err.Error()})
		return
	}

	// 设置默认值
	if req.Icon == "" {
		req.Icon = "fas fa-microchip"
	}
	if req.Color == "" {
		req.Color = "#6b7280"
	}

	err = UpdateDeviceType(id, req.TypeName, req.Description, req.Icon, req.Color, req.IsActive, req.SortOrder)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to update device type: " + err.Error()})
		return
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "UPDATE_DEVICE_TYPE", "DEVICE_TYPE_MANAGEMENT",
		"更新设备类型", idStr, req.TypeName, true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{"message": "Device type updated successfully"})
}

// 删除设备类型API
func deleteDeviceTypeHandler(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{"error": "Invalid device type ID"})
		return
	}

	// 获取设备类型信息用于日志记录
	var typeName string
	err = db.QueryRow("SELECT type_name FROM device_types WHERE id = ?", id).Scan(&typeName)
	if err != nil {
		c.JSON(404, gin.H{"error": "Device type not found"})
		return
	}

	err = DeleteDeviceType(id)
	if err != nil {
		if err.Error() == "cannot delete system device type" {
			c.JSON(400, gin.H{"error": "Cannot delete system device type"})
		} else if strings.Contains(err.Error(), "devices are using this type") {
			c.JSON(400, gin.H{"error": err.Error()})
		} else {
			c.JSON(500, gin.H{"error": "Failed to delete device type: " + err.Error()})
		}
		return
	}

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "DELETE_DEVICE_TYPE", "DEVICE_TYPE_MANAGEMENT",
		"删除设备类型", idStr, typeName, true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	c.JSON(200, gin.H{"message": "Device type deleted successfully"})
}

// 清空所有设备类型API
func clearAllDeviceTypesHandler(c *gin.Context) {
	// 检查是否有使用非系统设备类型的设备
	var deviceCount int
	err := db.QueryRow(`
		SELECT COUNT(*) FROM devices d
		JOIN device_types dt ON d.device_type = dt.type_code
		WHERE dt.is_system = 0
	`).Scan(&deviceCount)
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to check devices: " + err.Error()})
		return
	}

	if deviceCount > 0 {
		c.JSON(400, gin.H{"error": "Cannot clear device types: there are devices using user-defined device types. Please change their types or delete them first."})
		return
	}

	// 只删除非系统设备类型，保护系统类型
	result, err := db.Exec("DELETE FROM device_types WHERE is_system = 0")
	if err != nil {
		c.JSON(500, gin.H{"error": "Failed to clear device types: " + err.Error()})
		return
	}

	rowsAffected, _ := result.RowsAffected()

	// 记录操作日志
	currentUser := getCurrentUser(c)
	LogOperation(currentUser, "CLEAR_ALL_DEVICE_TYPES", "DEVICE_TYPE_MANAGEMENT",
		"清空所有设备类型", "", "", true,
		c.ClientIP(), c.GetHeader("User-Agent"), "", "", "")

	if rowsAffected == 0 {
		c.JSON(200, gin.H{
			"message":       "No user-defined device types to clear. Only system types exist.",
			"cleared_count": 0,
		})
	} else {
		c.JSON(200, gin.H{
			"message":       "User-defined device types cleared successfully",
			"cleared_count": rowsAffected,
		})
	}
}
