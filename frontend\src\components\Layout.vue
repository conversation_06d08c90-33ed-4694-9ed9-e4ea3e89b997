<template>
  <div class="app-layout">
    <!-- 侧边栏 -->
    <div class="modern-sidebar" :class="{ 'mobile-hidden': !showMobileSidebar }">
      <div class="sidebar-content">
        <div class="sidebar-brand">
          <h1>终端管理</h1>
          <div class="version">{{ getDisplayVersion() }}</div>
        </div>
        
        <nav class="sidebar-nav">
          <div 
            v-for="item in menuItems" 
            :key="item.key"
            class="nav-item"
            :class="{ active: activeMenu === item.key }"
            @click="handleMenuSelect(item.key)"
          >
            <i :class="item.icon"></i>
            <span>{{ item.label }}</span>
          </div>
        </nav>
      </div>
    </div>

    <!-- 移动端菜单按钮 -->
    <button class="mobile-menu-btn" @click="showMobileSidebar = !showMobileSidebar">
      <i class="fas fa-bars"></i>
    </button>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <div class="modern-header">
        <h1 class="header-title">{{ getMenuTitle(activeMenu) }}</h1>
        <div class="header-actions">
          <div class="user-info">
            <div class="user-avatar">{{ username.charAt(0).toUpperCase() }}</div>
            <span class="user-name">{{ username }}</span>
          </div>
          <button class="modern-btn btn-danger" @click="logout">
            <i class="fas fa-sign-out-alt"></i>
            退出登录
          </button>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="modern-content">
        <slot></slot>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div 
      v-if="showMobileSidebar" 
      class="mobile-overlay"
      @click="showMobileSidebar = false"
    ></div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { getDisplayVersion } from '../config/version'

// Props
const props = defineProps({
  username: {
    type: String,
    required: true
  },
  activeMenu: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['menu-select', 'logout'])

// 状态
const showMobileSidebar = ref(false)

// 菜单项
const menuItems = [
  { key: 'dashboard', label: '仪表盘', icon: 'fas fa-tachometer-alt' },
  { key: 'users', label: '用户管理', icon: 'fas fa-users' },
  { key: 'devices', label: '设备管理', icon: 'fas fa-desktop' },
  { key: 'mqtt', label: 'MQTT管理', icon: 'fas fa-exchange-alt' },
  { key: 'logs', label: '操作日志', icon: 'fas fa-list-alt' },
  { key: 'settings', label: '系统设置', icon: 'fas fa-cog' }
]

// 计算属性
const getMenuTitle = computed(() => (menu) => {
  const item = menuItems.find(item => item.key === menu)
  return item ? item.label : '仪表盘'
})

// 方法
const handleMenuSelect = (menu) => {
  showMobileSidebar.value = false
  emit('menu-select', menu)
}

const logout = () => {
  emit('logout')
}
</script>

<style scoped>
/* Layout 样式 */
.app-layout {
  display: flex;
  height: 100vh;
  background: #f8fafc;
}

.modern-sidebar {
  width: 250px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.sidebar-content {
  padding: 0;
}

.sidebar-brand {
  padding: 2rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.sidebar-brand h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.version {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  display: inline-block;
}

.sidebar-nav {
  padding: 1rem 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: #3498db;
}

.nav-item.active {
  background: rgba(52, 152, 219, 0.2);
  border-left-color: #3498db;
}

.nav-item i {
  width: 20px;
  margin-right: 0.75rem;
  font-size: 1rem;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.modern-header {
  background: white;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-name {
  font-weight: 500;
  color: #374151;
}

.modern-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

.modern-content {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
}

.mobile-menu-btn {
  display: none;
}

.mobile-overlay {
  display: none;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .modern-sidebar {
    transform: translateX(-100%);
  }
  
  .modern-sidebar:not(.mobile-hidden) {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .mobile-menu-btn {
    display: block;
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1001;
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    cursor: pointer;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .mobile-overlay {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
  
  .modern-content {
    padding: 1rem;
  }
}
</style>
