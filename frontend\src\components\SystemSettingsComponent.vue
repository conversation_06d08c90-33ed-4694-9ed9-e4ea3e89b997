<template>
  <div class="settings-management">
    <!-- 设置标签页 -->
    <el-tabs v-model="activeSettingsTab" class="settings-tabs">
      <!-- CORS设置 -->
      <el-tab-pane label="CORS设置" name="cors">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-globe"></i>
              跨域资源共享(CORS)配置
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-security-config', 'security')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('save-security-config')" class="modern-btn primary" :disabled="configSaving">
                <i class="fas fa-save"></i>
                {{ configSaving ? '保存中...' : '保存配置' }}
              </button>
            </div>
          </div>
          <div class="card-content">
            <el-form :model="securityConfig" label-width="150px" class="security-form">
              <el-form-item label="开发模式">
                <el-switch
                  v-model="securityConfig.cors_dev_mode"
                  active-text="启用"
                  inactive-text="禁用"
                  active-color="#f56c6c"
                  inactive-color="#13ce66"
                />
                <div class="form-help" style="color: #f56c6c;">
                  <i class="fas fa-exclamation-triangle"></i>
                  开发模式将允许所有来源访问API，仅用于开发环境，生产环境请务必禁用！
                </div>
              </el-form-item>
              <el-form-item label="允许的源地址" v-show="!securityConfig.cors_dev_mode">
                <div style="display: flex; flex-direction: column; gap: 10px;">
                  <el-input
                    :value="securityConfig.cors_allowed_origins"
                    @input="val => emit('update:securityConfig', {...securityConfig, cors_allowed_origins: val})"
                    type="textarea"
                    :rows="3"
                    placeholder="多个地址用逗号分隔，例如：http://localhost:5173,http://***********:5173,https://yourdomain.com"
                  />
                  <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <el-button size="small" type="primary" @click="() => emit('add-current-origin')">
                      <i class="fas fa-plus"></i>
                      添加当前访问地址
                    </el-button>
                    <el-button size="small" type="success" @click="() => emit('add-common-origins')">
                      <i class="fas fa-magic"></i>
                      添加常用地址
                    </el-button>
                    <el-button size="small" type="warning" @click="() => emit('reset-to-defaults')">
                      <i class="fas fa-undo"></i>
                      重置为默认
                    </el-button>
                  </div>
                </div>
                <div class="form-help">
                  配置允许访问API的源地址，生产环境请勿使用 * 通配符
                </div>
              </el-form-item>
              <el-form-item label="允许的HTTP方法">
                <el-input
                  :value="securityConfig.cors_allowed_methods"
                  @input="val => emit('update:securityConfig', {...securityConfig, cors_allowed_methods: val})"
                  placeholder="例如：GET,POST,PUT,DELETE,OPTIONS"
                />
                <div class="form-help">
                  配置允许的HTTP请求方法，用逗号分隔
                </div>
              </el-form-item>
              <el-form-item label="允许的请求头">
                <el-input
                  :value="securityConfig.cors_allowed_headers"
                  @input="val => emit('update:securityConfig', {...securityConfig, cors_allowed_headers: val})"
                  placeholder="例如：Origin,Content-Type,Authorization"
                />
                <div class="form-help">
                  配置允许的请求头，用逗号分隔
                </div>
              </el-form-item>
              <el-form-item label="允许携带凭证">
                <el-switch
                  v-model="securityConfig.cors_allow_credentials"
                  active-text="允许"
                  inactive-text="禁止"
                />
                <div class="form-help">
                  是否允许请求携带Cookie等凭证信息
                </div>
              </el-form-item>
              <el-form-item label="预检缓存时间">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="securityConfig.cors_max_age"
                      :min="0"
                      :max="604800"
                      :step="3600"
                      style="width: 200px"
                    />
                    <span style="margin-left: 10px; color: #999;">秒</span>
                  </div>
                  <div class="config-help">
                    <span class="range">建议值: 86400 (24小时)</span>
                    <div class="desc">预检请求的缓存时间</div>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 频率限制设置 -->
      <el-tab-pane label="频率限制" name="rate-limit">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-tachometer-alt"></i>
              API频率限制配置
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-security-config', 'rate_limit')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('save-security-config')" class="modern-btn primary" :disabled="configSaving">
                <i class="fas fa-save"></i>
                {{ configSaving ? '保存中...' : '保存配置' }}
              </button>
            </div>
          </div>
          <div class="card-content">
            <el-form :model="rateLimitConfig" label-width="150px" class="security-form">
              <el-form-item label="启用频率限制">
                <el-switch
                  v-model="rateLimitConfig.enable_rate_limit"
                  active-text="启用"
                  inactive-text="禁用"
                />
                <div class="form-help">
                  启用后将对API请求进行频率限制，防止滥用
                </div>
              </el-form-item>
              <el-form-item label="每分钟请求数">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="rateLimitConfig.requests_per_minute"
                      :min="10"
                      :max="1000"
                      :step="10"
                      style="width: 200px"
                      :disabled="!rateLimitConfig.enable_rate_limit"
                    />
                    <span style="margin-left: 10px; color: #999;">次/分钟</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 10-1000</span>
                    <div class="desc">每个IP每分钟允许的最大请求数</div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="突发请求缓冲">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="rateLimitConfig.burst_size"
                      :min="5"
                      :max="200"
                      :step="5"
                      style="width: 200px"
                      :disabled="!rateLimitConfig.enable_rate_limit"
                    />
                    <span style="margin-left: 10px; color: #999;">次</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 5-200</span>
                    <div class="desc">允许的瞬间突发请求数量</div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="清理间隔">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="rateLimitConfig.cleanup_interval"
                      :min="60"
                      :max="3600"
                      :step="60"
                      style="width: 200px"
                      :disabled="!rateLimitConfig.enable_rate_limit"
                    />
                    <span style="margin-left: 10px; color: #999;">秒</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 60-3600</span>
                    <div class="desc">清理过期访问记录的时间间隔</div>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 数据库配置 -->
      <el-tab-pane label="数据库配置" name="database">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-database"></i>
              数据库连接池配置
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-security-config', 'database')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('save-security-config')" class="modern-btn primary" :disabled="configSaving">
                <i class="fas fa-save"></i>
                {{ configSaving ? '保存中...' : '保存配置' }}
              </button>
              <button @click="() => emit('reload-config')" class="modern-btn warning" :disabled="configSaving">
                <i class="fas fa-redo"></i>
                重载配置
              </button>
            </div>
          </div>
          <div class="card-content">
            <el-form :model="databaseConfig" label-width="150px" class="security-form">
              <el-form-item label="最大连接数">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="databaseConfig.max_open_conns"
                      :min="5"
                      :max="200"
                      :step="5"
                      style="width: 200px"
                    />
                    <span style="margin-left: 10px; color: #999;">个</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 5-200</span>
                    <div class="desc">数据库连接池的最大连接数</div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="最大空闲连接">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="databaseConfig.max_idle_conns"
                      :min="1"
                      :max="50"
                      :step="1"
                      style="width: 200px"
                    />
                    <span style="margin-left: 10px; color: #999;">个</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 1-50</span>
                    <div class="desc">连接池中保持的最大空闲连接数</div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="连接最大生存时间">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="databaseConfig.conn_max_lifetime"
                      :min="60"
                      :max="7200"
                      :step="60"
                      style="width: 200px"
                    />
                    <span style="margin-left: 10px; color: #999;">秒</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 60-7200</span>
                    <div class="desc">连接的最大生存时间</div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="连接最大空闲时间">
                <div class="input-container">
                  <div>
                    <el-input-number
                      v-model="databaseConfig.conn_max_idle_time"
                      :min="30"
                      :max="1800"
                      :step="30"
                      style="width: 200px"
                    />
                    <span style="margin-left: 10px; color: #999;">秒</span>
                  </div>
                  <div class="config-help">
                    <span class="range">安全范围: 30-1800</span>
                    <div class="desc">连接在池中的最大空闲时间</div>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 输入验证配置 -->
      <el-tab-pane label="输入验证" name="validation">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-shield-alt"></i>
              输入验证和安全配置
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-security-config', 'validation')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('save-security-config')" class="modern-btn primary" :disabled="configSaving">
                <i class="fas fa-save"></i>
                {{ configSaving ? '保存中...' : '保存配置' }}
              </button>
            </div>
          </div>
          <div class="card-content">
            <el-form :model="validationConfig" label-width="150px" class="security-form">
              <el-form-item label="用户名最大长度">
                <el-input-number
                  :value="validationConfig.max_username_length"
                  @update:value="val => emit('update:validationConfig', {...validationConfig, max_username_length: val})"
                  :min="1"
                  :max="100"
                  :step="1"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
              </el-form-item>
              <el-form-item label="密码最大长度">
                <el-input-number
                  :value="validationConfig.max_password_length"
                  @update:value="val => emit('update:validationConfig', {...validationConfig, max_password_length: val})"
                  :min="8"
                  :max="256"
                  :step="8"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
              </el-form-item>
              <el-form-item label="手机号最大长度">
                <el-input-number
                  :value="validationConfig.max_phone_length"
                  @update:value="val => emit('update:validationConfig', {...validationConfig, max_phone_length: val})"
                  :min="10"
                  :max="30"
                  :step="1"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
              </el-form-item>
              <el-form-item label="备注最大长度">
                <el-input-number
                  :value="validationConfig.max_remark_length"
                  @update:value="val => emit('update:validationConfig', {...validationConfig, max_remark_length: val})"
                  :min="50"
                  :max="500"
                  :step="50"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">字符</span>
              </el-form-item>
              <el-form-item label="XSS防护">
                <el-switch
                  v-model="validationConfig.enable_xss_protection"
                  active-text="启用"
                  inactive-text="禁用"
                />
                <div class="form-help">
                  启用XSS(跨站脚本)攻击防护，检测和过滤恶意脚本
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 开发调试设置 -->
      <el-tab-pane label="开发调试" name="debug">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-bug"></i>
              开发调试设置
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-debug-config')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('save-debug-config')" class="modern-btn primary" :disabled="configSaving">
                <i class="fas fa-save"></i>
                {{ configSaving ? '保存中...' : '保存配置' }}
              </button>
            </div>
          </div>
          <div class="card-content">
            <el-form :model="debugConfig" label-width="150px" class="security-form">
              <el-form-item label="调试模式">
                <el-switch
                  v-model="debugConfig.debug_mode"
                  active-text="启用"
                  inactive-text="禁用"
                />
                <div class="form-help">启用后将禁用前端缓存，便于开发调试。生产环境请关闭。</div>
              </el-form-item>

              <el-form-item label="前端调试日志">
                <el-switch
                  v-model="debugConfig.frontend_debug_logs"
                  active-text="启用"
                  inactive-text="禁用"
                />
                <div class="form-help">启用前端控制台调试日志输出</div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- 验证码设置 -->
      <el-tab-pane label="验证码设置" name="captcha">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-puzzle-piece"></i>
              验证码配置和调试设置
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-captcha-config')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('save-captcha-config')" class="modern-btn primary" :disabled="configSaving">
                <i class="fas fa-save"></i>
                {{ configSaving ? '保存中...' : '保存配置' }}
              </button>
            </div>
          </div>
          <div class="card-content">
            <el-form :model="captchaConfig" label-width="150px" class="security-form">
              <el-form-item label="图片宽度">
                <el-input-number
                  :value="captchaConfig.img_width"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, img_width: val})"
                  :min="200"
                  :max="600"
                  :step="50"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                <div class="form-help">
                  验证码图片的宽度，建议300-400像素
                </div>
              </el-form-item>
              <el-form-item label="图片高度">
                <el-input-number
                  :value="captchaConfig.img_height"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, img_height: val})"
                  :min="100"
                  :max="300"
                  :step="25"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                <div class="form-help">
                  验证码图片的高度，建议150-200像素
                </div>
              </el-form-item>
              <el-form-item label="红圈半径">
                <el-input-number
                  :value="captchaConfig.point_radius"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, point_radius: val})"
                  :min="10"
                  :max="50"
                  :step="5"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                <div class="form-help">
                  验证码红色圆圈的半径大小
                </div>
              </el-form-item>
              <el-form-item label="容错半径">
                <el-input-number
                  :value="captchaConfig.tolerance_radius"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, tolerance_radius: val})"
                  :min="10"
                  :max="50"
                  :step="5"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                <div class="form-help">
                  点击验证的容错范围，数值越小验证越严格
                </div>
              </el-form-item>
              <el-form-item label="最小距离">
                <el-input-number
                  :value="captchaConfig.min_distance"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, min_distance: val})"
                  :min="50"
                  :max="150"
                  :step="10"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">像素</span>
                <div class="form-help">
                  验证码圆圈之间的最小距离
                </div>
              </el-form-item>
              <el-form-item label="验证码点数">
                <el-input-number
                  :value="captchaConfig.num_points"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, num_points: val})"
                  :min="2"
                  :max="6"
                  :step="1"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">个</span>
                <div class="form-help">
                  需要点击的红色圆圈数量
                </div>
              </el-form-item>
              <el-form-item label="过期时间">
                <el-input-number
                  :value="captchaConfig.expire_minutes"
                  @update:value="val => emit('update:captchaConfig', {...captchaConfig, expire_minutes: val})"
                  :min="1"
                  :max="10"
                  :step="1"
                  style="width: 200px"
                />
                <span style="margin-left: 10px; color: var(--text-secondary);">分钟</span>
                <div class="form-help">
                  验证码的有效期，超时需要重新获取
                </div>
              </el-form-item>
              <el-form-item label="调试输出">
                <el-switch
                  v-model="captchaConfig.debug_output"
                  active-text="启用"
                  inactive-text="禁用"
                  active-color="#f56c6c"
                  inactive-color="#13ce66"
                />
                <div class="form-help" :style="{ color: captchaConfig.debug_output ? '#f56c6c' : 'var(--text-secondary)' }">
                  <i class="fas fa-exclamation-triangle" v-if="captchaConfig.debug_output"></i>
                  <i class="fas fa-info-circle" v-else></i>
                  {{ captchaConfig.debug_output ? '启用后会在服务器控制台输出验证码坐标等调试信息，生产环境建议禁用以防信息泄露' : '禁用验证码调试信息输出，提高安全性' }}
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-tab-pane>

      <!-- RSA证书管理 -->
      <el-tab-pane label="RSA证书管理" name="certificates">
        <div class="modern-card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-key"></i>
              RSA证书管理
            </h3>
            <div class="card-actions">
              <button @click="() => emit('load-rsa-certificate-info')" class="modern-btn secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button @click="() => emit('update:show-rsa-cert-dialog', true)" class="modern-btn primary">
                <i class="fas fa-plus"></i>
                生成新证书
              </button>
            </div>
          </div>
          <div class="card-content">
            <!-- RSA密钥对状态 -->
            <div class="rsa-status-section" v-loading="rsaCertLoading">
              <el-descriptions :column="2" border style="margin-bottom: 20px;">
                <el-descriptions-item label="RSA密钥对状态">
                  <el-tag :type="rsaCertInfo.key_pair_initialized ? 'success' : 'danger'">
                    {{ rsaCertInfo.key_pair_initialized ? '已初始化' : '未初始化' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="当前密钥长度">
                  <el-tag type="primary">{{ rsaCertInfo.key_info?.key_size || 2048 }}位</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="私钥文件">
                  <span :style="{ color: rsaCertInfo.key_info?.private_key_exists ? '#67c23a' : '#f56c6c' }">
                    <i :class="rsaCertInfo.key_info?.private_key_exists ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                    {{ rsaCertInfo.key_info?.private_key_exists ? '存在' : '不存在' }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="公钥文件">
                  <span :style="{ color: rsaCertInfo.key_info?.public_key_exists ? '#67c23a' : '#f56c6c' }">
                    <i :class="rsaCertInfo.key_info?.public_key_exists ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                    {{ rsaCertInfo.key_info?.public_key_exists ? '存在' : '不存在' }}
                  </span>
                </el-descriptions-item>
                <el-descriptions-item label="生成的证书数量">
                  <el-tag type="info">{{ rsaCertInfo.total_rsa_certs || 0 }} 个</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="密钥来源">
                  <el-tag type="success">文件存储</el-tag>
                </el-descriptions-item>
              </el-descriptions>

              <!-- 说明信息 -->
              <el-alert
                title="RSA证书说明"
                type="info"
                :closable="false"
                style="margin-top: 15px;"
              >
                <template #default>
                  <p><strong>当前使用：</strong>{{ getCurrentCertificateDescription() }}</p>
                  <p><strong>默认证书：</strong>system_default.crt 是系统默认证书（永久有效），用于调试和开发。</p>
                </template>
              </el-alert>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  securityConfig: {
    type: Object,
    required: true
  },
  rateLimitConfig: {
    type: Object,
    required: true
  },
  databaseConfig: {
    type: Object,
    required: true
  },
  validationConfig: {
    type: Object,
    required: true
  },
  debugConfig: {
    type: Object,
    required: true
  },
  captchaConfig: {
    type: Object,
    required: true
  },
  rsaCertInfo: {
    type: Object,
    required: true
  },
  rsaCertLoading: {
    type: Boolean,
    required: true
  },
  configSaving: {
    type: Boolean,
    required: true
  },
  showRSACertDialog: {
    type: Boolean,
    required: true
  }
})

const emit = defineEmits([
  'load-security-config',
  'save-security-config',
  'add-current-origin',
  'add-common-origins',
  'reset-to-defaults',
  'reload-config',
  'load-debug-config',
  'save-debug-config',
  'load-captcha-config',
  'save-captcha-config',
  'load-rsa-certificate-info',
  'update:show-rsa-cert-dialog'
])

const activeSettingsTab = ref('cors')

const getCurrentCertificateDescription = () => {
  if (!props.rsaCertInfo.key_pair_initialized) {
    return '未初始化RSA密钥对'
  }
  
  if (props.rsaCertInfo.key_info?.private_key_exists && props.rsaCertInfo.key_info?.public_key_exists) {
    return `已加载 ${props.rsaCertInfo.key_info.key_size || 2048} 位RSA密钥对`
  }
  
  return 'RSA密钥对状态异常'
}
</script>

<style scoped src="../styles/SystemSettingsComponent.css"></style>