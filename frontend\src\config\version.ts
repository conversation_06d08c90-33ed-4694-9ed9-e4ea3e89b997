// 版本配置文件
// 统一管理应用版本号

export const APP_VERSION = '2.1.0'
export const APP_NAME = '终端管理系统'
export const APP_NAME_EN = 'IPC Management Platform'

// 版本历史记录
export const VERSION_HISTORY = [
  {
    version: '2.1.0',
    date: '2025-08-05',
    description: '优化界面布局，统一版本管理，增强验证码功能'
  },
  {
    version: '2.0.0',
    date: '2025-08-04',
    description: '现代化界面重构，新增MQTT管理功能'
  },
  {
    version: '1.0.0',
    date: '2025-08-01',
    description: '初始版本发布'
  }
]

// 获取版本信息
export const getVersionInfo = () => {
  return {
    version: APP_VERSION,
    name: APP_NAME,
    nameEn: APP_NAME_EN,
    buildTime: new Date().toISOString(),
    environment: import.meta.env.MODE
  }
}

// 获取显示用的版本号（带v前缀）
export const getDisplayVersion = () => {
  return `v${APP_VERSION}`
}
