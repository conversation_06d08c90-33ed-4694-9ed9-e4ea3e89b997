<template>
  <div class="login-page">
    <!-- 页面标题 -->
    <div class="login-page-header">
      <h1 class="login-page-title">
        <i class="fas fa-desktop"></i>
        终端管理系统
      </h1>
      <p class="login-page-subtitle">IPC Management Platform</p>
    </div>
    
    <!-- 登录组件 -->
    <LoginComponent 
      :login-loading="loginLoading"
      @login="handleLogin"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import LoginComponent from '../components/LoginComponent.vue'

const loginLoading = ref(false)

const emit = defineEmits(['login'])

const handleLogin = (formData) => {
  loginLoading.value = false
  emit('login', formData)
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-page-header {
  text-align: center;
  margin-bottom: 40px;
  color: white;
}

.login-page-title {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.login-page-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
  letter-spacing: 1px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .login-page-title {
    font-size: 2.2rem;
  }
  
  .login-page-subtitle {
    font-size: 1rem;
  }
  
  .login-page-header {
    margin-bottom: 30px;
  }
}
</style>
