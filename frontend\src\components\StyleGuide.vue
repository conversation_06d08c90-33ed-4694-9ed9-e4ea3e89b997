<template>
  <div class="style-guide">
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-brush"></i>
        IPC管理系统样式指南
      </h1>
      <p class="page-subtitle">统一的设计系统和组件库</p>
    </div>

    <div class="content-area">
      <!-- 颜色系统 -->
      <div class="content-card">
        <div class="card-header">
          <span><i class="el-icon-picture"></i>颜色系统</span>
        </div>
        <div class="card-content">
          <div class="color-section">
            <h4>主色调</h4>
            <div class="color-grid">
              <div class="color-item">
                <div class="color-swatch primary"></div>
                <span>Primary</span>
                <code>var(--color-primary)</code>
              </div>
              <div class="color-item">
                <div class="color-swatch success"></div>
                <span>Success</span>
                <code>var(--color-success)</code>
              </div>
              <div class="color-item">
                <div class="color-swatch warning"></div>
                <span>Warning</span>
                <code>var(--color-warning)</code>
              </div>
              <div class="color-item">
                <div class="color-swatch danger"></div>
                <span>Danger</span>
                <code>var(--color-danger)</code>
              </div>
            </div>
          </div>

          <div class="color-section">
            <h4>文字颜色</h4>
            <div class="text-samples">
              <p class="text-primary">主要文字 (text-primary)</p>
              <p class="text-regular">常规文字 (text-regular)</p>
              <p class="text-secondary">次要文字 (text-secondary)</p>
              <p class="text-muted">占位文字 (text-muted)</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 间距系统 -->
      <div class="content-card">
        <div class="card-header">
          <span><i class="el-icon-rank"></i>间距系统</span>
        </div>
        <div class="card-content">
          <div class="spacing-demo">
            <div class="spacing-item" v-for="size in spacingSizes" :key="size.name">
              <div class="spacing-label">{{ size.name }}</div>
              <div class="spacing-visual" :style="{ width: size.value }"></div>
              <code>{{ size.token }}</code>
            </div>
          </div>
        </div>
      </div>

      <!-- 按钮组件 -->
      <div class="content-card">
        <div class="card-header">
          <span><i class="el-icon-mouse"></i>按钮组件</span>
        </div>
        <div class="card-content">
          <div class="button-demo">
            <div class="button-group">
              <h4>基础按钮</h4>
              <button class="btn-base">基础按钮</button>
              <button class="refresh-btn">
                <i class="el-icon-refresh"></i>
                刷新按钮
              </button>
              <button class="add-btn">
                <i class="el-icon-plus"></i>
                添加按钮
              </button>
              <button class="clear-btn">
                <i class="el-icon-delete"></i>
                清理按钮
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格组件 -->
      <div class="content-card">
        <div class="card-header">
          <span><i class="el-icon-s-grid"></i>表格组件</span>
        </div>
        <div class="card-content">
          <table class="standard-table">
            <thead>
              <tr>
                <th>ID</th>
                <th>用户名</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>001</td>
                <td>admin</td>
                <td><span class="text-success">在线</span></td>
                <td>2025-07-10 10:30:00</td>
                <td>
                  <button class="btn-base">编辑</button>
                </td>
              </tr>
              <tr>
                <td>002</td>
                <td>user1</td>
                <td><span class="text-danger">离线</span></td>
                <td>2025-07-10 09:15:00</td>
                <td>
                  <button class="btn-base">编辑</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 状态指示 -->
      <div class="content-card">
        <div class="card-header">
          <span><i class="el-icon-info"></i>状态指示</span>
        </div>
        <div class="card-content">
          <div class="status-demo">
            <div class="status-group">
              <h4>文字状态</h4>
              <p class="text-success">成功状态</p>
              <p class="text-warning">警告状态</p>
              <p class="text-danger">危险状态</p>
              <p class="text-info">信息状态</p>
              <p class="text-primary">主要状态</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 表单组件 -->
      <div class="content-card">
        <div class="card-header">
          <span><i class="el-icon-edit-outline"></i>表单组件</span>
        </div>
        <div class="card-content">
          <div class="form-demo">
            <div class="filter-row">
              <div class="filter-item">
                <label>用户名:</label>
                <input type="text" class="filter-input" placeholder="请输入用户名">
              </div>
              <div class="filter-item">
                <label>状态:</label>
                <select class="filter-select">
                  <option>全部</option>
                  <option>在线</option>
                  <option>离线</option>
                </select>
              </div>
              <button class="filter-btn">
                <i class="el-icon-search"></i>
                搜索
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StyleGuide',
  data() {
    return {
      spacingSizes: [
        { name: 'XS', value: '4px', token: 'var(--spacing-xs)' },
        { name: 'SM', value: '8px', token: 'var(--spacing-sm)' },
        { name: 'MD', value: '12px', token: 'var(--spacing-md)' },
        { name: 'LG', value: '16px', token: 'var(--spacing-lg)' },
        { name: 'XL', value: '20px', token: 'var(--spacing-xl)' },
        { name: '2XL', value: '24px', token: 'var(--spacing-2xl)' },
        { name: '3XL', value: '32px', token: 'var(--spacing-3xl)' }
      ]
    }
  }
}
</script>

<style scoped>
.style-guide {
  min-height: 100vh;
  background-color: var(--color-bg-secondary);
}

/* 颜色展示 */
.color-section {
  margin-bottom: var(--spacing-3xl);
}

.color-section h4 {
  margin-bottom: var(--spacing-lg);
  color: var(--color-text-primary);
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}

.color-swatch {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--color-border-base);
}

.color-swatch.primary { background-color: var(--color-primary); }
.color-swatch.success { background-color: var(--color-success); }
.color-swatch.warning { background-color: var(--color-warning); }
.color-swatch.danger { background-color: var(--color-danger); }

.text-samples p {
  margin: var(--spacing-sm) 0;
  font-size: var(--font-size-base);
}

.text-primary { color: var(--color-text-primary); }
.text-regular { color: var(--color-text-regular); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-placeholder); }

/* 间距展示 */
.spacing-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.spacing-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.spacing-label {
  width: 40px;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.spacing-visual {
  height: 20px;
  background-color: var(--color-primary);
  border-radius: var(--border-radius-sm);
}

/* 按钮展示 */
.button-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.button-group h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--color-text-primary);
}

.button-group button {
  align-self: flex-start;
}

/* 状态展示 */
.status-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.status-group h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--color-text-primary);
}

.status-group p {
  margin: var(--spacing-sm) 0;
  font-weight: var(--font-weight-medium);
}

/* 表单展示 */
.form-demo {
  padding: var(--spacing-lg);
  background-color: var(--color-bg-tertiary);
  border-radius: var(--border-radius-base);
}

@media (max-width: 768px) {
  .color-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .spacing-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .button-group button {
    align-self: stretch;
  }
}
</style>
