<template>
  <div class="logs-management">
    <!-- 操作工具栏 -->
    <div class="modern-card" style="margin-bottom: 1rem;">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-clipboard-list"></i>
          日志管理
        </h3>
        <div style="display: flex; gap: 1rem;">
          <button @click="() => emit('refresh-logs')" class="modern-btn secondary">
            <i class="fas fa-sync-alt"></i>
            刷新
          </button>
          <button @click="() => emit('clear-logs')" class="modern-btn danger">
            <i class="fas fa-trash"></i>
            清理日志
          </button>
        </div>
      </div>
    </div>
    
    <!-- 日志统计 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">今日操作</span>
          <div class="stat-icon primary">
            <i class="fas fa-calendar-day"></i>
          </div>
        </div>
        <div class="stat-value">{{ logStats.today_count || 0 }}</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">成功率</span>
          <div class="stat-icon success">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>
        <div class="stat-value">{{ (logStats.success_rate || 0).toFixed(1) }}%</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">快捷清理</span>
          <div class="stat-icon warning">
            <i class="fas fa-broom"></i>
          </div>
        </div>
        <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
          <button @click="() => emit('quick-clear-logs', 1)" class="modern-btn danger small" title="清理1天前的日志（几乎全部）">
            1天前
          </button>
          <button @click="() => emit('quick-clear-logs', 7)" class="modern-btn secondary small" title="清理7天前的日志">
            7天前
          </button>
          <button @click="() => emit('quick-clear-logs', 30)" class="modern-btn secondary small" title="清理30天前的日志">
            30天前
          </button>
        </div>
      </div>
    </div>
    
    <!-- 过滤器 -->
    <div class="logs-filters">
      <div class="filter-row">
        <div class="filter-item">
          <label>操作类型:</label>
          <select :value="logFilters.operation_type" @change="e => emit('update:logFilters', {...logFilters, operation_type: e.target.value})" class="filter-select">
            <option value="">全部</option>
            <option value="LOGIN">登录</option>
            <option value="LOGIN_FAILED">登录失败</option>
            <option value="CREATE_USER">创建用户</option>
            <option value="UPDATE_USER">更新用户</option>
            <option value="DELETE_USER">删除用户</option>
          </select>
        </div>
        <div class="filter-item">
          <label>模块:</label>
          <select :value="logFilters.module" @change="e => emit('update:logFilters', {...logFilters, module: e.target.value})" class="filter-select">
            <option value="">全部</option>
            <option value="AUTH">认证</option>
            <option value="USER_MANAGEMENT">用户管理</option>
            <option value="USER_MANAGEMENT">用户管理</option>
            <option value="MQTT">MQTT服务器</option>
            <option value="SYSTEM">系统</option>
          </select>
        </div>
        <div class="filter-item">
          <label>搜索:</label>
          <input 
            :value="logFilters.keyword" 
            @input="e => emit('update:logFilters', {...logFilters, keyword: e.target.value})"
            placeholder="搜索操作详情..." 
            class="filter-input"
          />
        </div>
        <div class="filter-item">
          <button @click="() => emit('filter-logs')" class="filter-btn">
            <i class="el-icon-search"></i>
            搜索
          </button>
        </div>
      </div>
    </div>
    
    <!-- 日志表格 -->
    <div class="logs-table">
      <div class="table-header">
        <div class="table-cell">时间</div>
        <div class="table-cell">用户</div>
        <div class="table-cell">操作类型</div>
        <div class="table-cell">模块</div>
        <div class="table-cell">操作详情</div>
        <div class="table-cell">结果</div>
        <div class="table-cell">IP地址</div>
      </div>
      <div class="table-body">
        <div v-if="logs.length === 0" class="no-logs">
          <i class="el-icon-document"></i>
          <p>暂无操作日志</p>
        </div>
        <div v-else>
          <div v-for="log in logs" :key="log.id" class="table-row">
            <div class="table-cell">{{ formatTime(log.created_at) }}</div>
            <div class="table-cell">{{ log.username }}</div>
            <div class="table-cell">
              <span :class="getOperationTypeClass(log.operation_type)">
                {{ formatOperationType(log.operation_type) }}
              </span>
            </div>
            <div class="table-cell">{{ log.module }}</div>
            <div class="table-cell">{{ log.operation_detail }}</div>
            <div class="table-cell">
              <span :class="log.success ? 'success' : 'failed'">
                {{ log.success ? '成功' : '失败' }}
              </span>
            </div>
            <div class="table-cell">{{ formatIpAddress(log.ip_address) }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页组件 -->
    <div class="logs-pagination">
      <el-pagination
        :current-page="logPagination.page"
        :page-size="logPagination.page_size"
        :page-sizes="[10, 20, 50, 100]"
        :total="logPagination.total_count"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handlePageSizeChange"
        @current-change="handlePageChange"
        :small="false"
        background
        :hide-on-single-page="false"
      />
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  logs: {
    type: Array,
    required: true
  },
  logStats: {
    type: Object,
    required: true
  },
  logFilters: {
    type: Object,
    required: true
  },
  logPagination: {
    type: Object,
    required: true
  }
})

const emit = defineEmits([
  'refresh-logs',
  'clear-logs',
  'quick-clear-logs',
  'update:logFilters',
  'filter-logs',
  'update:logPagination'
])

const handlePageSizeChange = (pageSize) => {
  emit('update:logPagination', {
    ...props.logPagination,
    page_size: pageSize,
    page: 1
  })
}

const handlePageChange = (page) => {
  emit('update:logPagination', {
    ...props.logPagination,
    page
  })
}

const formatTime = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const formatOperationType = (type) => {
  const types = {
    'LOGIN': '登录',
    'LOGIN_FAILED': '登录失败',
    'CREATE_USER': '创建用户',
    'UPDATE_USER': '更新用户',
    'DELETE_USER': '删除用户'
  }
  return types[type] || type
}

const getOperationTypeClass = (type) => {
  const classes = {
    'LOGIN': 'login',
    'LOGIN_FAILED': 'login-failed',
    'CREATE_USER': 'create-user',
    'UPDATE_USER': 'update-user',
    'DELETE_USER': 'delete-user'
  }
  return classes[type] || ''
}

const formatIpAddress = (ip) => {
  if (!ip) return '-'
  // 如果是IPv6地址，简化显示
  if (ip.includes(':') && ip.includes('::')) {
    return ip
  }
  if (ip.includes(':') && ip.split(':').length > 2) {
    const parts = ip.split(':')
    return `${parts[0]}:${parts[1]}:...:${parts[parts.length - 1]}`
  }
  return ip
}
</script>

<style scoped src="../styles/LogsManagementComponent.css"></style>