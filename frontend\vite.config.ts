import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// 获取后端服务器地址，支持环境变量配置
const getBackendUrl = () => {
  // 优先使用环境变量
  if (process.env.VITE_BACKEND_URL) {
    return process.env.VITE_BACKEND_URL
  }

  // 开发环境默认使用localhost，这样可以自动适配
  return 'http://localhost:8080'
}

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: getBackendUrl(),
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    rollupOptions: {
      output: {
        entryFileNames: `assets/[name]-[hash]-v${Date.now()}.js`,
        chunkFileNames: `assets/[name]-[hash]-v${Date.now()}.js`,
        assetFileNames: `assets/[name]-[hash]-v${Date.now()}.[ext]`
      }
    }
  },
  define: {
    __DEV_MODE__: JSON.stringify(process.env.NODE_ENV === 'development')
  }
})
