package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"math/big"
	"net"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// RSAKeyPair RSA密钥对
type RSAKeyPair struct {
	PrivateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
}

// RSACertificateInfo RSA证书信息
type RSACertificateInfo struct {
	Name            string    `json:"name"`
	PrivateKeyPath  string    `json:"private_key_path"`
	CertificatePath string    `json:"certificate_path"`
	Subject         string    `json:"subject"`
	Issuer          string    `json:"issuer"`
	NotBefore       time.Time `json:"not_before"`
	NotAfter        time.Time `json:"not_after"`
	IsExpired       bool      `json:"is_expired"`
	DaysToExpiry    int       `json:"days_to_expiry"`
	KeySize         int       `json:"key_size"`
	SerialNumber    string    `json:"serial_number"`
	IsCurrentlyUsed bool      `json:"is_currently_used"` // 是否为当前使用的证书
	IsDefaultCert   bool      `json:"is_default_cert"`   // 是否为默认系统证书
}

// AuthorizationData 授权数据结构
type AuthorizationData struct {
	UserID    int       `json:"user_id"`
	Username  string    `json:"username"`
	MaxUsage  int       `json:"max_usage"`
	ExpiresAt time.Time `json:"expires_at"`
	Timestamp int64     `json:"timestamp"`
	Signature string    `json:"signature"`
}

var globalKeyPair *RSAKeyPair

// 原始默认密钥对（用于切换回默认证书）
var originalDefaultKeyPair *RSAKeyPair

// InitRSA 初始化RSA密钥对 - 支持永久化存储
func InitRSA() error {
	// 尝试从文件加载现有的RSA密钥对
	if loadRSAKeysFromFile() {
		log.Println("RSA keys loaded from certificate directory")

		// 保存原始默认密钥对的备份
		originalDefaultKeyPair = &RSAKeyPair{
			PrivateKey: globalKeyPair.PrivateKey,
			PublicKey:  globalKeyPair.PublicKey,
		}
		log.Printf("Original default key pair backed up")

		// RSA密钥对加载完成，生成默认证书文件
		if err := generateDefaultCertificate(); err != nil {
			log.Printf("Warning: Failed to generate default certificate: %v", err)
		}

		// 初始化当前证书状态（如果状态文件不存在）
		statusFile := "./certificate/current_cert.txt"
		if _, err := os.Stat(statusFile); os.IsNotExist(err) {
			defaultCertPath := "./certificate/rsa_default.crt"
			if err := os.WriteFile(statusFile, []byte(defaultCertPath), 0644); err != nil {
				log.Printf("Warning: Failed to initialize current certificate status: %v", err)
			} else {
				log.Printf("Initialized current certificate status: %s", defaultCertPath)
			}
		}

		return nil
	}

	// 如果没有现有密钥，生成新的密钥对
	log.Println("No existing RSA keys found, generating new keys...")
	if err := generateAndSaveRSAKeys(); err != nil {
		return fmt.Errorf("failed to generate and save RSA keys: %v", err)
	}
	log.Println("New RSA keys generated and saved successfully")

	// 保存原始默认密钥对的备份
	originalDefaultKeyPair = &RSAKeyPair{
		PrivateKey: globalKeyPair.PrivateKey,
		PublicKey:  globalKeyPair.PublicKey,
	}
	log.Printf("Original default key pair backed up")

	// RSA密钥对生成完成，生成默认证书文件
	if err := generateDefaultCertificate(); err != nil {
		log.Printf("Warning: Failed to generate default certificate: %v", err)
	}

	return nil
}

// GenerateRSAKeyPair 生成RSA密钥对
func GenerateRSAKeyPair(bits int) (*RSAKeyPair, error) {
	privateKey, err := rsa.GenerateKey(rand.Reader, bits)
	if err != nil {
		return nil, err
	}

	return &RSAKeyPair{
		PrivateKey: privateKey,
		PublicKey:  &privateKey.PublicKey,
	}, nil
}

// EncryptWithPublicKey 使用公钥加密（使用OAEP填充）
func EncryptWithPublicKey(data []byte, publicKey *rsa.PublicKey) ([]byte, error) {
	// 使用OAEP填充，更安全
	return rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, data, nil)
}

// DecryptWithPrivateKey 使用私钥解密（使用OAEP填充）
func DecryptWithPrivateKey(encryptedData []byte, privateKey *rsa.PrivateKey) ([]byte, error) {
	// 使用OAEP填充，更安全
	return rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, encryptedData, nil)
}

// EncryptWithRSACertificate 使用指定的RSA证书加密数据
func EncryptWithRSACertificate(data []byte, certPath string) ([]byte, error) {
	// 读取证书文件
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read certificate file: %v", err)
	}

	// 解析证书
	block, _ := pem.Decode(certData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block from certificate")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse certificate: %v", err)
	}

	// 获取公钥
	publicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("certificate does not contain an RSA public key")
	}

	// 使用OAEP填充模式加密
	encryptedData, err := rsa.EncryptOAEP(
		sha256.New(),
		rand.Reader,
		publicKey,
		data,
		nil,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt data with certificate: %v", err)
	}

	return encryptedData, nil
}

// GetRSACertificatePublicKey 从RSA证书文件获取公钥
func GetRSACertificatePublicKey(certPath string) (*rsa.PublicKey, error) {
	// 读取证书文件
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read certificate file: %v", err)
	}

	// 解析证书
	block, _ := pem.Decode(certData)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block from certificate")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse certificate: %v", err)
	}

	// 获取公钥
	publicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("certificate does not contain an RSA public key")
	}

	return publicKey, nil
}

// GetCurrentRSAKeyPairInfo 获取当前使用的RSA密钥对信息
func GetCurrentRSAKeyPairInfo() map[string]interface{} {
	if globalKeyPair == nil {
		return map[string]interface{}{
			"initialized": false,
			"source":      "none",
		}
	}

	// 检查是否来自文件
	certDir := "./certificate"
	privateKeyPath := filepath.Join(certDir, "rsa_default.key")
	publicKeyPath := filepath.Join(certDir, "rsa_default.pub")

	privateKeyExists := false
	publicKeyExists := false

	if _, err := os.Stat(privateKeyPath); err == nil {
		privateKeyExists = true
	}
	if _, err := os.Stat(publicKeyPath); err == nil {
		publicKeyExists = true
	}

	return map[string]interface{}{
		"initialized":        true,
		"private_key_exists": privateKeyExists,
		"public_key_exists":  publicKeyExists,
		"private_key_path":   privateKeyPath,
		"public_key_path":    publicKeyPath,
		"key_size":           globalKeyPair.PublicKey.N.BitLen(),
		"source":             "file", // 当前总是从文件加载
	}
}

// SwitchToRSACertificate 切换系统使用的RSA证书
func SwitchToRSACertificate(certPath string) error {
	log.Printf("Switching to RSA certificate: %s", certPath)

	// 验证证书文件存在
	if _, err := os.Stat(certPath); os.IsNotExist(err) {
		return fmt.Errorf("certificate file not found: %s", certPath)
	}

	// 构造对应的私钥文件路径
	keyPath := strings.TrimSuffix(certPath, ".crt") + ".key"
	log.Printf("Looking for private key file: %s", keyPath)
	if _, err := os.Stat(keyPath); os.IsNotExist(err) {
		return fmt.Errorf("corresponding private key file not found: %s", keyPath)
	}

	// 读取私钥文件
	log.Printf("Reading private key file: %s", keyPath)
	keyData, err := os.ReadFile(keyPath)
	if err != nil {
		return fmt.Errorf("failed to read private key file: %v", err)
	}
	log.Printf("Private key file read successfully, size: %d bytes", len(keyData))

	// 解析私钥
	log.Printf("Decoding PEM block from private key")
	block, _ := pem.Decode(keyData)
	if block == nil {
		return fmt.Errorf("failed to decode PEM block from private key")
	}
	log.Printf("PEM block decoded successfully, type: %s", block.Type)

	log.Printf("Parsing PKCS1 private key")
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %v", err)
	}
	log.Printf("Private key parsed successfully")

	// 读取证书文件获取公钥
	log.Printf("Reading certificate file: %s", certPath)
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return fmt.Errorf("failed to read certificate file: %v", err)
	}
	log.Printf("Certificate file read successfully, size: %d bytes", len(certData))

	log.Printf("Decoding PEM block from certificate")
	certBlock, _ := pem.Decode(certData)
	if certBlock == nil {
		return fmt.Errorf("failed to decode PEM block from certificate")
	}
	log.Printf("Certificate PEM block decoded successfully, type: %s", certBlock.Type)

	log.Printf("Parsing X.509 certificate")
	cert, err := x509.ParseCertificate(certBlock.Bytes)
	if err != nil {
		return fmt.Errorf("failed to parse certificate: %v", err)
	}
	log.Printf("Certificate parsed successfully")

	log.Printf("Extracting RSA public key from certificate")
	publicKey, ok := cert.PublicKey.(*rsa.PublicKey)
	if !ok {
		return fmt.Errorf("certificate does not contain an RSA public key")
	}
	log.Printf("RSA public key extracted successfully")

	// 验证密钥对匹配
	log.Printf("Verifying key pair match...")
	log.Printf("Private key N (first 50 chars): %s", privateKey.PublicKey.N.String()[:50])
	log.Printf("Certificate public key N (first 50 chars): %s", publicKey.N.String()[:50])

	if privateKey.PublicKey.N.Cmp(publicKey.N) != 0 {
		return fmt.Errorf("private key and certificate do not match - key pair mismatch detected")
	}
	log.Printf("Key pair verification successful")

	// 检查是否切换到默认证书
	certDir := "./certificate"
	defaultCertPath := filepath.Join(certDir, "rsa_default.crt")

	// 转换为绝对路径进行比较
	absCertPath, err := filepath.Abs(certPath)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for %s: %v", certPath, err)
	}

	absDefaultPath, err := filepath.Abs(defaultCertPath)
	if err != nil {
		return fmt.Errorf("failed to get absolute path for %s: %v", defaultCertPath, err)
	}

	log.Printf("Comparing absolute paths: certPath=%s, defaultCertPath=%s", absCertPath, absDefaultPath)

	if absCertPath == absDefaultPath {
		// 切换到默认证书：恢复原始默认密钥对
		if originalDefaultKeyPair == nil {
			return fmt.Errorf("original default key pair not available")
		}

		log.Printf("Switching to default certificate - restoring original key pair")
		globalKeyPair = &RSAKeyPair{
			PrivateKey: originalDefaultKeyPair.PrivateKey,
			PublicKey:  originalDefaultKeyPair.PublicKey,
		}
	} else {
		// 切换到用户证书：使用证书中的密钥对
		log.Printf("Switching to user certificate - using certificate key pair")
		globalKeyPair = &RSAKeyPair{
			PrivateKey: privateKey,
			PublicKey:  publicKey,
		}
	}

	log.Printf("RSA certificate switched successfully: %s", certPath)
	return nil
}

// CreateAuthorizationToken 创建授权令牌
func CreateAuthorizationToken(userID int, username string, maxUsage int, expiresAt time.Time) (string, error) {
	if globalKeyPair == nil {
		return "", errors.New("RSA key pair not initialized")
	}

	authData := AuthorizationData{
		UserID:    userID,
		Username:  username,
		MaxUsage:  maxUsage,
		ExpiresAt: expiresAt,
		Timestamp: time.Now().Unix(),
	}

	// 序列化授权数据
	jsonData, err := json.Marshal(authData)
	if err != nil {
		return "", err
	}

	// 使用公钥加密数据（正确的加密方式）
	encryptedData, err := EncryptWithPublicKey(jsonData, globalKeyPair.PublicKey)
	if err != nil {
		return "", err
	}

	// 编码为base64
	return base64.StdEncoding.EncodeToString(encryptedData), nil
}

// ValidateAuthorizationToken 验证授权令牌
func ValidateAuthorizationToken(token string) (*AuthorizationData, error) {
	if globalKeyPair == nil {
		return nil, errors.New("RSA key pair not initialized")
	}

	// 解码base64
	encryptedData, err := base64.StdEncoding.DecodeString(token)
	if err != nil {
		return nil, err
	}

	// 使用私钥解密
	decryptedData, err := DecryptWithPrivateKey(encryptedData, globalKeyPair.PrivateKey)
	if err != nil {
		return nil, err
	}

	// 反序列化授权数据
	var authData AuthorizationData
	err = json.Unmarshal(decryptedData, &authData)
	if err != nil {
		return nil, err
	}

	// 验证时间戳（防止重放攻击）
	if time.Now().Unix()-authData.Timestamp > 3600 { // 1小时有效期
		return nil, errors.New("token timestamp expired")
	}

	return &authData, nil
}

// ExportPublicKeyPEM 导出公钥为PEM格式
func ExportPublicKeyPEM(publicKey *rsa.PublicKey) (string, error) {
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", err
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return string(publicKeyPEM), nil
}

// GetPublicKeyPEM 获取当前公钥的PEM格式
func GetPublicKeyPEM() (string, error) {
	if globalKeyPair == nil {
		return "", errors.New("RSA key pair not initialized")
	}
	return ExportPublicKeyPEM(globalKeyPair.PublicKey)
}

// AccountInfoMessage 账户信息消息结构
type AccountInfoMessage struct {
	ExpireTime    interface{} `json:"expire_time"`
	MaxUsageCount int         `json:"max_usage_count"`
	DeviceName    string      `json:"device_name,omitempty"` // 设备名称（可选）
	DeviceID      string      `json:"device_id,omitempty"`   // 设备ID（可选）
	Timestamp     int64       `json:"timestamp"`
}

// SendAccountInfoOnLogin 用户登录成功后发送授权信息 - 增强版本支持设备模式
func SendAccountInfoOnLogin(clientID, username, deviceID string) error {
	log.Printf("Starting to send account info to client %s (user: %s, device: %s)", clientID, username, deviceID)

	if GlobalMQTTServer == nil {
		log.Printf("MQTT server not initialized, skipping account info sending")
		return fmt.Errorf("MQTT server not initialized")
	}

	// 检查是否启用账户信息发送
	if !GetConfigBool("account_info", "enable_send", true) {
		log.Printf("Account info sending is disabled in configuration")
		return nil
	}

	var accountInfo map[string]interface{}
	var err error

	// 统一使用设备级别管理 - 所有MQTT连接都基于设备配置
	var targetDeviceID string
	if deviceID != "" {
		// 明确的设备登录模式
		targetDeviceID = deviceID
		log.Printf("Explicit device login mode - device ID: %s", deviceID)
	} else {
		// 用户登录模式，使用clientID作为设备ID
		targetDeviceID = clientID
		log.Printf("User login mode - treating clientID as device ID: %s", clientID)
	}

	// 统一使用设备配置
	accountInfo, err = buildDeviceAccountInfo(targetDeviceID)
	if err != nil {
		log.Printf("Failed to build device account info for device %s: %v", targetDeviceID, err)
		return err
	}

	log.Printf("Using device configuration for device %s", targetDeviceID)

	// 构建消息主题
	topicPrefix := GetConfigWithDefault("account_info", "topic_prefix", "account")
	topic := fmt.Sprintf("%s/%s", topicPrefix, clientID)

	// 根据设备或用户的配置决定是否加密
	var messageData []byte
	var enableEncryption bool

	// 统一从设备配置中读取加密设置
	deviceAuth, err := GetDeviceAuthorization(targetDeviceID)
	if err != nil {
		log.Printf("Failed to get device authorization for encryption check (device %s): %v", targetDeviceID, err)
		enableEncryption = true // 默认启用加密
	} else {
		enableEncryption = deviceAuth.AccountInfoEncryption
		log.Printf("Using device encryption setting for device %s: %v", targetDeviceID, enableEncryption)
	}

	log.Printf("Encryption setting for client %s (device: %s, user: %s): %v", clientID, deviceID, username, enableEncryption)

	if enableEncryption {
		// 添加timestamp到账户信息中
		accountInfo["timestamp"] = time.Now().Unix()

		// 使用RSA加密整个账户信息
		encryptedData, err := EncryptAccountInfo(accountInfo)
		if err != nil {
			log.Printf("Failed to encrypt account info: %v", err)
			return err
		}

		// 直接发送加密串，不包装在JSON中
		messageData = []byte(encryptedData)
		log.Printf("Sending encrypted account info as raw string to client %s", clientID)
	} else {
		// 明文发送 - 直接发送账户信息，添加timestamp
		accountInfo["timestamp"] = time.Now().Unix()

		messageData, err = json.Marshal(accountInfo)
		if err != nil {
			log.Printf("Failed to marshal plain message: %v", err)
			return err
		}
		log.Printf("Sending plain account info as JSON to client %s", clientID)
	}

	// 发送消息给指定客户端
	err = GlobalMQTTServer.SendMessageToClient(clientID, topic, string(messageData))
	if err != nil {
		log.Printf("Failed to send account info to client %s: %v", clientID, err)
		return err
	}

	log.Printf("Successfully sent account info to client %s on topic %s", clientID, topic)
	return nil
}

// buildDeviceAccountInfo 构建设备账户信息
func buildDeviceAccountInfo(deviceID string) (map[string]interface{}, error) {
	// 获取设备授权信息
	deviceAuth, err := GetDeviceAuthorization(deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device authorization: %v", err)
	}

	// 构建标准格式的设备账户信息
	accountInfo := make(map[string]interface{})

	accountInfo["maxRun"] = deviceAuth.MaxUsage
	if deviceAuth.ExpireTime != nil {
		// 确保使用本地时区格式化时间
		localTime := deviceAuth.ExpireTime.Local()
		accountInfo["expire"] = localTime.Format("2006-01-02 15:04:05")
		log.Printf("Device %s expire time - UTC: %s, Local: %s", deviceID,
			deviceAuth.ExpireTime.Format("2006-01-02 15:04:05"),
			localTime.Format("2006-01-02 15:04:05"))
	} else {
		// 过期时间为空表示无限制
		accountInfo["expire"] = "unlimited"
		log.Printf("Device %s has unlimited expire time", deviceID)
	}

	log.Printf("Built device account info for device %s: %+v", deviceID, accountInfo)
	return accountInfo, nil
}

// buildUserAccountInfo 已废弃 - 统一使用设备级别管理
// 保留此函数声明以避免编译错误，但不再使用
func buildUserAccountInfo(username string) (map[string]interface{}, error) {
	return nil, fmt.Errorf("buildUserAccountInfo is deprecated - use device-level management only")
}

// RSA密钥永久化存储相关函数

// loadRSAKeysFromFile 从文件加载RSA密钥对
func loadRSAKeysFromFile() bool {
	certDir := "./certificate"
	privateKeyPath := filepath.Join(certDir, "rsa_default.key")

	// 检查私钥文件是否存在
	if _, err := os.Stat(privateKeyPath); os.IsNotExist(err) {
		return false
	}

	// 读取私钥文件
	privateKeyData, err := ioutil.ReadFile(privateKeyPath)
	if err != nil {
		log.Printf("Failed to read RSA private key file: %v", err)
		return false
	}

	// 解析私钥
	block, _ := pem.Decode(privateKeyData)
	if block == nil {
		log.Printf("Failed to decode PEM block from private key")
		return false
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		log.Printf("Failed to parse RSA private key: %v", err)
		return false
	}

	// 创建密钥对
	globalKeyPair = &RSAKeyPair{
		PrivateKey: privateKey,
		PublicKey:  &privateKey.PublicKey,
	}

	// 保存原始默认密钥对的副本
	originalDefaultKeyPair = &RSAKeyPair{
		PrivateKey: privateKey,
		PublicKey:  &privateKey.PublicKey,
	}

	return true
}

// generateAndSaveRSAKeys 生成并保存RSA密钥对
func generateAndSaveRSAKeys() error {
	// 生成新的密钥对
	keyPair, err := GenerateRSAKeyPair(2048)
	if err != nil {
		return err
	}

	// 保存到文件
	if err := saveRSAKeysToFile(keyPair); err != nil {
		return err
	}

	globalKeyPair = keyPair
	return nil
}

// saveRSAKeysToFile 保存RSA密钥对到文件
func saveRSAKeysToFile(keyPair *RSAKeyPair) error {
	certDir := "./certificate"

	// 确保证书目录存在
	if err := os.MkdirAll(certDir, 0755); err != nil {
		return fmt.Errorf("failed to create certificate directory: %v", err)
	}

	// 保存私钥
	privateKeyPath := filepath.Join(certDir, "rsa_default.key")
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(keyPair.PrivateKey)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	if err := ioutil.WriteFile(privateKeyPath, privateKeyPEM, 0600); err != nil {
		return fmt.Errorf("failed to save private key: %v", err)
	}

	// 保存公钥
	publicKeyPath := filepath.Join(certDir, "rsa_default.pub")
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(keyPair.PublicKey)
	if err != nil {
		return fmt.Errorf("failed to marshal public key: %v", err)
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	if err := ioutil.WriteFile(publicKeyPath, publicKeyPEM, 0644); err != nil {
		return fmt.Errorf("failed to save public key: %v", err)
	}

	log.Printf("RSA keys saved to %s and %s", privateKeyPath, publicKeyPath)
	return nil
}

// RSA证书生成相关函数

// GenerateRSACertificateRequest RSA证书生成请求
type GenerateRSACertificateRequest struct {
	CommonName       string   `json:"common_name"`
	Organization     string   `json:"organization"`
	OrganizationUnit string   `json:"organization_unit"`
	Country          string   `json:"country"`
	Province         string   `json:"province"`
	Locality         string   `json:"locality"`
	DNSNames         []string `json:"dns_names"`
	IPAddresses      []string `json:"ip_addresses"`
	KeySize          int      `json:"key_size"`
	ValidityPeriod   struct {
		Months int    `json:"months"`
		Label  string `json:"label"`
	} `json:"validity_period"`
}

// GenerateRSACertificate 生成RSA证书（10年有效期）
func GenerateRSACertificate(req GenerateRSACertificateRequest) (*RSACertificateInfo, error) {
	// 设置默认值
	if req.KeySize == 0 {
		req.KeySize = 2048
	}
	if req.ValidityPeriod.Months == 0 {
		req.ValidityPeriod.Months = 120 // 默认10年（120个月）
		req.ValidityPeriod.Label = "10年"
	}
	if req.CommonName == "" {
		req.CommonName = "IPC Management System"
	}
	if req.Organization == "" {
		req.Organization = "IPC Management"
	}
	if req.Country == "" {
		req.Country = "CN"
	}

	// 生成RSA密钥对
	privateKey, err := rsa.GenerateKey(rand.Reader, req.KeySize)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA key: %v", err)
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			CommonName:         req.CommonName,
			Organization:       []string{req.Organization},
			OrganizationalUnit: []string{req.OrganizationUnit},
			Country:            []string{req.Country},
			Province:           []string{req.Province},
			Locality:           []string{req.Locality},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(0, req.ValidityPeriod.Months, 0), // 根据月数设置有效期
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth, x509.ExtKeyUsageClientAuth},
		BasicConstraintsValid: true,
		DNSNames:              req.DNSNames,
	}

	// 解析IP地址
	for _, ipStr := range req.IPAddresses {
		if ip := net.ParseIP(ipStr); ip != nil {
			template.IPAddresses = append(template.IPAddresses, ip)
		}
	}

	// 生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create certificate: %v", err)
	}

	// 保存证书和私钥到文件
	certDir := "./certificate"
	if err := os.MkdirAll(certDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create certificate directory: %v", err)
	}

	// 生成文件名（基于时间戳）
	timestamp := time.Now().Format("20060102_150405")
	certFileName := fmt.Sprintf("rsa_cert_%s.crt", timestamp)
	keyFileName := fmt.Sprintf("rsa_cert_%s.key", timestamp)
	pubFileName := fmt.Sprintf("rsa_cert_%s.pub", timestamp)

	certPath := filepath.Join(certDir, certFileName)
	keyPath := filepath.Join(certDir, keyFileName)
	pubPath := filepath.Join(certDir, pubFileName)

	// 保存证书文件
	certPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "CERTIFICATE",
		Bytes: certDER,
	})
	if err := os.WriteFile(certPath, certPEM, 0644); err != nil {
		return nil, fmt.Errorf("failed to save certificate: %v", err)
	}

	// 保存私钥文件
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})
	if err := os.WriteFile(keyPath, privateKeyPEM, 0600); err != nil {
		return nil, fmt.Errorf("failed to save private key: %v", err)
	}

	// 保存公钥文件
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal public key: %v", err)
	}
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})
	if err := os.WriteFile(pubPath, publicKeyPEM, 0644); err != nil {
		return nil, fmt.Errorf("failed to save public key: %v", err)
	}

	// 解析证书以获取详细信息
	cert, err := x509.ParseCertificate(certDER)
	if err != nil {
		return nil, fmt.Errorf("failed to parse generated certificate: %v", err)
	}

	// 创建证书信息
	certInfo := &RSACertificateInfo{
		PrivateKeyPath:  keyPath,
		CertificatePath: certPath,
		Subject:         cert.Subject.String(),
		Issuer:          cert.Issuer.String(),
		NotBefore:       cert.NotBefore,
		NotAfter:        cert.NotAfter,
		IsExpired:       time.Now().After(cert.NotAfter),
		DaysToExpiry:    int(time.Until(cert.NotAfter).Hours() / 24),
		KeySize:         req.KeySize,
		SerialNumber:    cert.SerialNumber.String(),
	}

	log.Printf("RSA certificate generated: %s (valid for %s)", certPath, req.ValidityPeriod.Label)
	return certInfo, nil
}

// SwitchToDefaultKeyPair 切换回默认密钥对
func SwitchToDefaultKeyPair() error {
	if originalDefaultKeyPair == nil {
		return errors.New("original default key pair not available")
	}

	// 恢复原始默认密钥对
	globalKeyPair = &RSAKeyPair{
		PrivateKey: originalDefaultKeyPair.PrivateKey,
		PublicKey:  originalDefaultKeyPair.PublicKey,
	}

	log.Println("Switched back to original default key pair")
	return nil
}

// EncryptAccountInfo 加密账户信息数据
func EncryptAccountInfo(accountInfo map[string]interface{}) (string, error) {
	if globalKeyPair == nil {
		return "", errors.New("RSA key pair not initialized")
	}

	// 序列化账户信息
	jsonData, err := json.Marshal(accountInfo)
	if err != nil {
		return "", fmt.Errorf("failed to marshal account info: %v", err)
	}

	// 使用RSA公钥加密
	encryptedData, err := EncryptWithPublicKey(jsonData, globalKeyPair.PublicKey)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt account info: %v", err)
	}

	// Base64编码
	return base64.StdEncoding.EncodeToString(encryptedData), nil
}

// generateDefaultCertificate 为默认RSA密钥对生成证书文件
func generateDefaultCertificate() error {
	if globalKeyPair == nil {
		return errors.New("RSA key pair not initialized")
	}

	certDir := "./certificate"
	defaultCertPath := filepath.Join(certDir, "rsa_default.crt")
	defaultPrivatePath := filepath.Join(certDir, "rsa_default.key")
	defaultPublicPath := filepath.Join(certDir, "rsa_default.pub")

	// 删除旧的默认证书文件，重新生成以确保一致性
	os.Remove(defaultCertPath)
	os.Remove(defaultPrivatePath)
	os.Remove(defaultPublicPath)
	log.Println("Regenerating default certificate files to ensure consistency")

	// 创建证书模板（10年有效期）
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			CommonName:         "IPC Management System (Default)",
			Organization:       []string{"IPC Management"},
			OrganizationalUnit: []string{"Default RSA Certificate"},
			Country:            []string{"CN"},
			Province:           []string{"Beijing"},
			Locality:           []string{"Beijing"},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(10, 0, 0), // 10年有效期
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth, x509.ExtKeyUsageClientAuth},
		BasicConstraintsValid: true,
		DNSNames:              []string{"localhost", "127.0.0.1"},
		IPAddresses:           []net.IP{net.IPv4(127, 0, 0, 1), net.IPv6loopback},
	}

	// 使用当前的RSA密钥对生成证书
	certDER, err := x509.CreateCertificate(rand.Reader, &template, &template, globalKeyPair.PublicKey, globalKeyPair.PrivateKey)
	if err != nil {
		return fmt.Errorf("failed to create default certificate: %v", err)
	}

	// 保存证书文件
	certPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "CERTIFICATE",
		Bytes: certDER,
	})
	if err := os.WriteFile(defaultCertPath, certPEM, 0644); err != nil {
		return fmt.Errorf("failed to save default certificate: %v", err)
	}

	// 保存私钥文件（与证书匹配）
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(globalKeyPair.PrivateKey)
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	})
	if err := os.WriteFile(defaultPrivatePath, privateKeyPEM, 0600); err != nil {
		return fmt.Errorf("failed to save default private key: %v", err)
	}

	// 保存公钥文件（与证书匹配）
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(globalKeyPair.PublicKey)
	if err != nil {
		return fmt.Errorf("failed to marshal default public key: %v", err)
	}
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})
	if err := os.WriteFile(defaultPublicPath, publicKeyPEM, 0644); err != nil {
		return fmt.Errorf("failed to save default public key: %v", err)
	}

	log.Printf("Default RSA certificate set generated: %s, %s, %s (valid for 10 years)",
		defaultCertPath, defaultPrivatePath, defaultPublicPath)
	return nil
}
