@echo off
setlocal enabledelayedexpansion
title IPC Management System Launcher

cls
echo.
echo ============== IPC Management System Launcher ==============
echo.

:: Detect local IP address
echo Detecting IP address...
set LOCAL_IP=localhost
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4" ^| findstr /v "127.0.0.1"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set LOCAL_IP=%%b
        set LOCAL_IP=!LOCAL_IP: =!
        goto :found_ip
    )
)

:found_ip
echo Using IP address: !LOCAL_IP!

:: Configure frontend environment
echo Configuring frontend...
if exist "frontend\.env.local" del "frontend\.env.local" >nul 2>&1
echo VITE_BACKEND_URL=http://!LOCAL_IP!:8080> "frontend\.env.local"
if exist "frontend\.env.local" (
    echo Frontend configured successfully
) else (
    echo Warning: Could not create frontend config file
    echo Using default localhost configuration
)

echo.
echo ============== Starting Services ==============
echo Backend URL: http://!LOCAL_IP!:8080
echo Frontend URL: http://!LOCAL_IP!:5173
echo MQTT URL: mqtt://!LOCAL_IP!:1883
echo.
echo Starting servers...

:: Start backend server
start "IPC Backend Server" cmd /k "title IPC Backend Server && echo Starting backend server... && go run . && echo. && echo Backend server stopped && pause"

:: Wait for backend to start
timeout /t 3 /nobreak >nul

:: Start frontend server
start "IPC Frontend Server" cmd /k "title IPC Frontend Server && echo Starting frontend server... && cd frontend && npm run dev && echo. && echo Frontend server stopped && pause"

echo.
echo ============== Startup Complete ==============
echo Backend and frontend servers started in new windows
echo Check the new windows for startup status
echo To stop services, close the corresponding server windows
echo.
echo Access URLs:
echo   - Management UI: http://!LOCAL_IP!:5173
echo   - API Endpoint: http://!LOCAL_IP!:8080
echo   - MQTT Service: mqtt://!LOCAL_IP!:1883
echo.
echo Default Admin Account:
echo   - Username: admin
echo   - Password: admin123
echo.
pause
