<template>
  <div id="app">
    <!-- 根据登录状态显示不同的视图 -->
    <LoginView v-if="!isLoggedIn" @login="handleLogin" />
    <MainView v-else :username="username" @logout="handleLogout" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import LoginView from './views/LoginView.vue'
import MainView from './views/MainView.vue'

// 应用基本状态（只管理认证状态）
const isLoggedIn = ref(false)
const username = ref('')

// 方法
const handleLogin = (formData) => {
  // 登录成功处理
  username.value = formData.username
  isLoggedIn.value = true

  // 设置 axios 默认认证头
  if (formData.token) {
    axios.defaults.headers.common['Authorization'] = `Bearer ${formData.token}`
    // 保存 token 到 localStorage
    localStorage.setItem('token', formData.token)
    localStorage.setItem('username', formData.username)
  }
}

const handleLogout = () => {
  isLoggedIn.value = false
  username.value = ''

  // 清除认证信息
  delete axios.defaults.headers.common['Authorization']
  localStorage.removeItem('token')
  localStorage.removeItem('username')
}

// 检查是否已经登录（页面刷新时恢复状态）
const checkAuthStatus = () => {
  const token = localStorage.getItem('token')
  const savedUsername = localStorage.getItem('username')

  if (token && savedUsername) {
    // 设置 axios 默认认证头
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    username.value = savedUsername
    isLoggedIn.value = true
  }
}

// 生命周期
onMounted(() => {
  checkAuthStatus()
})
</script>

<style>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  overflow: hidden;
}
</style>
