<template>
  <div class="device-form">
    <el-form :model="formData" label-width="120px" label-position="left">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备ID" required>
            <el-input 
              v-model="formData.device_id" 
              placeholder="请输入设备ID"
              :disabled="isEdit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="设备名称" required>
            <el-input 
              v-model="formData.device_name" 
              placeholder="请输入设备名称"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设备类型" required>
            <el-select 
              v-model="formData.device_type" 
              placeholder="请选择设备类型"
              style="width: 100%"
            >
              <el-option
                v-for="type in deviceTypes"
                :key="type.type_code"
                :label="type.type_name"
                :value="type.type_code"
              >
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                  <i :class="type.icon" :style="{ color: type.color }"></i>
                  <span>{{ type.type_name }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属用户">
            <el-select
              v-model="formData.user_id"
              placeholder="请选择用户（可选）"
              clearable
              filterable
              style="width: 100%"
              @focus="loadUsers"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.username"
                :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="设备状态">
        <el-switch 
          v-model="formData.is_active"
          active-text="启用"
          inactive-text="禁用"
        />
      </el-form-item>
    </el-form>

    <div class="form-actions">
      <el-button @click="$emit('cancel')">取消</el-button>
      <el-button 
        type="primary" 
        @click="handleSave"
        :loading="loading"
      >
        {{ loading ? '保存中...' : '保存' }}
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  deviceTypes: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['save', 'cancel'])

// 用户列表
const users = ref([])
const usersLoading = ref(false)

// 加载用户列表
const loadUsers = async () => {
  if (users.value.length > 0) return // 已加载过

  usersLoading.value = true
  try {
    const response = await axios.get('/api/users', {
      params: { page: 1, page_size: 1000 } // 加载所有用户
    })
    users.value = response.data.users || []
  } catch (error) {
    console.error('Failed to load users:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

const handleSave = () => {
  // 基本验证
  if (!props.formData.device_id) {
    ElMessage.error('请输入设备ID')
    return
  }
  
  if (!props.formData.device_name) {
    ElMessage.error('请输入设备名称')
    return
  }
  
  if (!props.formData.device_type) {
    ElMessage.error('请选择设备类型')
    return
  }
  
  // 验证设备ID格式（可选）
  const deviceIdPattern = /^[a-zA-Z0-9_-]+$/
  if (!deviceIdPattern.test(props.formData.device_id)) {
    ElMessage.error('设备ID只能包含字母、数字、下划线和连字符')
    return
  }
  
  emit('save')
}

// 生命周期
onMounted(() => {
  // 预加载用户列表
  loadUsers()
})
</script>

<style scoped>
.device-form {
  padding: 0;
}

.form-actions {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.el-form-item {
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .el-col {
    margin-bottom: 1rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
}
</style>
