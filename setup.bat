@echo off
chcp 65001 > nul

echo IPC Management System - Environment Setup

rem Check Go environment
echo Checking Go environment...
where go >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Go environment not found
    pause
    exit /b 1
)
echo Go environment check passed

rem Check Node.js environment
echo Checking Node.js environment...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js environment not found
    pause
    exit /b 1
)
echo Node.js environment check passed

rem Check npm
echo Checking npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: npm not found
    pause
    exit /b 1
)
echo npm check passed

rem Configure Go proxy
echo Configuring Go proxy...
go env -w GOPROXY=https://goproxy.cn,direct
echo Go proxy configuration completed

rem Install Go dependencies
echo Installing Go dependencies...
go mod tidy
echo Go dependencies installation completed

rem Check frontend dependencies
echo Checking frontend dependencies...
if not exist "frontend\node_modules" (
    echo Installing frontend dependencies...
    cd frontend
    call npm install
    cd ..
    echo Frontend dependencies installation completed
) else (
    echo Frontend dependencies already exist
)

echo Environment Setup Completed
echo All dependencies have been installed
echo You can now use start.bat to launch the system
pause

