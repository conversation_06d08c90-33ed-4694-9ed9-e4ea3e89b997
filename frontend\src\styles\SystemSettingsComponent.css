.settings-management {
  padding: 20px;
}

.settings-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
}

:deep(.el-tabs__header) {
  margin: 0;
  background-color: #f8f9fa;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__item) {
  height: 50px;
  line-height: 50px;
  font-weight: 500;
  color: #666;
}

:deep(.el-tabs__item.is-active) {
  color: #667eea;
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  background-color: #667eea;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eef2f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
}

.modern-btn.secondary {
  background: #909399;
  color: white;
}

.modern-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modern-btn.warning {
  background: #f2c94c;
  color: #333;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  padding: 20px;
}

.security-form {
  max-width: 800px;
}

:deep(.el-form-item) {
  margin-bottom: 25px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #333;
}

.form-help {
  margin-top: 10px;
  font-size: 12px;
  line-height: 1.5;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.config-help {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.range {
  color: #667eea;
  font-weight: 500;
}

.desc {
  color: #999;
  font-size: 12px;
}

.rsa-status-section {
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  width: 120px !important;
}

:deep(.el-alert) {
  border-radius: 8px;
}

:deep(.el-alert__title) {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-management {
    padding: 15px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .card-actions {
    justify-content: center;
  }
  
  .security-form {
    max-width: none;
  }
  
  :deep(.el-descriptions__label) {
    width: 100px !important;
  }
}