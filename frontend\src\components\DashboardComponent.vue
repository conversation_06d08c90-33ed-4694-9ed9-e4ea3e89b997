<template>
  <div class="management-container">
    <!-- 统计卡片网格 - 使用通用类 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">当前用户</span>
          <div class="stat-icon primary">
            <i class="fas fa-user"></i>
          </div>
        </div>
        <div class="stat-value">{{ username }}</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">总用户数</span>
          <div class="stat-icon success">
            <i class="fas fa-users"></i>
          </div>
        </div>
        <div class="stat-value">{{ totalUsers }}</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">系统状态</span>
          <div class="stat-icon success">
            <i class="fas fa-check-circle"></i>
          </div>
        </div>
        <div class="stat-value">运行中</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">MQTT状态</span>
          <div :class="['stat-icon', mqttStats.running ? 'success' : 'danger']">
            <i class="fas fa-wifi"></i>
          </div>
        </div>
        <div class="stat-value">{{ mqttStats.running ? '运行中' : '已停止' }}</div>
      </div>
    </div>

    <!-- 功能卡片 - 使用统一标准结构 -->
    <div class="grid-layout-2col">
      <!-- 系统概览 -->
      <div class="modern-card">
        <div class="card-header">
          <h4 class="card-title">
            <i class="fas fa-server"></i>
            系统概览
          </h4>
        </div>
        <div class="card-content">
          <div class="info-list">
            <div class="info-item">
              <span class="info-label">应用版本:</span>
              <span class="info-value">{{ getDisplayVersion() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">运行时间:</span>
              <span class="info-value success">正常运行</span>
            </div>
            <div class="info-item">
              <span class="info-label">数据库状态:</span>
              <span class="info-value success">连接正常</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="modern-card">
        <div class="card-header">
          <h4 class="card-title">
            <i class="fas fa-bolt"></i>
            快速操作
          </h4>
        </div>
        <div class="card-content">
          <div class="form-group">
            <button @click="() => emit('menu-select', 'users')" class="modern-btn quick-action-btn">
              <i class="fas fa-users"></i>
              用户管理
            </button>
            <button @click="() => emit('menu-select', 'mqtt')" class="modern-btn quick-action-btn">
              <i class="fas fa-network-wired"></i>
              MQTT管理
            </button>
            <button @click="() => emit('menu-select', 'logs')" class="modern-btn quick-action-btn">
              <i class="fas fa-clipboard-list"></i>
              操作日志
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getDisplayVersion } from '../config/version'

defineProps({
  username: {
    type: String,
    required: true
  },
  totalUsers: {
    type: Number,
    required: true
  },
  mqttStats: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['menu-select'])
</script>

<style scoped src="../styles/DashboardComponent.css"></style>