<template>
  <div class="user-management">
    <!-- 操作工具栏 -->
    <div class="modern-card" style="margin-bottom: 1rem;">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-users"></i>
          用户列表
        </h3>
        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
          <div class="modern-search">
            <i class="fas fa-search"></i>
            <input
              v-model="searchKeyword"
              @input="handleSearch"
              placeholder="搜索用户名..."
              class="search-input"
            />
          </div>
          
          <!-- 批量操作按钮组 -->
          <div class="batch-operations" style="display: flex; gap: 0.5rem; align-items: center;">
            <button 
              @click="batchToggleUsers('enable')" 
              class="modern-btn success small"
              :disabled="selectedUsers.length === 0 || batchActionLoading"
              :title="selectedUsers.length === 0 ? '请先选择用户' : `批量启用 ${selectedUsers.length} 个用户`"
            >
              <i class="fas fa-check"></i>
              {{ batchActionLoading ? '处理中...' : '批量启用' }}
            </button>
            <button 
              @click="batchToggleUsers('disable')" 
              class="modern-btn warning small"
              :disabled="selectedUsers.length === 0 || batchActionLoading"
              :title="selectedUsers.length === 0 ? '请先选择用户' : `批量禁用 ${selectedUsers.length} 个用户`"
            >
              <i class="fas fa-ban"></i>
              {{ batchActionLoading ? '处理中...' : '批量禁用' }}
            </button>
          </div>
          
          <!-- 分隔线 -->
          <div style="height: 30px; width: 1px; background: #e5e7eb; margin: 0 0.5rem;"></div>
          
          <!-- 添加用户按钮 -->
          <button @click="showAddUserDialogHandler" class="modern-btn primary">
            <i class="fas fa-plus"></i>
            添加用户
          </button>
        </div>
      </div>
    </div>

    <!-- 用户表格 -->
    <div class="modern-card">
      <div class="card-content">
        <el-table 
          :data="users" 
          v-loading="usersLoading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="username" label="用户名" min-width="120">
            <template #default="scope">
              <div class="user-info">
                <div class="user-avatar">{{ scope.row.username.charAt(0).toUpperCase() }}</div>
                <span class="user-name">{{ scope.row.username }}</span>
                <el-tag v-if="scope.row.username === 'admin'" type="danger" size="small">管理员</el-tag>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="email" label="邮箱" min-width="150" />
          <el-table-column prop="phone" label="电话" min-width="120" />
          
          <el-table-column label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="使用情况" min-width="120">
            <template #default="scope">
              <div v-if="scope.row.usage_control_enabled">
                {{ scope.row.current_usage || 0 }} / {{ scope.row.max_usage || 0 }}
              </div>
              <el-tag v-else type="info" size="small">无限制</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" min-width="150">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <button
                  class="modern-btn primary small"
                  @click="editUser(scope.row)"
                >
                  <i class="fas fa-edit"></i>
                  编辑
                </button>
                <button
                  class="modern-btn danger small"
                  @click="deleteUser(scope.row)"
                  :disabled="scope.row.username === 'admin'"
                  :title="scope.row.username === 'admin' ? '不能删除系统管理员' : '删除用户'"
                >
                  <i class="fas fa-trash"></i>
                  删除
                </button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <div class="user-pagination">
          <el-pagination
            :current-page="pagination.page"
            :page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 添加用户对话框 -->
    <el-dialog
      v-model="showAddUserDialog"
      title="添加用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <UserForm
        :form-data="userForm"
        :loading="userFormLoading"
        @save="saveUser"
        @cancel="showAddUserDialog = false"
      />
    </el-dialog>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="showEditUserDialog"
      title="编辑用户"
      width="600px"
      :close-on-click-modal="false"
    >
      <UserForm
        :form-data="userForm"
        :loading="userFormLoading"
        :is-edit="true"
        @save="saveUser"
        @cancel="showEditUserDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import UserForm from './UserForm.vue'

// 用户管理状态
const users = ref([])
const usersLoading = ref(false)
const batchActionLoading = ref(false)
const searchKeyword = ref('')
const selectedUsers = ref([])

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0
})

// 用户对话框状态
const showAddUserDialog = ref(false)
const showEditUserDialog = ref(false)
const currentEditUser = ref(null)
const userFormLoading = ref(false)

// 用户表单数据
const userForm = reactive({
  username: '',
  password: '',
  email: '',
  phone: '',
  remark: '',
  maxUsage: 20,
  usageControlEnabled: true,
  canLoginBackend: true,
  canLoginMqtt: true,
  expireTime: '',
  isActive: true,
  accountInfoEncryption: false,
  accountInfoContent: ['expire_time', 'max_usage']
})

// 方法
const loadUsers = async () => {
  usersLoading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize
    }
    
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    
    const response = await axios.get('/api/users', { params })
    users.value = response.data.users || []
    pagination.total = response.data.total || 0
    pagination.totalPages = response.data.total_pages || 0
  } catch (error) {
    console.error('Failed to load users:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    usersLoading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadUsers()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadUsers()
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection.map(user => user.id)
}

const showAddUserDialogHandler = () => {
  resetUserForm()
  showAddUserDialog.value = true
}

const editUser = (user) => {
  currentEditUser.value = user
  Object.assign(userForm, {
    username: user.username,
    password: '',
    email: user.email || '',
    phone: user.phone || '',
    remark: user.remark || '',
    maxUsage: user.max_usage || 20,
    usageControlEnabled: user.usage_control_enabled,
    canLoginBackend: user.can_login_backend,
    canLoginMqtt: user.can_login_mqtt,
    expireTime: user.expire_time ? user.expire_time.split('T')[0] : '',
    isActive: user.is_active,
    accountInfoEncryption: user.account_info_encryption,
    accountInfoContent: user.account_info_content ? user.account_info_content.split(',') : ['expire_time', 'max_usage']
  })
  showEditUserDialog.value = true
}

const deleteUser = async (user) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await axios.delete(`/api/users/${user.id}`)
    ElMessage.success('用户删除成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete user:', error)
      ElMessage.error('删除用户失败')
    }
  }
}

const batchToggleUsers = async (action) => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户')
    return
  }
  
  batchActionLoading.value = true
  try {
    await axios.post('/api/users/batch-toggle', {
      user_ids: selectedUsers.value,
      action: action
    })
    
    ElMessage.success(`批量${action === 'enable' ? '启用' : '禁用'}用户成功`)
    selectedUsers.value = []
    loadUsers()
  } catch (error) {
    console.error('Failed to batch toggle users:', error)
    ElMessage.error(`批量${action === 'enable' ? '启用' : '禁用'}用户失败`)
  } finally {
    batchActionLoading.value = false
  }
}

const resetUserForm = () => {
  Object.assign(userForm, {
    username: '',
    password: '',
    email: '',
    phone: '',
    remark: '',
    maxUsage: 20,
    usageControlEnabled: true,
    canLoginBackend: true,
    canLoginMqtt: true,
    expireTime: '',
    isActive: true,
    accountInfoEncryption: false,
    accountInfoContent: ['expire_time', 'max_usage']
  })
}

const saveUser = async () => {
  userFormLoading.value = true
  try {
    const userData = {
      username: userForm.username,
      password: userForm.password,
      email: userForm.email,
      phone: userForm.phone,
      remark: userForm.remark,
      max_usage: userForm.maxUsage,
      usage_control_enabled: userForm.usageControlEnabled,
      can_login_backend: userForm.canLoginBackend,
      can_login_mqtt: userForm.canLoginMqtt,
      expire_time: userForm.expireTime,
      is_active: userForm.isActive,
      account_info_encryption: userForm.accountInfoEncryption,
      account_info_content: userForm.accountInfoContent
    }
    
    if (currentEditUser.value) {
      // 编辑用户
      await axios.put(`/api/users/${currentEditUser.value.id}`, userData)
      ElMessage.success('用户更新成功')
      showEditUserDialog.value = false
    } else {
      // 创建用户
      await axios.post('/api/users', userData)
      ElMessage.success('用户创建成功')
      showAddUserDialog.value = false
    }
    
    loadUsers()
    resetUserForm()
    currentEditUser.value = null
  } catch (error) {
    console.error('Failed to save user:', error)
    ElMessage.error(currentEditUser.value ? '更新用户失败' : '创建用户失败')
  } finally {
    userFormLoading.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  padding: 0;
}

.modern-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  padding: 1.5rem;
}

.modern-search {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search i {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 200px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.modern-btn.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.modern-btn.primary {
  background: #3b82f6;
  color: white;
}

.modern-btn.primary:hover {
  background: #2563eb;
}

.modern-btn.success {
  background: #10b981;
  color: white;
}

.modern-btn.success:hover {
  background: #059669;
}

.modern-btn.warning {
  background: #f59e0b;
  color: white;
}

.modern-btn.warning:hover {
  background: #d97706;
}

.modern-btn.danger {
  background: #ef4444;
  color: white;
}

.modern-btn.danger:hover {
  background: #dc2626;
}

.modern-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.user-name {
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.user-pagination {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
  
  .batch-operations {
    flex-wrap: wrap;
  }
}
</style>
