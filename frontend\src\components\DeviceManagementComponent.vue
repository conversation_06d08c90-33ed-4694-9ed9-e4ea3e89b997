<template>
  <div class="device-management">
    <!-- 统计卡片区域 -->
    <div class="device-stats-cards">
      <div class="stat-card">
        <div class="stat-icon primary">
          <i class="fas fa-mobile-alt"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ devices.length }}</div>
          <div class="stat-label">设备总数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon success">
          <i class="fas fa-wifi"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ onlineDevicesCount }}</div>
          <div class="stat-label">在线设备</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon danger">
          <i class="fas fa-times-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ offlineDevicesCount }}</div>
          <div class="stat-label">离线设备</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon success">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ activeDevicesCount }}</div>
          <div class="stat-label">启用设备</div>
        </div>
      </div>
    </div>

    <!-- 操作工具栏 -->
    <div class="modern-card" style="margin-bottom: 1rem;">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-mobile-alt"></i>
          设备列表
        </h3>
        <div style="display: flex; gap: 1rem; align-items: center; flex-wrap: wrap;">
          <div class="modern-search">
            <i class="fas fa-search"></i>
            <input
              v-model="searchKeyword"
              @input="handleSearch"
              placeholder="搜索设备ID或名称..."
              class="search-input"
            />
          </div>
          
          <!-- 设备类型筛选 -->
          <el-select
            v-model="selectedDeviceType"
            placeholder="筛选设备类型"
            clearable
            @change="handleSearch"
            style="width: 200px"
          >
            <el-option
              v-for="type in deviceTypes"
              :key="type.type_code"
              :label="type.type_name"
              :value="type.type_code"
            />
          </el-select>
          
          <!-- 添加设备按钮 -->
          <button @click="showAddDeviceDialog" class="modern-btn primary">
            <i class="fas fa-plus"></i>
            添加设备
          </button>
        </div>
      </div>
    </div>

    <!-- 设备表格 -->
    <div class="modern-card">
      <div class="card-content">
        <el-table 
          :data="devices" 
          v-loading="devicesLoading"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="device_id" label="设备ID" min-width="150">
            <template #default="scope">
              <div class="device-info">
                <div class="device-status" :class="{ online: isDeviceOnline(scope.row.device_id) }"></div>
                <span class="device-id">{{ scope.row.device_id }}</span>
              </div>
            </template>
          </el-table-column>
          
          <el-table-column prop="device_name" label="设备名称" min-width="120" />
          
          <el-table-column label="设备类型" min-width="120">
            <template #default="scope">
              <el-tag :color="getDeviceTypeColor(scope.row.device_type)" size="small">
                {{ getDeviceTypeName(scope.row.device_type) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column label="所属用户" min-width="100">
            <template #default="scope">
              {{ scope.row.username || '-' }}
            </template>
          </el-table-column>
          
          <el-table-column label="状态" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.is_active ? 'success' : 'danger'" size="small">
                {{ scope.row.is_active ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="created_at" label="创建时间" min-width="150">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="scope">
              <div class="action-buttons">
                <button
                  class="modern-btn primary small"
                  @click="editDevice(scope.row)"
                >
                  <i class="fas fa-edit"></i>
                  编辑
                </button>
                <button
                  class="modern-btn danger small"
                  @click="deleteDevice(scope.row)"
                >
                  <i class="fas fa-trash"></i>
                  删除
                </button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页组件 -->
        <div class="device-pagination">
          <el-pagination
            :current-page="pagination.page"
            :page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handlePageSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 添加/编辑设备对话框 -->
    <el-dialog
      v-model="showDeviceDialog"
      :title="isEditMode ? '编辑设备' : '添加设备'"
      width="600px"
      :close-on-click-modal="false"
    >
      <DeviceForm
        :form-data="deviceForm"
        :device-types="deviceTypes"
        :loading="deviceFormLoading"
        :is-edit="isEditMode"
        @save="saveDevice"
        @cancel="showDeviceDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import DeviceForm from './DeviceForm.vue'

// 设备管理状态
const devices = ref([])
const devicesLoading = ref(false)
const deviceTypes = ref([])
const deviceTypesLoading = ref(false)
const searchKeyword = ref('')
const selectedDeviceType = ref('')
const selectedDevices = ref([])
const onlineDevices = ref({})

// 分页状态
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0,
  totalPages: 0
})

// 设备对话框状态
const showDeviceDialog = ref(false)
const isEditMode = ref(false)
const currentEditDevice = ref(null)
const deviceFormLoading = ref(false)

// 设备表单数据
const deviceForm = reactive({
  device_id: '',
  device_name: '',
  device_type: '',
  user_id: null,
  is_active: true
})

// 计算属性
const onlineDevicesCount = computed(() => {
  return devices.value.filter(d => isDeviceOnline(d.device_id)).length
})

const offlineDevicesCount = computed(() => {
  return devices.value.filter(d => !isDeviceOnline(d.device_id)).length
})

const activeDevicesCount = computed(() => {
  return devices.value.filter(d => d.is_active).length
})

// 方法
const loadDevices = async () => {
  devicesLoading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize
    }
    
    if (searchKeyword.value) {
      params.keyword = searchKeyword.value
    }
    
    if (selectedDeviceType.value) {
      params.device_type = selectedDeviceType.value
    }
    
    const response = await axios.get('/api/devices', { params })
    devices.value = response.data.devices || []
    pagination.total = response.data.total || 0
    pagination.totalPages = response.data.total_pages || 0
  } catch (error) {
    console.error('Failed to load devices:', error)
    ElMessage.error('加载设备列表失败')
  } finally {
    devicesLoading.value = false
  }
}

const loadDeviceTypes = async () => {
  deviceTypesLoading.value = true
  try {
    const response = await axios.get('/api/device-types')
    deviceTypes.value = response.data.device_types || []
  } catch (error) {
    console.error('Failed to load device types:', error)
    ElMessage.error('加载设备类型失败')
  } finally {
    deviceTypesLoading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadDevices()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadDevices()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadDevices()
}

const handleSelectionChange = (selection) => {
  selectedDevices.value = selection.map(device => device.device_id)
}

const isDeviceOnline = (deviceId) => {
  return onlineDevices.value[deviceId] || false
}

const getDeviceTypeName = (typeCode) => {
  const type = deviceTypes.value.find(t => t.type_code === typeCode)
  return type ? type.type_name : typeCode || '未知类型'
}

const getDeviceTypeColor = (typeCode) => {
  const type = deviceTypes.value.find(t => t.type_code === typeCode)
  return type ? type.color : '#6b7280'
}

const showAddDeviceDialog = () => {
  resetDeviceForm()
  isEditMode.value = false
  showDeviceDialog.value = true
}

const editDevice = (device) => {
  currentEditDevice.value = device
  Object.assign(deviceForm, {
    device_id: device.device_id,
    device_name: device.device_name,
    device_type: device.device_type,
    user_id: device.user_id,
    is_active: device.is_active
  })
  isEditMode.value = true
  showDeviceDialog.value = true
}

const deleteDevice = async (device) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备 "${device.device_id}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await axios.delete(`/api/devices/${device.device_id}`)
    ElMessage.success('设备删除成功')
    loadDevices()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete device:', error)
      ElMessage.error('删除设备失败')
    }
  }
}

const resetDeviceForm = () => {
  Object.assign(deviceForm, {
    device_id: '',
    device_name: '',
    device_type: '',
    user_id: null,
    is_active: true
  })
}

const saveDevice = async () => {
  deviceFormLoading.value = true
  try {
    const deviceData = {
      device_id: deviceForm.device_id,
      device_name: deviceForm.device_name,
      device_type: deviceForm.device_type,
      user_id: deviceForm.user_id,
      is_active: deviceForm.is_active
    }

    if (isEditMode.value) {
      // 编辑设备
      await axios.put(`/api/devices/${currentEditDevice.value.device_id}`, deviceData)
      ElMessage.success('设备更新成功')
    } else {
      // 创建设备
      await axios.post('/api/devices', deviceData)
      ElMessage.success('设备创建成功')
    }

    showDeviceDialog.value = false
    loadDevices()
    resetDeviceForm()
    currentEditDevice.value = null
  } catch (error) {
    console.error('Failed to save device:', error)
    ElMessage.error(isEditMode.value ? '更新设备失败' : '创建设备失败')
  } finally {
    deviceFormLoading.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadDevices()
  loadDeviceTypes()
})
</script>

<style scoped>
.device-management {
  padding: 0;
}

.device-stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon.primary { background: #3b82f6; }
.stat-icon.success { background: #10b981; }
.stat-icon.danger { background: #ef4444; }
.stat-icon.warning { background: #f59e0b; }

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.modern-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.card-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  padding: 1.5rem;
}

.modern-search {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search i {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  padding: 0.5rem 0.75rem 0.5rem 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  width: 200px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.modern-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.modern-btn.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.modern-btn.primary {
  background: #3b82f6;
  color: white;
}

.modern-btn.primary:hover {
  background: #2563eb;
}

.modern-btn.danger {
  background: #ef4444;
  color: white;
}

.modern-btn.danger:hover {
  background: #dc2626;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.device-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ef4444;
}

.device-status.online {
  background: #10b981;
}

.device-id {
  font-family: monospace;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.device-pagination {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .device-stats-cards {
    grid-template-columns: 1fr;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-input {
    width: 100%;
  }
}
</style>
