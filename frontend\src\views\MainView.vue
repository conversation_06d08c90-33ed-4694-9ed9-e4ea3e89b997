<template>
  <Layout 
    :username="username"
    :active-menu="activeMenu"
    @menu-select="handleMenuSelect"
    @logout="handleLogout"
  >
    <!-- 路由内容 -->
    <DashboardComponent
      v-if="activeMenu === 'dashboard'"
      :username="username"
      :total-users="totalUsers"
      :mqtt-stats="mqttStats"
      @menu-select="handleMenuSelect"
    />
    <UserManagementComponent v-else-if="activeMenu === 'users'" />
    <DeviceManagementComponent v-else-if="activeMenu === 'devices'" />
    <MqttManagementComponent v-else-if="activeMenu === 'mqtt'" />
    <LogsManagementComponent
      v-else-if="activeMenu === 'logs'"
      :logs="[]"
      :log-stats="defaultLogStats"
      :log-filters="defaultLogFilters"
      :log-pagination="defaultLogPagination"
    />
    <SystemSettingsComponent
      v-else-if="activeMenu === 'settings'"
      :security-config="defaultSecurityConfig"
      :rate-limit-config="defaultRateLimitConfig"
      :database-config="defaultDatabaseConfig"
      :validation-config="defaultValidationConfig"
      :debug-config="defaultDebugConfig"
      :captcha-config="defaultCaptchaConfig"
      :rsa-cert-info="defaultRsaCertInfo"
    />
  </Layout>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import DashboardComponent from '../components/DashboardComponent.vue'
import UserManagementComponent from '../components/UserManagementComponent.vue'
import DeviceManagementComponent from '../components/DeviceManagementComponent.vue'
import MqttManagementComponent from '../components/MqttManagementComponent.vue'
import SystemSettingsComponent from '../components/SystemSettingsComponent.vue'
import LogsManagementComponent from '../components/LogsManagementComponent.vue'
import axios from 'axios'

const props = defineProps({
  username: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['logout'])

// 路由状态
const activeMenu = ref('dashboard')

// 仪表盘数据
const totalUsers = ref(0)
const mqttStats = reactive({
  running: false,
  clients: 0,
  topics: 0,
  blacklist_count: 0,
  client_list: [],
  blacklist: [],
  failed_auth: []
})

// 设备管理数据已移至 DeviceManagementComponent 自管理
// MQTT管理数据已移至 MqttManagementComponent 自管理

const defaultLogStats = reactive({
  today_count: 0,
  success_rate: 0
})

const defaultLogFilters = reactive({
  operation_type: '',
  module: '',
  keyword: ''
})

const defaultLogPagination = reactive({
  page: 1,
  page_size: 10,
  total_count: 0
})

const defaultSecurityConfig = reactive({
  cors_dev_mode: false,
  cors_allowed_origins: '',
  cors_allowed_methods: '',
  cors_allowed_headers: '',
  cors_allow_credentials: false,
  cors_max_age: 86400
})

const defaultRateLimitConfig = reactive({
  enable_rate_limit: true,
  requests_per_minute: 100,
  burst_size: 20,
  cleanup_interval: 300
})

const defaultDatabaseConfig = reactive({
  max_open_conns: 20,
  max_idle_conns: 5,
  conn_max_lifetime: 3600,
  conn_max_idle_time: 1800
})

const defaultValidationConfig = reactive({
  max_username_length: 50,
  max_password_length: 128,
  max_phone_length: 20,
  max_remark_length: 200,
  enable_xss_protection: true
})

const defaultDebugConfig = reactive({
  debug_mode: false,
  frontend_debug_logs: false
})

const defaultCaptchaConfig = reactive({
  img_width: 300,
  img_height: 150,
  point_radius: 20,
  tolerance_radius: 20,
  max_attempts: 5,
  expire_time: 300
})

const defaultRsaCertInfo = reactive({
  key_pair_initialized: false,
  key_info: null
})

// 计算属性（设备相关已移除）

// 方法
const handleMenuSelect = (menu) => {
  activeMenu.value = menu
  // 设备数据加载已移至 DeviceManagementComponent 自管理
}

const handleLogout = () => {
  emit('logout')
}

// 加载仪表盘数据
const loadDashboardData = async () => {
  try {
    // 加载用户总数
    const usersResponse = await axios.get('/api/users', { params: { page: 1, page_size: 1 } })
    totalUsers.value = usersResponse.data.total || 0

    // 加载MQTT统计
    const mqttResponse = await axios.get('/api/mqtt/stats')
    Object.assign(mqttStats, mqttResponse.data)
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
    // 设置默认值，避免组件报错
    totalUsers.value = 0
    Object.assign(mqttStats, {
      running: false,
      clients: 0,
      topics: 0,
      blacklist_count: 0,
      client_list: [],
      blacklist: [],
      failed_auth: []
    })
  }
}

// 设备数据加载已移至 DeviceManagementComponent 自管理

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
/* MainView 不需要额外样式，所有样式都在 Layout 组件中 */
</style>
