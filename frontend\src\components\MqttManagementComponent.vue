<template>
  <div class="mqtt-management">
    <!-- MQTT统计信息 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">服务器状态</span>
          <div class="stat-icon" :class="mqttStats.running ? 'success' : 'danger'">
            <i :class="mqttStats.running ? 'fas fa-check' : 'fas fa-times'"></i>
          </div>
        </div>
        <div class="stat-value" :class="mqttStats.running ? 'text-success' : 'text-danger'">
          {{ mqttStats.running ? '运行中' : '已停止' }}
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">连接客户端</span>
          <div class="stat-icon primary">
            <i class="fas fa-users"></i>
          </div>
        </div>
        <div class="stat-value">{{ mqttStats.clients || 0 }}</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">订阅主题</span>
          <div class="stat-icon warning">
            <i class="fas fa-list"></i>
          </div>
        </div>
        <div class="stat-value">{{ mqttStats.topics || 0 }}</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <span class="stat-label">黑名单数量</span>
          <div class="stat-icon danger">
            <i class="fas fa-ban"></i>
          </div>
        </div>
        <div class="stat-value">{{ mqttStats.blacklist_count || 0 }}</div>
      </div>
    </div>

    <!-- MQTT内容区域 -->
    <el-tabs v-model="mqttActiveTab" type="card" class="mqtt-tabs">
      <el-tab-pane label="连接管理" name="clients">
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-plug"></i> 客户端列表</h4>
          </div>
          <div v-if="mqttStats.client_list && mqttStats.client_list.length > 0">
            <el-table
              :data="mqttStats.client_list"
              border
              stripe
              class="standard-table"
              size="small"
              :height="'auto'"
              :max-height="400"
            >
              <el-table-column prop="id" label="客户端ID" width="200" class-name="client-id-cell" show-overflow-tooltip />
              <el-table-column prop="username" label="用户名" width="90" show-overflow-tooltip />
              <el-table-column prop="client_ip" label="IP地址" width="120">
                <template #default="scope">
                  {{ formatIpAddress(scope.row.client_ip) }}
                </template>
              </el-table-column>
              <el-table-column prop="authenticated" label="状态" width="80">
                <template #default="scope">
                  <el-tag :type="scope.row.authenticated ? 'success' : 'danger'" size="small">
                    {{ scope.row.authenticated ? '认证' : '未认证' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="订阅主题" min-width="200">
                <template #default="scope">
                  <div v-if="scope.row.subscribed_topics && scope.row.subscribed_topics.length > 0">
                    <el-tag
                      v-for="(topic, index) in scope.row.subscribed_topics.slice(0, 3)"
                      :key="index"
                      size="small"
                      style="margin-right: 4px; margin-bottom: 2px;"
                    >
                      {{ topic }}
                    </el-tag>
                    <el-tag v-if="scope.row.subscribed_topics.length > 3" size="small" type="info">
                      +{{ scope.row.subscribed_topics.length - 3 }}
                    </el-tag>
                  </div>
                  <span v-else style="color: #999;">无订阅</span>
                </template>
              </el-table-column>
              <el-table-column prop="connected_at" label="连接时间" width="150">
                <template #default="scope">
                  {{ formatDate(scope.row.connected_at) }}
                </template>
              </el-table-column>
              <el-table-column label="心跳" width="100">
                <template #default="scope">
                  <div class="heartbeat-info">
                    <span class="heartbeat-time">{{ formatHeartbeat(scope.row.last_ping) }}</span>
                    <button 
                      class="heartbeat-monitor-btn" 
                      @click="showHeartbeatMonitor(scope.row.id)"
                      title="心跳监控"
                    >
                      <i class="fas fa-heartbeat"></i>
                    </button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="scope">
                  <el-button type="warning" size="small" @click="addClientToBlacklist(scope.row)">
                    加入黑名单
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p v-else style="text-align: center; color: #999; padding: 20px;">暂无客户端连接</p>
        </div>

        <!-- 黑名单管理 -->
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-ban"></i> 黑名单管理</h4>
            <button class="modern-btn primary" @click="showAddBlacklistDialog">
              <i class="fas fa-plus"></i>
              添加黑名单
            </button>
          </div>
          <div v-if="mqttStats.blacklist && mqttStats.blacklist.length > 0">
            <el-table
              :data="mqttStats.blacklist"
              border
              stripe
              class="standard-table"
              size="small"
              :height="'auto'"
              :max-height="300"
            >
              <el-table-column prop="ip" label="IP地址" width="140">
                <template #default="scope">
                  {{ formatIpAddress(scope.row.ip) }}
                </template>
              </el-table-column>
              <el-table-column prop="username" label="用户名" width="100" />
              <el-table-column prop="reason" label="封禁原因" show-overflow-tooltip />
              <el-table-column prop="created_at" label="封禁时间" width="150">
                <template #default="scope">
                  {{ formatDate(scope.row.created_at) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="danger" size="small" @click="removeFromBlacklist(scope.row.ip)">
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p v-else style="text-align: center; color: #999; padding: 20px;">暂无黑名单记录</p>
        </div>

        <!-- 失败认证记录 -->
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-lock"></i> 失败认证记录</h4>
          </div>
          <div v-if="mqttStats.failed_auth && mqttStats.failed_auth.length > 0">
            <el-table
              :data="mqttStats.failed_auth"
              border
              stripe
              class="standard-table"
              size="small"
              :height="'auto'"
              :max-height="300"
            >
              <el-table-column prop="ip" label="IP地址" width="140">
                <template #default="scope">
                  {{ formatIpAddress(scope.row.ip) }}
                </template>
              </el-table-column>
              <el-table-column prop="username" label="用户名" width="100" />
              <el-table-column prop="attempts" label="失败次数" width="80" />
              <el-table-column prop="last_attempt" label="最后尝试" width="150">
                <template #default="scope">
                  {{ formatDate(scope.row.last_attempt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button type="warning" size="small" @click="clearFailedAuth(scope.row.ip)">
                    清除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p v-else style="text-align: center; color: #999; padding: 20px;">暂无失败认证记录</p>
        </div>
      </el-tab-pane>

      <!-- 配置管理 -->
      <el-tab-pane label="配置管理" name="config">
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-cog"></i> MQTT服务器配置</h4>
          </div>
          <div class="card-content">
            <div class="config-form">
              <!-- 认证配置 -->
              <div class="config-section">
                <h5 class="section-title">认证配置</h5>
                <div class="switch-group">
                  <div class="switch-item">
                    <label class="form-label">启用认证</label>
                    <el-switch v-model="mqttConfig.require_auth" />
                  </div>
                  <div class="switch-item">
                    <label class="form-label">允许匿名连接</label>
                    <el-switch v-model="mqttConfig.allow_anonymous" />
                  </div>
                </div>
              </div>

              <!-- 安全配置 -->
              <div class="config-section">
                <h5 class="section-title">安全配置</h5>
                <div class="form-grid-2col">
                  <div class="form-group">
                    <label class="form-label">最大失败次数</label>
                    <div class="input-group">
                      <el-input-number
                        v-model="mqttConfig.max_failed_attempts"
                        :min="1" :max="10" size="small"
                      />
                      <span class="unit">次</span>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="form-label">封禁时长</label>
                    <div class="input-group">
                      <el-input-number
                        v-model="mqttConfig.blacklist_duration"
                        :min="1" :max="168" size="small"
                      />
                      <span class="unit">小时</span>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="form-label">清理间隔</label>
                  <div class="input-group">
                    <el-input-number
                      v-model="mqttConfig.cleanup_interval"
                      :min="1" :max="1440" size="small"
                    />
                    <span class="unit">分钟</span>
                  </div>
                </div>
              </div>

              <!-- 心跳配置 -->
              <div class="config-section">
                <h5 class="section-title">心跳配置</h5>
                <div class="form-grid-2col">
                  <div class="form-group">
                    <label class="form-label">心跳检查间隔</label>
                    <div class="input-group">
                      <el-input-number
                        v-model="mqttConfig.heartbeat_interval"
                        :min="5" :max="300" size="small"
                      />
                      <span class="unit">秒</span>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="form-label">心跳超时时间</label>
                    <div class="input-group">
                      <el-input-number
                        v-model="mqttConfig.heartbeat_timeout"
                        :min="30" :max="600" size="small"
                      />
                      <span class="unit">秒</span>
                    </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="form-label">心跳包间隔</label>
                  <div class="input-group">
                    <el-input-number
                      v-model="mqttConfig.heartbeat_packet_interval"
                      :min="500" :max="60000" size="small"
                    />
                    <span class="unit">毫秒</span>
                  </div>
                </div>

                <!-- 心跳配置说明 -->
                <div class="config-info">
                  <div class="info-item">
                    <span class="info-label">遗嘱触发时间预估:</span>
                    <span class="info-value">最快 {{ mqttConfig.heartbeat_timeout }}秒，最慢 {{ mqttConfig.heartbeat_timeout + mqttConfig.heartbeat_interval }}秒</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 保存按钮 -->
            <div class="config-actions">
              <button class="modern-btn primary large" @click="updateMqttConfig" :disabled="configLoading">
                <i class="fas fa-save"></i>
                {{ configLoading ? '保存中...' : '保存配置' }}
              </button>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 消息发布 -->
      <el-tab-pane label="消息发布" name="publish">
        <div class="publish-grid">
          <!-- 广播消息 -->
          <div class="modern-card">
            <div class="card-header">
              <h4 class="card-title"><i class="fas fa-broadcast-tower"></i> 广播消息</h4>
            </div>
            <div class="card-content">
              <div class="publish-form">
                <div class="form-group">
                  <label class="form-label">主题</label>
                  <el-input v-model="publishForm.topic" placeholder="请输入主题" />
                </div>
                <div class="form-group">
                  <label class="form-label">消息内容</label>
                  <el-input
                    v-model="publishForm.message"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入消息内容"
                  />
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">QoS等级</label>
                    <el-select v-model="publishForm.qos" style="width: 100%">
                      <el-option label="0 - 最多一次" :value="0" />
                      <el-option label="1 - 至少一次" :value="1" />
                      <el-option label="2 - 恰好一次" :value="2" />
                    </el-select>
                  </div>
                  <div class="form-group">
                    <label class="form-label">保持消息</label>
                    <el-switch v-model="publishForm.retain" />
                  </div>
                </div>
                <button class="modern-btn primary" @click="publishMessage" :disabled="publishLoading">
                  <i class="fas fa-paper-plane"></i>
                  {{ publishLoading ? '发布中...' : '发布消息' }}
                </button>
              </div>
            </div>
          </div>

          <!-- 单发消息 -->
          <div class="modern-card">
            <div class="card-header">
              <h4 class="card-title"><i class="fas fa-paper-plane"></i> 单发消息</h4>
            </div>
            <div class="card-content">
              <div class="send-form">
                <div class="form-group">
                  <label class="form-label">目标客户端</label>
                  <el-select
                    v-model="sendForm.clientId"
                    placeholder="选择客户端"
                    filterable
                    clearable
                    style="width: 100%"
                    @change="onClientChange"
                  >
                    <template #prefix>
                      <span class="select-prefix">{{ mqttStats.client_list?.length || 0 }}个在线</span>
                    </template>
                    <el-option
                      v-for="client in mqttStats.client_list"
                      :key="client.id"
                      :value="client.id"
                      :label="`${client.id} ${client.username ? `[${client.username}]` : '[未认证]'}`"
                    />
                  </el-select>
                </div>
                <div class="form-group">
                  <label class="form-label">主题</label>
                  <el-select
                    v-model="sendForm.topic"
                    :placeholder="selectedClientTopics.length ? '选择订阅主题' : '请先选择客户端'"
                    :disabled="!selectedClientTopics.length"
                    filterable
                    clearable
                    allow-create
                    default-first-option
                    style="width: 100%"
                  >
                    <el-option
                      v-for="topic in selectedClientTopics"
                      :key="topic"
                      :value="topic"
                      :label="topic"
                    />
                  </el-select>
                  <div v-if="sendForm.clientId && !selectedClientTopics.length" class="form-help">
                    <span class="help-text">该客户端暂未订阅任何主题，您可以手动输入主题名称</span>
                  </div>
                </div>
                <div class="form-group">
                  <label class="form-label">消息内容</label>
                  <el-input
                    v-model="sendForm.message"
                    type="textarea"
                    :rows="4"
                    placeholder="请输入消息内容"
                  />
                </div>
                <button class="modern-btn primary" @click="sendToClient" :disabled="sendLoading">
                  <i class="fas fa-send"></i>
                  {{ sendLoading ? '发送中...' : '发送消息' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 保持消息 -->
      <el-tab-pane label="保持消息" name="retained">
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-envelope-open"></i> 保持消息管理</h4>
            <div class="header-actions">
              <button class="header-btn refresh" @click="loadRetainedMessages">
                <i class="fas fa-refresh"></i>
                刷新列表
              </button>
              <button class="header-btn danger" @click="clearAllRetainedMessages" :disabled="clearRetainedLoading">
                <i class="fas fa-trash"></i>
                清空所有
              </button>
            </div>
          </div>
          <div v-if="retainedMessages && retainedMessages.length > 0">
            <el-table :data="retainedMessages" border stripe v-loading="retainedLoading" class="standard-table">
              <el-table-column prop="topic" label="主题" min-width="200" />
              <el-table-column prop="message" label="消息内容" min-width="250">
                <template #default="scope">
                  <div class="message-content">
                    {{ scope.row.message }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="qos" label="QoS等级" width="80" />
              <el-table-column prop="timestamp" label="保存时间" width="160">
                <template #default="scope">
                  {{ formatDate(scope.row.timestamp) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="danger"
                    @click="deleteRetainedMessage(scope.row.topic)"
                    :loading="deleteRetainedLoading[scope.row.topic]"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p v-else style="text-align: center; color: #999; padding: 20px;">暂无保持消息</p>
        </div>
      </el-tab-pane>

      <!-- 主题管理 -->
      <el-tab-pane label="主题管理" name="topics">
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-tags"></i> 活跃主题</h4>
            <el-input
              v-model="topicFilter"
              placeholder="搜索主题..."
              prefix-icon="el-icon-search"
              clearable
              class="header-input"
              style="width: 200px;"
              size="small"
            />
          </div>
          <div v-if="processedTopics && processedTopics.length > 0">
            <el-table
              :data="filteredTopics"
              class="topic-table"
              border
              stripe
              size="small"
              :height="'auto'"
              :max-height="350"
            >
              <el-table-column label="主题模式" prop="pattern" min-width="200">
                <template #default="scope">
                  <div class="topic-pattern">
                    <i :class="scope.row.type === 'dynamic' ? 'fas fa-folder' : 'fas fa-comment'"></i>
                    <span>{{ scope.row.pattern }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="类型" prop="type" width="100" align="center">
                <template #default="scope">
                  <el-tag :type="scope.row.type === 'dynamic' ? 'warning' : 'success'" size="small">
                    {{ scope.row.type === 'dynamic' ? '动态主题' : '静态主题' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="活跃数量" prop="count" width="100" align="center">
                <template #default="scope">
                  <span class="topic-count">{{ scope.row.count }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p v-else style="text-align: center; color: #999; padding: 20px;">暂无活跃主题</p>
        </div>
      </el-tab-pane>

      <!-- 遗嘱管理 -->
      <el-tab-pane label="遗嘱管理" name="will">
        <div class="modern-card">
          <div class="card-header">
            <h4 class="card-title"><i class="fas fa-hourglass-half"></i> 遗嘱消息管理</h4>
            <div class="header-actions">
              <button class="header-btn refresh" @click="loadWillMessages">
                <i class="fas fa-refresh"></i>
                刷新列表
              </button>
            </div>
          </div>
          <div v-if="willMessages && willMessages.length > 0">
            <el-table :data="willMessages" border stripe v-loading="willLoading" class="standard-table will-messages-table">
              <el-table-column prop="client_id" label="客户端ID" width="240" class-name="client-id-cell" />
              <el-table-column prop="topic" label="遗嘱主题" min-width="160" class-name="will-topic-cell" />
              <el-table-column prop="message" label="遗嘱消息" min-width="180" class-name="will-message-cell">
                <template #default="scope">
                  <div class="will-message-content">
                    {{ scope.row.message }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="qos" label="QoS" width="60" class-name="will-compact-cell" />
              <el-table-column prop="retain" label="保持" width="70" class-name="will-compact-cell">
                <template #default="scope">
                  <el-tag :type="scope.row.retain ? 'success' : 'info'" size="small">
                    {{ scope.row.retain ? '是' : '否' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="is_connected" label="状态" width="70" class-name="will-compact-cell">
                <template #default="scope">
                  <el-tag :type="scope.row.is_connected ? 'success' : 'danger'" size="small">
                    {{ scope.row.is_connected ? '在线' : '离线' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="created_at" label="创建时间" width="150" class-name="will-compact-cell">
                <template #default="scope">
                  <div class="will-date-cell" :title="formatDate(scope.row.created_at)">
                    {{ formatCompactDate(scope.row.created_at) }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="90" class-name="will-action-cell">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="danger"
                    @click="deleteWillMessage(scope.row.client_id)"
                    :loading="deleteWillLoading[scope.row.client_id]"
                    class="will-action-btn"
                  >
                    清除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p v-else style="text-align: center; color: #999; padding: 20px;">暂无遗嘱消息配置</p>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 心跳监控弹窗 -->
    <el-dialog
      v-model="heartbeatDialog.visible"
      :title="`客户端心跳监控 - ${heartbeatDialog.clientId}`"
      width="600px"
      :before-close="closeHeartbeatMonitor"
    >
      <div class="heartbeat-monitor" :class="{ 'heartbeat-active': heartbeatDialog.isActive }">
        <div class="monitor-header">
          <div class="client-info">
            <i class="fas fa-heartbeat"></i>
            <span>实时心跳监控</span>
          </div>
          <div class="monitor-status">
            <el-tag :type="heartbeatDialog.isActive ? 'success' : 'danger'" size="small">
              {{ heartbeatDialog.isActive ? '活跃' : '离线' }}
            </el-tag>
          </div>
        </div>

        <div class="heartbeat-chart">
          <canvas
            ref="heartbeatCanvas"
            width="550"
            height="200"
            class="heartbeat-canvas"
          ></canvas>
        </div>

        <div class="heartbeat-stats">
          <div class="stat-item">
            <span class="stat-label">心跳次数:</span>
            <span class="stat-value">{{ heartbeatDialog.heartbeatCount }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最后心跳:</span>
            <span class="stat-value">{{ heartbeatDialog.lastHeartbeat || '无' }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">监控时长:</span>
            <span class="stat-value">{{ heartbeatDialog.monitorDuration }}秒</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeHeartbeatMonitor" size="default">
            <i class="fas fa-times"></i>
            关闭
          </el-button>
          <el-button type="primary" @click="clearHeartbeatData" size="default">
            <i class="fas fa-trash"></i>
            清空数据
          </el-button>
          <el-button type="success" @click="testHeartbeat" size="default">
            <i class="fas fa-heartbeat"></i>
            测试心跳
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加黑名单对话框 -->
    <el-dialog
      v-model="showBlacklistDialog"
      title="添加黑名单"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="blacklist-form">
        <div class="form-group">
          <label class="form-label">IP地址</label>
          <el-input v-model="blacklistForm.ip" placeholder="请输入IP地址" />
        </div>
        <div class="form-group">
          <label class="form-label">用户名</label>
          <el-input v-model="blacklistForm.username" placeholder="请输入用户名（可选）" />
        </div>
        <div class="form-group">
          <label class="form-label">封禁原因</label>
          <el-input v-model="blacklistForm.reason" placeholder="请输入封禁原因" />
        </div>
        <div class="form-group">
          <label class="form-label">封禁时长</label>
          <div class="input-group">
            <el-input-number v-model="blacklistForm.hours" :min="1" :max="8760" />
            <span class="unit">小时</span>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBlacklistDialog = false">取消</el-button>
          <el-button type="primary" @click="addToBlacklist">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// MQTT管理状态
const mqttActiveTab = ref('clients')
const mqttStats = reactive({
  running: false,
  clients: 0,
  topics: 0,
  blacklist_count: 0,
  client_list: [],
  topic_list: [],
  blacklist: [],
  failed_auth: []
})

// MQTT配置
const mqttConfig = reactive({
  require_auth: true,
  allow_anonymous: false,
  max_failed_attempts: 3,
  blacklist_duration: 24,
  cleanup_interval: 30,
  heartbeat_interval: 15,
  heartbeat_timeout: 60,
  heartbeat_packet_interval: 1000
})
const configLoading = ref(false)

// 消息发布表单
const publishForm = reactive({
  topic: '',
  message: '',
  qos: 0,
  retain: false
})
const publishLoading = ref(false)

// 单发消息表单
const sendForm = reactive({
  clientId: '',
  topic: '',
  message: '',
  qos: 0
})
const sendLoading = ref(false)

// 黑名单管理
const showBlacklistDialog = ref(false)
const blacklistForm = reactive({
  ip: '',
  username: '',
  reason: '',
  hours: 24
})

// 保持消息管理
const retainedMessages = ref([])
const retainedLoading = ref(false)
const clearRetainedLoading = ref(false)
const deleteRetainedLoading = ref({})

// 遗嘱消息管理
const willMessages = ref([])
const willLoading = ref(false)
const deleteWillLoading = ref({})

// 主题管理
const topicFilter = ref('')
const processedTopics = ref([])

// 心跳监控相关数据
const heartbeatDialog = reactive({
  visible: false,
  clientId: '',
  isActive: false,
  heartbeatCount: 0,
  lastHeartbeat: '',
  monitorDuration: 0,
  startTime: null
})

const heartbeatCanvas = ref(null)
let heartbeatChart = null
let heartbeatData = []
let animationId = null
let monitorTimer = null
let lastActiveTime = null
let eventSource = null // SSE连接

// 计算属性
const selectedClientTopics = computed(() => {
  if (!sendForm.clientId) return []
  const client = mqttStats.client_list?.find(c => c.id === sendForm.clientId)
  return client?.subscribed_topics || []
})

const filteredTopics = computed(() => {
  if (!topicFilter.value) return processedTopics.value
  return processedTopics.value.filter(topic =>
    topic.pattern.toLowerCase().includes(topicFilter.value.toLowerCase())
  )
})

// 方法
const loadMqttStats = async () => {
  try {
    const response = await axios.get('/api/mqtt/stats')
    Object.assign(mqttStats, response.data.stats || {})

    // 同时加载配置
    if (response.data.stats?.config) {
      Object.assign(mqttConfig, response.data.stats.config)
    }

    // 更新处理后的主题列表
    updateProcessedTopics()
  } catch (error) {
    console.error('Failed to load MQTT stats:', error)
    ElMessage.error('加载MQTT状态失败')
  }
}

const updateProcessedTopics = () => {
  processedTopics.value = processTopics(mqttStats.topic_list || [])
}

const processTopics = (rawTopics) => {
  if (!rawTopics || rawTopics.length === 0) {
    return []
  }

  // 分组模式
  const groups = new Map()
  const standalone = []

  rawTopics.forEach(topic => {
    const pattern = extractTopicPattern(topic)
    if (pattern && pattern !== topic && groups.has(pattern)) {
      // 动态主题，加入已有分组
      groups.get(pattern).topics.push(topic)
      groups.get(pattern).count++
    } else if (pattern && pattern !== topic) {
      // 动态主题，创建新分组
      groups.set(pattern, {
        pattern: pattern + '/*',
        type: 'dynamic',
        count: 1,
        topics: [topic]
      })
    } else {
      // 静态主题，单独显示
      standalone.push({
        pattern: topic,
        type: 'static',
        count: 1,
        topics: [topic]
      })
    }
  })

  return [...Array.from(groups.values()), ...standalone]
}

const extractTopicPattern = (topic) => {
  // 识别常见的动态主题模式
  const patterns = [
    /^(account)\/[a-zA-Z0-9_]+$/,  // account/client_id
    /^(ipc\/[^\/]+)\/[a-zA-Z0-9_]+$/,  // ipc/xxx/client_id
    /^([^\/]+)\/[a-zA-Z0-9_]{10,}$/  // prefix/long_id
  ]

  for (const pattern of patterns) {
    const match = topic.match(pattern)
    if (match) {
      return match[1]
    }
  }

  return topic  // 返回原主题表示不是动态主题
}

const formatIpAddress = (ip) => {
  if (!ip) return '-'
  // 如果是IPv6地址，简化显示
  if (ip.includes(':') && ip.includes('::')) {
    return ip
  }
  if (ip.includes(':') && ip.split(':').length > 2) {
    const parts = ip.split(':')
    return `${parts[0]}:${parts[1]}:...:${parts[parts.length - 1]}`
  }
  return ip
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatHeartbeat = (timestamp) => {
  if (!timestamp) return '-'
  const now = Date.now()
  const diff = now - timestamp
  if (diff < 60000) return `${Math.floor(diff / 1000)}s前`
  if (diff < 3600000) return `${Math.floor(diff / 60000)}m前`
  return `${Math.floor(diff / 3600000)}h前`
}

const formatCompactDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const logDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  // 如果是今天，显示今天+时间
  if (logDate.getTime() === today.getTime()) {
    return '今天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 如果是昨天，显示昨天+时间
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
  if (logDate.getTime() === yesterday.getTime()) {
    return '昨天 ' + date.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 其他情况显示完整的年月日时间，格式：MM-DD HH:mm
  return date.toLocaleString('zh-CN', {
    year: '2-digit',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).replace(/\//g, '-').replace(/\s/, ' ')
}

const showHeartbeatMonitor = (clientId) => {
  console.log('Opening heartbeat monitor for client:', clientId)

  heartbeatDialog.visible = true
  heartbeatDialog.clientId = clientId
  heartbeatDialog.isActive = false
  heartbeatDialog.heartbeatCount = 0
  heartbeatDialog.lastHeartbeat = ''
  heartbeatDialog.monitorDuration = 0
  heartbeatDialog.startTime = null

  // 清空之前的数据
  heartbeatData = []
  lastActiveTime = null

  // 初始化画布
  nextTick(() => {
    initHeartbeatChart()
    startHeartbeatMonitoring()
  })
}

const addClientToBlacklist = async (client) => {
  try {
    await ElMessageBox.confirm(
      `确定要将客户端 "${client.id}" (IP: ${formatIpAddress(client.client_ip)}) 加入黑名单吗？`,
      '确认加入黑名单',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await axios.post('/api/mqtt/blacklist', {
      ip: client.client_ip,
      username: client.username || '',
      reason: `客户端 ${client.id} 被管理员手动加入黑名单`,
      hours: 24
    })
    ElMessage.success('客户端已加入黑名单')
    loadMqttStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to add client to blacklist:', error)
      ElMessage.error('加入黑名单失败')
    }
  }
}

const showAddBlacklistDialog = () => {
  blacklistForm.ip = ''
  blacklistForm.username = ''
  blacklistForm.reason = ''
  blacklistForm.hours = 24
  showBlacklistDialog.value = true
}

const removeFromBlacklist = async (ip) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除黑名单 ${ip} 吗？`,
      '确认移除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await axios.delete(`/api/mqtt/blacklist/${ip}`)
    ElMessage.success('黑名单移除成功')
    loadMqttStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to remove blacklist:', error)
      ElMessage.error('移除黑名单失败')
    }
  }
}

const clearFailedAuth = async (ip) => {
  try {
    await ElMessageBox.confirm(
      `确定要清除 ${ip} 的失败认证记录吗？`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await axios.delete(`/api/mqtt/failedauth/${ip}`)
    ElMessage.success('失败认证记录清除成功')
    loadMqttStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to clear failed auth:', error)
      ElMessage.error('清除失败认证记录失败')
    }
  }
}

const updateMqttConfig = async () => {
  configLoading.value = true
  try {
    await axios.put('/api/mqtt/config', mqttConfig)
    ElMessage.success('配置更新成功')
    loadMqttStats()
  } catch (error) {
    console.error('Failed to update MQTT config:', error)
    ElMessage.error('配置更新失败')
  } finally {
    configLoading.value = false
  }
}

const publishMessage = async () => {
  if (!publishForm.topic || !publishForm.message) {
    ElMessage.error('请输入主题和消息内容')
    return
  }

  publishLoading.value = true
  try {
    await axios.post('/api/mqtt/publish', {
      topic: publishForm.topic,
      message: publishForm.message,
      qos: publishForm.qos,
      retain: publishForm.retain
    })
    ElMessage.success('消息发布成功')
    loadMqttStats()
  } catch (error) {
    console.error('Failed to publish message:', error)
    ElMessage.error('消息发布失败')
  } finally {
    publishLoading.value = false
  }
}

const sendToClient = async () => {
  if (!sendForm.clientId || !sendForm.topic || !sendForm.message) {
    ElMessage.error('请选择客户端并输入主题和消息内容')
    return
  }

  sendLoading.value = true
  try {
    await axios.post('/api/mqtt/send', {
      client_id: sendForm.clientId,
      topic: sendForm.topic,
      message: sendForm.message,
      qos: sendForm.qos
    })
    ElMessage.success(`消息已发送到客户端 ${sendForm.clientId}`)
    loadMqttStats()
  } catch (error) {
    console.error('Failed to send message:', error)
    ElMessage.error('消息发送失败')
  } finally {
    sendLoading.value = false
  }
}

const onClientChange = () => {
  // 清空之前选择的主题
  sendForm.topic = ''
}

const addToBlacklist = async () => {
  if (!blacklistForm.ip || !blacklistForm.reason) {
    ElMessage.error('请填写完整的黑名单信息')
    return
  }

  try {
    await axios.post('/api/mqtt/blacklist', {
      ip: blacklistForm.ip,
      username: blacklistForm.username,
      reason: blacklistForm.reason,
      hours: blacklistForm.hours
    })
    ElMessage.success('黑名单添加成功')
    showBlacklistDialog.value = false
    loadMqttStats()
  } catch (error) {
    console.error('Failed to add blacklist:', error)
    ElMessage.error('添加黑名单失败')
  }
}

// 保持消息管理函数
const loadRetainedMessages = async () => {
  retainedLoading.value = true
  try {
    const response = await axios.get('/api/mqtt/retained')
    retainedMessages.value = response.data.messages || []
  } catch (error) {
    console.error('Failed to load retained messages:', error)
    ElMessage.error('加载保持消息失败')
    retainedMessages.value = []
  } finally {
    retainedLoading.value = false
  }
}

const deleteRetainedMessage = async (topic) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除主题 "${topic}" 的保持消息吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    deleteRetainedLoading.value[topic] = true
    await axios.delete('/api/mqtt/retained', {
      data: { topic }
    })
    ElMessage.success('保持消息删除成功')
    loadRetainedMessages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete retained message:', error)
      ElMessage.error('删除保持消息失败')
    }
  } finally {
    delete deleteRetainedLoading.value[topic]
  }
}

const clearAllRetainedMessages = async () => {
  try {
    await ElMessageBox.confirm(
      '⚠️ 警告：这将删除所有保持消息，此操作不可恢复！\n确定要继续吗？',
      '确认清空',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    clearRetainedLoading.value = true
    await axios.delete('/api/mqtt/retained/all')
    ElMessage.success('所有保持消息已清空')
    retainedMessages.value = []
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to clear all retained messages:', error)
      ElMessage.error('清空保持消息失败')
    }
  } finally {
    clearRetainedLoading.value = false
  }
}

// 遗嘱消息管理函数
const loadWillMessages = async () => {
  willLoading.value = true
  try {
    // 先刷新MQTT状态获取最新的客户端列表
    await loadMqttStats()

    const response = await axios.get('/api/mqtt/will')
    willMessages.value = response.data.messages || []

    // 检查每个遗嘱消息对应的客户端是否在线
    if (mqttStats.client_list) {
      const onlineClientIds = new Set(mqttStats.client_list.map(client => client.id))
      willMessages.value = willMessages.value.map(will => ({
        ...will,
        is_connected: onlineClientIds.has(will.client_id)
      }))
    }
  } catch (error) {
    console.error('Failed to load will messages:', error)
    ElMessage.error('加载遗嘱消息失败')
    willMessages.value = []
  } finally {
    willLoading.value = false
  }
}

const deleteWillMessage = async (clientId) => {
  try {
    await ElMessageBox.confirm(
      `确定要清除客户端 "${clientId}" 的遗嘱消息吗？`,
      '确认清除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    deleteWillLoading.value[clientId] = true
    await axios.delete('/api/mqtt/will', {
      data: { client_id: clientId }
    })
    ElMessage.success('遗嘱消息清除成功')
    loadWillMessages()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete will message:', error)
      ElMessage.error('清除遗嘱消息失败')
    }
  } finally {
    delete deleteWillLoading.value[clientId]
  }
}

// 心跳监控相关函数
const closeHeartbeatMonitor = () => {
  heartbeatDialog.visible = false
  stopHeartbeatMonitoring()
}

const clearHeartbeatData = () => {
  heartbeatDialog.heartbeatCount = 0
  heartbeatDialog.lastHeartbeat = ''
  heartbeatData = []
  if (heartbeatChart) {
    drawHeartbeatChart()
  }
}

const testHeartbeat = () => {
  console.log('🧪 Testing heartbeat animation')
  addHeartbeat(1.5) // 添加一个测试心跳，强度稍高
}

const initHeartbeatChart = () => {
  if (!heartbeatCanvas.value) {
    console.error('Canvas element not found!')
    return
  }

  const canvas = heartbeatCanvas.value
  const ctx = canvas.getContext('2d')

  console.log('Initializing heartbeat chart:', canvas.width, 'x', canvas.height)

  heartbeatChart = {
    canvas,
    ctx,
    width: canvas.width,
    height: canvas.height,
    centerY: canvas.height / 2,
    timeOffset: 0
  }

  // 清空之前的动画
  if (animationId) {
    cancelAnimationFrame(animationId)
  }

  // 开始绘制动画
  drawHeartbeatChart()
  console.log('Heartbeat chart animation started')
}

const drawHeartbeatChart = () => {
  // 如果窗口已关闭，停止绘制
  if (!heartbeatChart || !heartbeatDialog.visible) {
    animationId = null
    return
  }

  const { ctx, width, height, centerY } = heartbeatChart

  // 清空画布
  ctx.clearRect(0, 0, width, height)

  // 绘制背景网格
  drawGrid(ctx, width, height)

  // 绘制基线
  ctx.strokeStyle = '#409eff'
  ctx.lineWidth = 2
  ctx.beginPath()
  ctx.moveTo(0, centerY)
  ctx.lineTo(width, centerY)
  ctx.stroke()

  // 绘制心跳波形
  if (heartbeatData.length > 0) {
    drawHeartbeatWave(ctx, width, height, centerY)
  }

  // 恢复60fps流畅动画
  if (heartbeatDialog.visible) {
    animationId = requestAnimationFrame(drawHeartbeatChart)
  }
}

const drawGrid = (ctx, width, height) => {
  ctx.strokeStyle = '#f0f0f0'
  ctx.lineWidth = 1

  // 垂直网格线
  for (let x = 0; x <= width; x += 50) {
    ctx.beginPath()
    ctx.moveTo(x, 0)
    ctx.lineTo(x, height)
    ctx.stroke()
  }

  // 水平网格线
  for (let y = 0; y <= height; y += 25) {
    ctx.beginPath()
    ctx.moveTo(0, y)
    ctx.lineTo(width, y)
    ctx.stroke()
  }
}

const drawHeartbeatWave = (ctx, width, height, centerY) => {
  const now = Date.now()
  const timeWindow = 10000 // 显示最近10秒的数据
  const pixelsPerSecond = width / (timeWindow / 1000)

  if (heartbeatData.length === 0) {
    return // 没有心跳数据
  }

  console.log(`Drawing ${heartbeatData.length} heartbeat points`)

  ctx.strokeStyle = '#67c23a'
  ctx.lineWidth = 3

  // 绘制每个心跳点
  heartbeatData.forEach((heartbeat, index) => {
    const timeDiff = now - heartbeat.timestamp
    if (timeDiff > timeWindow) return // 超出时间窗口

    const x = width - (timeDiff / 1000) * pixelsPerSecond

    if (x < 0 || x > width) return

    // 绘制心跳波形
    const wavePoints = generateHeartbeatWave(x, centerY, heartbeat.intensity)

    ctx.beginPath()
    ctx.moveTo(wavePoints[0].x, wavePoints[0].y)

    for (let i = 1; i < wavePoints.length; i++) {
      ctx.lineTo(wavePoints[i].x, wavePoints[i].y)
    }

    ctx.stroke()
  })
}

const generateHeartbeatWave = (centerX, centerY, intensity = 1) => {
  const amplitude = 60 * intensity
  const width = 30

  return [
    { x: centerX - width, y: centerY },
    { x: centerX - width/2, y: centerY },
    { x: centerX - width/4, y: centerY - amplitude * 0.3 },
    { x: centerX - width/8, y: centerY + amplitude * 0.8 },
    { x: centerX, y: centerY - amplitude },
    { x: centerX + width/8, y: centerY + amplitude * 0.4 },
    { x: centerX + width/4, y: centerY - amplitude * 0.2 },
    { x: centerX + width/2, y: centerY },
    { x: centerX + width, y: centerY }
  ]
}

const startHeartbeatMonitoring = () => {
  console.log('🚀 Starting heartbeat monitoring for:', heartbeatDialog.clientId)

  // 设置监控开始时间
  heartbeatDialog.startTime = Date.now()

  // 重置计数器
  heartbeatDialog.heartbeatCount = 0

  // 建立SSE连接监听心跳事件
  connectToSSE()

  // 更新监控时长的定时器
  monitorTimer = setInterval(() => {
    if (heartbeatDialog.startTime) {
      heartbeatDialog.monitorDuration = Math.floor((Date.now() - heartbeatDialog.startTime) / 1000)
    }

    // 检查客户端活跃状态
    checkClientHeartbeat()
  }, 1000)

  console.log('💡 心跳监控已启动，将监听服务器的 client_heartbeat 事件')
}

const checkClientHeartbeat = () => {
  const currentClient = mqttStats.client_list?.find(client => client.id === heartbeatDialog.clientId)
  if (!currentClient) {
    heartbeatDialog.isActive = false
    return
  }

  // 简单检查客户端活跃状态
  try {
    const lastPingTime = currentClient.last_ping
    const now = Date.now()
    const timeSinceLastPing = now - lastPingTime

    // 更新最后心跳显示时间
    heartbeatDialog.lastHeartbeat = new Date(lastPingTime).toLocaleTimeString()

    // 使用与后端一致的60秒超时时间判断活跃状态
    heartbeatDialog.isActive = timeSinceLastPing < 60000

  } catch (error) {
    console.error('Error checking client heartbeat:', error)
    heartbeatDialog.isActive = false
  }
}

const stopHeartbeatMonitoring = () => {
  if (monitorTimer) {
    clearInterval(monitorTimer)
    monitorTimer = null
  }

  // 清理动画ID
  if (animationId) {
    cancelAnimationFrame(animationId)
    animationId = null
  }

  // 关闭SSE连接
  disconnectFromSSE()

  console.log('Heartbeat monitoring stopped')
}

const addHeartbeat = (intensity = 1) => {
  const now = Date.now()

  console.log('💓 Adding heartbeat to chart, intensity:', intensity)

  heartbeatData.push({
    timestamp: now,
    intensity
  })

  // 只保留最近的心跳数据
  heartbeatData = heartbeatData.filter(h => now - h.timestamp < 10000)

  // 更新统计信息
  heartbeatDialog.heartbeatCount++

  console.log(`Heartbeat count: ${heartbeatDialog.heartbeatCount}, Data points: ${heartbeatData.length}`)
}

// 处理真实的心跳事件
const handleHeartbeatEvent = (eventData) => {
  console.log('💓 服务器心跳事件:', eventData)

  // 更新客户端列表中的 last_ping 时间
  if (mqttStats.client_list && eventData.timestamp && eventData.client_id) {
    const clientIndex = mqttStats.client_list.findIndex(client =>
      client.id === eventData.client_id
    )
    if (clientIndex !== -1) {
      // 更新客户端的最后心跳时间（转换为毫秒）
      mqttStats.client_list[clientIndex].last_ping = eventData.timestamp * 1000
      console.log('🔄 更新客户端心跳时间:', eventData.client_id, new Date(eventData.timestamp * 1000))
    }
  }

  // 只处理当前监控的客户端的心跳
  if (!heartbeatDialog.visible || eventData.client_id !== heartbeatDialog.clientId) {
    return
  }

  // 简单直接：每收到一个服务器心跳事件，就显示一次心跳动画
  addHeartbeat(1)

  console.log(`✅ 心跳计数: ${heartbeatDialog.heartbeatCount}`)
}

// SSE连接管理
const connectToSSE = () => {
  if (eventSource) {
    return // 已经连接
  }

  try {
    eventSource = new EventSource('/api/events')

    eventSource.onopen = () => {
      console.log('✅ SSE连接已建立')
    }

    eventSource.onerror = (error) => {
      console.error('❌ SSE连接错误:', error)
      // 自动重连
      setTimeout(() => {
        if (heartbeatDialog.visible) {
          connectToSSE()
        }
      }, 5000)
    }

    // 监听心跳事件
    eventSource.addEventListener('client_heartbeat', function(event) {
      try {
        const data = JSON.parse(event.data)
        console.log('💓 收到心跳事件:', data)
        handleHeartbeatEvent(data)
      } catch (error) {
        console.error('解析心跳事件失败:', error, 'Raw event:', event)
      }
    })

  } catch (error) {
    console.error('创建SSE连接失败:', error)
  }
}

const disconnectFromSSE = () => {
  if (eventSource) {
    eventSource.close()
    eventSource = null
    console.log('🔌 SSE连接已关闭')
  }
}

// 生命周期
onMounted(() => {
  loadMqttStats()
})

// 监听标签切换
watch(mqttActiveTab, (newTab) => {
  if (newTab === 'clients') {
    loadMqttStats()
  } else if (newTab === 'retained') {
    loadRetainedMessages()
  } else if (newTab === 'will') {
    loadWillMessages()
  } else if (newTab === 'topics') {
    loadMqttStats() // 主题管理需要加载统计信息
  }
})
</script>

<style scoped>
.mqtt-management {
  padding: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.primary { background: #3b82f6; }
.stat-icon.success { background: #10b981; }
.stat-icon.warning { background: #f59e0b; }
.stat-icon.danger { background: #ef4444; }

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.text-success { color: #10b981 !important; }
.text-danger { color: #ef4444 !important; }

.modern-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  overflow: hidden;
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.card-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-content {
  padding: 1.5rem;
}

.modern-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.modern-btn.primary {
  background: #3b82f6;
  color: white;
}

.modern-btn.primary:hover {
  background: #2563eb;
}

.modern-btn.large {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.heartbeat-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.heartbeat-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.heartbeat-monitor-btn {
  background: none;
  border: none;
  color: #3b82f6;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.heartbeat-monitor-btn:hover {
  background: #f3f4f6;
}

.mqtt-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.publish-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.config-form {
  max-width: 800px;
}

.config-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.switch-group {
  display: flex;
  gap: 2rem;
  flex-wrap: wrap;
}

.switch-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-grid-2col {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.unit {
  font-size: 0.875rem;
  color: #6b7280;
}

.config-info {
  margin-top: 1rem;
  padding: 1rem;
  background: #f3f4f6;
  border-radius: 6px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-weight: 500;
  color: #374151;
}

.info-value {
  color: #6b7280;
}

.config-actions {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: center;
}

.publish-form, .send-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.blacklist-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.select-prefix {
  font-size: 0.75rem;
  color: #6b7280;
}

.form-help {
  margin-top: 0.5rem;
}

.help-text {
  font-size: 0.75rem;
  color: #64748b;
  font-style: italic;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.header-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.header-btn.refresh {
  background: #6b7280;
  color: white;
}

.header-btn.refresh:hover {
  background: #4b5563;
}

.header-btn.danger {
  background: #ef4444;
  color: white;
}

.header-btn.danger:hover {
  background: #dc2626;
}

.header-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message-content {
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.topic-pattern {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.topic-count {
  font-weight: 600;
  color: #3b82f6;
}

.header-input {
  width: 200px;
}

.will-messages-table .will-message-content {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.will-date-cell {
  font-size: 0.75rem;
  color: #6b7280;
}

.will-action-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

/* 心跳监控样式 */
.heartbeat-monitor {
  padding: 1rem;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.client-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #1f2937;
}

.client-info i {
  color: #ef4444;
  animation: heartbeat-pulse 1.5s ease-in-out infinite;
}

.heartbeat-active .client-info i {
  color: #10b981;
  animation: heartbeat-active-pulse 1s ease-in-out infinite;
}

@keyframes heartbeat-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes heartbeat-active-pulse {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.2); }
  50% { transform: scale(1); }
  75% { transform: scale(1.1); }
}

.monitor-status {
  display: flex;
  align-items: center;
}

.heartbeat-chart {
  background: #f8fafc;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heartbeat-canvas {
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
}

.heartbeat-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .publish-grid {
    grid-template-columns: 1fr;
  }

  .form-grid-2col {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .switch-group {
    flex-direction: column;
    gap: 1rem;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
}
</style>
