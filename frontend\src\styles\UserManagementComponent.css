.user-management {
  padding: 20px;
}

.modern-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #eef2f7;
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eef2f7;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.card-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modern-search {
  position: relative;
  display: flex;
  align-items: center;
}

.modern-search .fas.fa-search {
  position: absolute;
  left: 12px;
  color: #999;
  z-index: 1;
}

.search-input {
  padding: 8px 12px 8px 36px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
}

.modern-btn.small {
  padding: 6px 12px;
  font-size: 12px;
}

.modern-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modern-btn.success {
  background: #6fcf97;
  color: white;
}

.modern-btn.warning {
  background: #f2c94c;
  color: #333;
}

.modern-btn.secondary {
  background: #909399;
  color: white;
}

.modern-btn.danger {
  background: #f25b5b;
  color: white;
}

.modern-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.modern-btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modern-table-container {
  position: relative;
}

.user-pagination {
  padding: 20px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #eef2f7;
}

:deep(.el-table) {
  border: none;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  font-weight: 600;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-tag) {
  border: none;
}

:deep(.el-table__row:hover) {
  background-color: #f8f9fa;
}

:deep(.el-pagination) {
  padding: 0;
}

.batch-operations {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 15px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .batch-operations {
    flex-wrap: wrap;
  }
}